package weaver.toolbox.hrm.cache;

import com.engine.hrm.cmd.matrix.biz.MatrixMaintComInfo;
import weaver.general.BaseBean;
import weaver.hrm.cachecenter.bean.RolemembersComInfo;
import weaver.hrm.company.DepartmentComInfo;
import weaver.hrm.company.SubCompanyComInfo;
import weaver.hrm.job.JobActivitiesComInfo;
import weaver.hrm.job.JobGroupsComInfo;
import weaver.hrm.job.JobTitlesComInfo;
import weaver.hrm.job.UseKindComInfo;
import weaver.hrm.location.LocationComInfo;
import weaver.hrm.resource.ResourceComInfo;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;
import weaver.systeminfo.systemright.CheckUserRight;

/**
 * 功能说明 清除所有人力资源缓存
 * 
 * <AUTHOR>  
 * @create 2022-10-10
 */
public class RemoveCacheAction extends BaseBean implements Action {
	
	@Override
	public String execute(RequestInfo request) {  
		try {
			ResourceComInfo resourcecominfo           = new ResourceComInfo();
			DepartmentComInfo departmentComInfo       = new DepartmentComInfo();
			SubCompanyComInfo subCompanyComInfo       = new SubCompanyComInfo();
			JobActivitiesComInfo jobActivitiesComInfo = new JobActivitiesComInfo();
			JobTitlesComInfo jobTitlesComInfo         = new JobTitlesComInfo();
			JobGroupsComInfo jobGroupsComInfo         = new JobGroupsComInfo();
			LocationComInfo locationComInfo           = new LocationComInfo();
			UseKindComInfo useKindComInfo             = new UseKindComInfo();
			CheckUserRight checkUserRight             = new CheckUserRight();
			RolemembersComInfo rolemembersComInfo     = new RolemembersComInfo();
			MatrixMaintComInfo matrixMaintComInfo     = new MatrixMaintComInfo();
			resourcecominfo.removeResourceCache();
			departmentComInfo.removeCompanyCache();
			subCompanyComInfo.removeCompanyCache();
			jobActivitiesComInfo.removeJobActivitiesCache();
			jobTitlesComInfo.removeJobTitlesCache();
			jobGroupsComInfo.removeCompanyCache();
			locationComInfo.removeLocationCache();
			useKindComInfo.removeUseKindCache();
			checkUserRight.removeMemberRoleCache();
			checkUserRight.removeRoleRightdetailCache();
			rolemembersComInfo.removeCache();
			matrixMaintComInfo.removeCache();
		} catch (Exception ex) {
			writeLog(ex.getMessage());
			request.getRequestManager().setMessagecontent("流程提交时发生异常，请联系管理员！" + ex.getMessage());
			return Action.FAILURE_AND_CONTINUE;
		}

		return Action.SUCCESS;
	}
}