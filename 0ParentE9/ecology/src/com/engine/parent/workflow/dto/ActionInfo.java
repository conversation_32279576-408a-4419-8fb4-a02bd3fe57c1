package com.engine.parent.workflow.dto;

import lombok.Data;
import lombok.experimental.Accessors;
import weaver.hrm.User;

import java.util.List;
import java.util.Map;

/**
 * @FileName ActionInfo
 * @Description Action信息
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/6/13
 */
@Data
@Accessors(chain = true)//可以使用链式set
public class ActionInfo {
    /**
     * 数据id
     */
    private int id;
    /**
     * 请求id
     */
    private String requestId;
    /**
     * 流程workflowid
     */
    private String workflowId;
    /**
     * 当前操作类型
     * 0 退回
     * 1 提交(节点类型为批准或提交的都是这个)
     * 2 强制收回
     * 3 流程干预
     * 注意：强制归档不会走action
     */
    private int submitType;
    /**
     * 当前操作用户对象
     */
    private User user;
    /**
     * 请求标题
     */
    private String requestName;
    /**
     * 当前用户提交时的签字意见
     */
    private String remark;
    /**
     * 当前节点的id
     * <p>
     * 1、节点后附加操作：当前节点为当前操作节点的id
     * 场景1：创建（挂在节点后）-提交到-审批1 ---- 这里为 创建节点id
     * 场景2：审批1（挂在节点后）-退回到-创建 ---- 这里为 审批1节点id
     * <p>
     * 2、节点前附加操作：表示由哪个节点操作过来的id（其实还是当前节点的意思，因为action成功走完才会流转）
     * 场景1：创建-提交到-审批1（挂在节点前） ---- 这里为 创建节点id
     * 场景2：审批1-退回到-创建（挂在节点前） ---- 这里为 审批1节点id
     */
    private int currentNodeId;
    /**
     * 当前节点的类型
     * 和 currentNodeId 取值场景一直
     * 0：创建，1：批准，2：提交，3：归档
     */
    private String currentNodeType;
    /**
     * 下一个节点的id
     * <p>
     * 1、节点后附加操作：当前节点为当前操作节点的id
     * 场景1：创建（挂在节点后）-提交到-审批1 ---- 这里为0，具体下个节点是什么在这拿不到
     * 场景2：审批1（挂在节点后）-退回到-创建 ---- 这里为0，具体下个节点是什么在这拿不到
     * <p>
     * 2、节点前附加操作：表示即将流转到的节点（即将的意思是，如果action出错，则停留在当前操作的节点）
     * 场景1：创建-提交到-审批1（挂在节点前） ---- 这里为 审批1节点id
     * 场景2：审批1-退回到-创建（挂在节点前） ---- 这里为 创建节点id
     */
    private int nextNodeId;
    /**
     * 下一个节点的类型
     * 和 nextNodeId取值场景一直
     * 0：创建，1：批准，2：提交，3：归档
     */
    private String nextNodeType;
    /**
     * 表单名称
     */
    private String formtableName;
    /**
     * 客户端ip
     */
    private String clientIp;
    /**
     * 主表数据
     */
    private Map<String, String> mainData;
    /**
     * 明细表数据
     * 格式：key：明细表的index，如明细1，则key为1
     * value：对应明细表的明细表字段值合集，和主表结构一致，组成为ArrayList
     */
    private Map<Integer, List<Map<String, String>>> detailData;

}
