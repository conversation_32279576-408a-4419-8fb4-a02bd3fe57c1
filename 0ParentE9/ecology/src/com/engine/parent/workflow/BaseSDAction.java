package com.engine.parent.workflow;

import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.functionlog.bean.SDLog;
import com.engine.sd2.functionlog.util.SDLogUtil;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * 公共二开action继承
 * 可以用一些公共方法
 */
public class BaseSDAction {
    //使用二开log类
    private final static Logger log = LoggerFactory.getLogger(BaseSDAction.class);

    /**
     * 通用ThreadLocal管理器，用于管理子类的私有参数
     * 使用完整的类名作为key，确保不同Action的Param类不会冲突
     */
    private static final Map<String, ThreadLocal<?>> threadLocalParamCustomMap = new ConcurrentHashMap<>();


    /**
     * 当前线程action的公共变量
     */
    public static class BaseParam {
        /**
         * action类的class
         */
        public Class<?> actionClass = null;
        /**
         * action类私有的Param类class
         */
        public Class<?> paramClass = null;
        /**
         * action报错信息，如果不为空，前端流程将报错，拦截流转
         */
        public String actionError = "";
        /**
         * action请求对象
         */
        public RequestInfo requestInfo = null;
        /**
         * action信息类
         */
        public ActionInfo actionInfo = null;
        /**
         * 二开日志对象
         */
        public SDLog sdLog = null;
        /**
         * 二开打印日志工具类
         */
        public SDLogUtil sdLogUtil = new SDLogUtil();
        /**
         * 流程主表信息
         */
        public Map<String, String> wfMainData = null;

        /**
         * 使用ThreadLocal构建线程安全的当前action公共变量参数
         */
        private static final ThreadLocal<BaseParam> LOCAL = ThreadLocal.withInitial(BaseParam::new);

        /**
         * 每个线程第一次调用时会触发 BaseParam::new
         *
         * @return
         */
        public static BaseParam getCurrent() {
            return LOCAL.get();
        }

        /**
         * 清理当前线程的对象，防止内存泄露
         */
        public static void clear() {
            LOCAL.remove();
        }
    }

    /**
     * 获取当前线程的BaseParam实例（使用父类的ThreadLocal管理）
     *
     * @return 当前线程的BaseParam实例
     */
    protected BaseParam getThreadLocalBaseParam() {
        return BaseParam.getCurrent();
    }

    /**
     * 获取指定类型的ThreadLocal参数对象
     *
     * @param actionClass 调用的Action类的Class对象，用于生成唯一key
     * @param paramClass  参数类的Class对象
     * @param supplier    参数对象的构造函数
     * @param <T>         参数类型
     * @return 当前线程的参数对象
     */
    @SuppressWarnings("unchecked")
    protected static <T> T getThreadLocalCustomParam(Class<?> actionClass, Class<T> paramClass, Supplier<T> supplier) {
        // 使用Action类名+Param类名作为唯一key，避免不同Action的Param类冲突
        String key = actionClass.getName() + "#" + paramClass.getSimpleName();
        ThreadLocal<T> threadLocal = (ThreadLocal<T>) threadLocalParamCustomMap.computeIfAbsent(key,
                k -> ThreadLocal.withInitial(supplier));
        return threadLocal.get();
    }

    /**
     * 清理指定Action类型的ThreadLocal参数对象
     *
     * @param actionClass 调用的Action类的Class对象
     * @param paramClass  参数类的Class对象
     */
    protected static void clearThreadLocalCustomParam(Class<?> actionClass, Class<?> paramClass) {
        // 使用Action类名+Param类名作为唯一key，避免不同Action的Param类冲突
        String key = actionClass.getName() + "#" + paramClass.getSimpleName();
        ThreadLocal<?> threadLocal = threadLocalParamCustomMap.get(key);
        if (threadLocal != null) {
            threadLocal.remove();
        }
    }


    /**
     * 初始化，设置公共变量
     * 没有action私有变量
     *
     * @param requestInfo  流程requestInfo
     * @param actionClass  action代码类class，用作二开日志类的功能标识（需要唯一）
     * @param actionClass  action代码类中私有的Param类class，没有私有Param则传null
     * @param functionDesc action功能简述
     */
    public void initAction(RequestInfo requestInfo, Class<?> actionClass, String functionDesc) {
        initAction(requestInfo, actionClass, null, functionDesc);
    }

    /**
     * 初始化，设置公共变量
     * 有action私有变量
     *
     * @param requestInfo  流程requestInfo
     * @param actionClass  action代码类class，用作二开日志类的功能标识（需要唯一）
     * @param actionClass  action代码类中私有的Param类class，没有私有Param则传null
     * @param functionDesc action功能简述
     */
    public void initAction(RequestInfo requestInfo, Class<?> actionClass, Class<?> paramClass, String functionDesc) {
        // 防御性清理ThreadLocal数据，确保每次调用都从干净状态开始
        BaseParam.clear();
        DBUtil.clearThreadLocalRecordSet();
        //清理action私有的Param对象
        if (actionClass != null && paramClass != null) {
            clearThreadLocalCustomParam(actionClass, paramClass);
        }
        //获取当前线程的BaseParam，线程第一次调用会new出来
        BaseParam baseParam = getThreadLocalBaseParam();
        try {
            String functionCode = "";
            String codePath = "";
            if (actionClass != null) {
                functionCode = Util.null2String(actionClass.getSimpleName());
                codePath = Util.null2String(actionClass.getName());
            }
            baseParam.actionClass = actionClass;
            baseParam.paramClass = paramClass;
            baseParam.requestInfo = requestInfo;
            baseParam.actionInfo = ActionUtil.getInfo(requestInfo);
            log.info("二开流程action:" + functionCode + "执行,requestid:" + baseParam.actionInfo.getRequestId() + "---START---");
            baseParam.wfMainData = baseParam.actionInfo.getMainData();
            //初始化二开日志类
            initSDLog(functionCode, codePath, functionDesc);

        } catch (Exception e) {
            baseParam.actionError = "初始化action异常:" + e.getMessage();
            log.error("初始化action异常:", e);
        }
    }

    /**
     * 获取当前action错误信息
     *
     * @return
     */
    public String getActionError() {
        return getThreadLocalBaseParam().actionError;
    }

    /**
     * 初始化二开日志类
     *
     * @param functionCode
     * @param codePath
     * @param functionDesc
     */
    public void initSDLog(String functionCode, String codePath, String functionDesc) {
        BaseParam baseParam = getThreadLocalBaseParam();
        //初始化日志bean
        baseParam.sdLog = new SDLog(baseParam.actionInfo.getUser().getUID(),
                functionCode,
                codePath,
                SDLog.TYPE_ACTION,
                functionDesc);
        baseParam.sdLog.setRelate_module("流程");
        baseParam.sdLog.setRelate_table(baseParam.actionInfo.getFormtableName());
        baseParam.sdLog.setRelate_dataid(baseParam.actionInfo.getRequestId());
    }

    /**
     * 根据当前action的actionError返回成功或者失败
     *
     * @return
     */
    public String actionReturn() {
        BaseParam baseParam = getThreadLocalBaseParam();
        String result;
        if (baseParam.actionError == null || baseParam.actionError.trim().isEmpty()) {
            log.info("Action Return requestid:" + baseParam.requestInfo.getRequestid() + " SUCCESS END");
            // 注意：不清理所有ThreadLocal参数，让每个Action自己管理自己的参数生命周期
            result = Action.SUCCESS;
        } else {
            //流程提交失败信息内容
            //前后增加-，防止json格式内容不显示
            String errorMsg = "-" + baseParam.actionError + "-";
            baseParam.requestInfo.getRequestManager().setMessagecontent(errorMsg);
            log.warn("Action Return requestid:" + baseParam.requestInfo.getRequestid() + " FAIL END");
            result = Action.FAILURE_AND_CONTINUE;
        }

        //action返回前的收尾工作,保存二开日志、清理
        doFinalWork();
        return result;
    }

    /**
     * 返回action成功，无论是否有错误
     *
     * @return
     */
    public String actionReturnAlwaysSuccess() {
        BaseParam baseParam = getThreadLocalBaseParam();
        if (baseParam.actionError == null || baseParam.actionError.trim().isEmpty()) {
            log.info("Action Return requestid:" + baseParam.requestInfo.getRequestid() + " SUCCESS END");
        } else {
            log.warn("Action Return requestid:" + baseParam.requestInfo.getRequestid() + " with error:" + baseParam.actionError + " but SUCCESS END");
        }
        //action返回前的收尾工作,保存二开日志、清理
        doFinalWork();
        return Action.SUCCESS;
    }

    /**
     * action返回前的收尾工作,保存二开日志、清理
     */
    private void doFinalWork() {
        try {
            //保存二开日志
            saveSDLog();
            //清理action私有的Param对象
            if (getThreadLocalBaseParam().actionClass != null && getThreadLocalBaseParam().paramClass != null) {
                clearThreadLocalCustomParam(getThreadLocalBaseParam().actionClass, getThreadLocalBaseParam().paramClass);
            }
            //清除RecordSet和BaseParam
            DBUtil.clearThreadLocalRecordSet();
            BaseParam.clear();

        } catch (Exception e) {
            log.error("doFinalWork error:", e);
        }

    }

    /**
     * 保存二开日志
     */
    public void saveSDLog() {
        BaseParam baseParam = getThreadLocalBaseParam();
        try {
            //插入二开日志
            if (baseParam.sdLog != null) {
                //异步插入日志
                SDLog.saveLogAsync(baseParam.sdLog, baseParam.sdLogUtil.getFullLog(), baseParam.actionError);
            } else {
                log.warn("二开日志对象为null，不插入二开日志");
            }
        } catch (Exception e) {
            log.error("saveSDLog error:", e);
        }
    }

    /**
     * 添加日志
     *
     * @param logMsg
     */
    public void appendLog(String logMsg) {
        BaseParam baseParam = getThreadLocalBaseParam();
        log.info(logMsg);
        baseParam.sdLogUtil.appendLog(logMsg);
    }
}
