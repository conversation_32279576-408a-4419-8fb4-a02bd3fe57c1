package com.engine.demo.gyl.workflow.common.action;


import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.BaseSDAction;
import lombok.Getter;
import lombok.Setter;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.List;
import java.util.Map;

/**
 * @FileName DemoAction.java
 * @Description demo action
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/11/5
 */
@Getter
@Setter
public class DemoAction extends BaseSDAction implements Action {
    //Action参数---START---
    //Action参数---END---


    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    /**
     * 自定义action类私有变量
     * 使用ThreadLocal管理
     */
    private static class Params {
        private String checkResult = "T"; //默认成功 //校验接口  T成功 F失败
    }

    private Params getParam() {
        return getThreadLocalCustomParam(this.getClass(), Params.class, Params::new);
    }

    /**
     * action执行入口
     *
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        //SDAction初始化
        initAction(requestInfo, this.getClass(), "测试demo");
        try {
            //执行业务逻辑
            execuetMy();
            log.info("当前param:" + getParam().checkResult);
            //修改
            getParam().checkResult = "F";
            log.info("修改后当前param:" + getParam().checkResult);
        } catch (Exception e) {
            getThreadLocalBaseParam().actionError = "执行异常：" + e.getMessage();
            log.error("执行异常：", e);
        } finally {
            log.info(this.getClass().getName() + "---END---requestid:" + requestInfo.getRequestid());
        }
        return actionReturn();
    }

    private void execuetMy() {
        try {
            //明细1数据
            List<Map<String, String>> detailData1;
            //主表数据
            Map<String, String> mainData = getThreadLocalBaseParam().actionInfo.getMainData();
            //明细表数据
            Map<Integer, List<Map<String, String>>> mapDetail = getThreadLocalBaseParam().actionInfo.getDetailData();
            if (!mapDetail.isEmpty()) {
                //获取明细1表的数据
                detailData1 = mapDetail.getOrDefault(1, null);
            }
            //TODO
        } catch (Exception e) {
            getThreadLocalBaseParam().actionError = this.getClass().getName() + "异常：" + SDUtil.getExceptionDetail(e);
        }
    }
}
