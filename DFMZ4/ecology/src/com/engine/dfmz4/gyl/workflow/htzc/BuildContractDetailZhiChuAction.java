package com.engine.dfmz4.gyl.workflow.htzc;


import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.BaseSDAction;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * @FileName BuildContractDetailAction.java
 * @Description 合同支出流程，构建流程明细
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/6/30
 */
@Getter
@Setter
public class BuildContractDetailZhiChuAction extends BaseSDAction implements Action {
    //Action参数---START---
    /**
     * 流程主表字段-合同编号
     */
    private String field_htbh;
    //Action参数---END---

    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * action执行入口
     *
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        //SDAction初始化
        initAction(requestInfo, this.getClass(), "构建合同流程明细(支出流程)");
        if (getActionError().isEmpty()) {
            try {
                //校验参数
                checkParams();
                if (getActionError().isEmpty()) {
                    //合同编号
                    String contractCode = Util.null2String(getThreadLocalBaseParam().wfMainData.get(field_htbh));
                    if (contractCode.isEmpty()) {
                        appendLog("合同编号为空,不执行逻辑");
                    } else {
                        //执行业务逻辑
                        //step 1: 构建明细数据
                        buildDetailData();
                        //step 2: 更新主表3个税率
                        if (getActionError().isEmpty()) {
                            updateMainShuilv();
                        }
                    }
                }
            } catch (Exception e) {
                getThreadLocalBaseParam().actionError = "action执行异常:" + SDUtil.getExceptionDetail(e);
                appendLog(getActionError());
                log.error("action执行异常:", e);
            } finally {
                appendLog(this.getClass().getName() + "---END---requestid:" + requestInfo.getRequestid());
                appendLog("action默认走成功，不拦截提交");
            }
        }
        //action结束返回,这里默认走成功，不拦截
        return actionReturnAlwaysSuccess();
    }


    /**
     * 构建明细数据
     */
    private void buildDetailData() {
        String ids;
        int ksxx, kpsl;
        BigDecimal sl, jehs, bhsje;
        try {
            String contractCode = Util.null2String(getThreadLocalBaseParam().wfMainData.get(field_htbh)); //合同编号
            //根据合同查收款计划数据
            String sql = "select " +
                    " a.htbh," +
                    " a.ksxx," +
                    " a.sl as kpsl," +
                    " b.sl," +
                    " sum(a.jhfkje) as jehs," +
                    " STUFF((SELECT ',' + CAST(t2.id AS VARCHAR) FROM uf_fkxx t2 WHERE t2.ksxx = a.ksxx AND t2.sl = a.sl AND t2.htbh = a.htbh FOR XML PATH('')), 1, 1, '') as ids" +
                    " from uf_fkxx a " +
                    " left join uf_kpsl b on (a.sl = b.id and b.sfqy = 0) " +
                    " where a.htbh = ? " +
                    " group by a.htbh, a.ksxx, a.sl, b.sl " +
                    " order by a.htbh, a.ksxx, a.sl, b.sl ";
            appendLog("获取付款台账汇总sql:" + sql + ";参数:" + contractCode);
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            if (rs.executeQuery(sql, contractCode)) {
                // 获取流程明细表名和主表id
                String detailTable = getThreadLocalBaseParam().actionInfo.getFormtableName() + "_dt3";
                int mainid = getThreadLocalBaseParam().actionInfo.getId();
                while (rs.next()) {
                    ksxx = rs.getInt("ksxx");//客商信息(客户名称)
                    kpsl = rs.getInt("kpsl"); //税率台账数据id
                    sl = SDUtil.getBigDecimalValue(rs.getString("sl")); //税率值
                    jehs = SDUtil.getBigDecimalValue(rs.getString("jehs")); //金额（含税）
                    ids = Util.null2String(rs.getString("ids"));//关联业务履约进度
                    // 不含税金额 = 含税金额 / (1 + 税率)
                    bhsje = BigDecimal.ZERO;
                    if (sl != null && jehs != null) {
                        bhsje = jehs.divide(BigDecimal.ONE.add(sl), 2, RoundingMode.HALF_UP);
                    }

                    // 构建insert语句
                    String insertSql = "insert into " + detailTable + " (mainid, khmc, je, sl, jebhs, glywlyjd) values (?, ?, ?, ?, ?, ?)";
                    RecordSet rsInsert = DBUtil.getThreadLocalRecordSet();
                    List<Object> params = new ArrayList<>();
                    params.add(mainid);
                    params.add(ksxx);
                    params.add(jehs);
                    params.add(kpsl);
                    params.add(bhsje);
                    params.add(ids);
                    appendLog("插入明细sql:" + insertSql + ",参数：" + params);
                    boolean insertFlag = rsInsert.executeUpdate(insertSql, params);
                    if (!insertFlag) {
                        appendLog("插入明细数据失败:" + rsInsert.getExceptionMsg());
                    } else {
                        appendLog("插入明细数据成功!");
                    }
                }
            } else {
                getThreadLocalBaseParam().actionError = "查询付款台账汇总sql出错:" + rs.getExceptionMsg();
                appendLog(getActionError());
            }
        } catch (Exception e) {
            getThreadLocalBaseParam().actionError = "构建收支明细异常：" + SDUtil.getExceptionDetail(e);
            appendLog(getActionError());
        }
    }

    /**
     * 更新主表3个税率
     */
    private void updateMainShuilv() {
        int kpsl;
        BigDecimal skjehs;
        try {
            String contractCode = Util.null2String(getThreadLocalBaseParam().wfMainData.get(field_htbh)); //合同编号
            //根据合同查收款计划数据
            String sql = "select " +
                    " a.htbh," +
                    " a.sl as kpsl," +
                    " sum(a.jhfkje) as jehs " +
                    " from uf_fkxx a " +
                    " where a.htbh = ? " +
                    " group by a.htbh, a.sl " +
                    " order by a.htbh, a.sl ";
            appendLog("获取付款台账汇总sql:" + sql + ";参数:" + contractCode);
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            if (rs.executeQuery(sql, contractCode)) {
                List<Integer> slList = new ArrayList<>();
                List<BigDecimal> slJeList = new ArrayList<>();

                while (rs.next()) {
                    kpsl = rs.getInt("kpsl"); //税率台账数据id
                    skjehs = SDUtil.getBigDecimalValue(rs.getString("jehs")); //款金额（含税）
                    // 收集前3个税率和金额
                    if (kpsl > 0 && slList.size() < 3) {
                        slList.add(kpsl);
                        slJeList.add(skjehs);
                    }
                }
                if (slList.isEmpty()) {
                    appendLog("未查询到任何税率信息，不执行更新主表税率");
                } else {
                    // 补齐3个税率和金额，不足的用null
                    while (slList.size() < 3) {
                        slList.add(null);
                        slJeList.add(null);
                    }
                    List<Object> params = new ArrayList<>();
                    params.add(slList.get(0));
                    params.add(slList.get(1));
                    params.add(slList.get(2));
                    params.add(slJeList.get(0));
                    params.add(slJeList.get(1));
                    params.add(slJeList.get(2));
                    params.add(getThreadLocalBaseParam().actionInfo.getId());
                    String updateSql = "update " + getThreadLocalBaseParam().actionInfo.getFormtableName() + " set sl1=?,sl2=?,sl3=?,sl1je=?,sl2je=?,sl3je=? where id=?";
                    appendLog("更新主表税率sql:" + updateSql);
                    appendLog("更新主表税率参数:" + params);
                    if (rs.executeUpdate(updateSql, params)) {
                        appendLog("更新主表税率成功！");
                    } else {
                        appendLog("更新主表税率失败:" + rs.getExceptionMsg());
                    }
                }
            } else {
                appendLog("查询主表税率sql失败:" + rs.getExceptionMsg());
            }
        } catch (Exception e) {
            getThreadLocalBaseParam().actionError = "更新主表税率异常：" + SDUtil.getExceptionDetail(e);
            appendLog(getActionError());
        }
    }

    /**
     * 校验参数
     *
     * @return
     */
    private void checkParams() {
        if (StringUtils.isBlank(field_htbh)) {
            getThreadLocalBaseParam().actionError = "缺失action参数";
            appendLog(getActionError());
        }
    }

}
