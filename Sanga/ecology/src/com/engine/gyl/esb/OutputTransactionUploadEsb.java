package com.engine.gyl.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.util.HttpUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import weaver.general.Util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * @FileName OutputTransactionUploadEsb.java
 * @Description 百望开票上传接口 ESB
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/12/19
 */
public class OutputTransactionUploadEsb {
    /**
     * 默认执行方法
     *
     * @param params
     * @return
     */
    public Map<String, Object> execute(Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {
            //链接资源 https://openapi.baiwang.com/router
            String urlSource = Util.null2String(params.get("urlSource"));
            String method = Util.null2String(params.get("method"));
            String version = Util.null2String(params.get("version"));
            String appKey = Util.null2String(params.get("appKey"));
            String appSecret = Util.null2String(params.get("appSecret"));
            String timestamp = Util.null2String(params.get("timestamp"));
            String token = Util.null2String(params.get("token"));
            String syncType = Util.null2String(params.get("syncType"));
            String requestId = Util.null2String(params.get("requestId"));
            String body = Util.null2String(params.get("body"));

            //转换body
            JSONObject bodyObj = JSONObject.parseObject(body);
            if (bodyObj != null) {
                //签名
                String sign = getSign(appSecret, method, version, appKey, timestamp, token, syncType, body);
                if (sign.isEmpty()) {
                    errorMsg = "未成功获取到签名";
                } else {
                    String requestUrl = urlSource + "/rest?method=" + method +
                            "&version=" + version +
                            "&appKey=" + appKey +
                            "&format=json" +
                            "&timestamp=" + timestamp +
                            "&token=" + token +
                            "&type=" + syncType +
                            "&requestId=" + requestId +
                            "&sign=" + sign;
                    result.put("sign", sign);
                    result.put("requestUrl", requestUrl);
                    RestResult restResult = HttpUtil.postData(requestUrl, body);
                    if (restResult.isSuccess()) {
                        RestResult.ResponseInfo responseInfo = restResult.getResponseInfo();
                        String responseInfoStr = JSON.toJSONString(responseInfo);
                        result.put("response", responseInfoStr);
                        JSONObject responseBody = JSONObject.parseObject(responseInfo.getBody());
                        result.put("responseBody", responseBody.toJSONString());
                        if ("false".equals(Util.null2String(responseBody.get("success")))) {
                            errorMsg = responseBody.toJSONString();
                        }
                    } else {
                        errorMsg = restResult.getMsg();
                    }
                }
            } else {
                errorMsg = "请求参数body为空";
            }
        } catch (Exception e) {
            errorMsg = "执行异常：" + SDUtil.getExceptionDetail(e);
        }
        result.put("errorMsg", errorMsg);
        result.put("success", errorMsg.isEmpty());
        return result;

    }

    /**
     * 获取签名
     *
     * @param appSecret
     * @param method
     * @param version
     * @param appKey
     * @param timestamp
     * @param token
     * @param syncType
     * @param body
     * @return
     */
    private String getSign(String appSecret,
                           String method,
                           String version,
                           String appKey,
                           String timestamp,
                           String token,
                           String syncType,
                           String body
    ) {

        Map<String, String> urlParams = new HashMap<>();

        // 添加协议级请求参数
        urlParams.put("method", method);
        urlParams.put("version", version);
        urlParams.put("appKey", appKey);
        urlParams.put("format", "json");
        urlParams.put("timestamp", timestamp);
        urlParams.put("token", token);
        urlParams.put("type", syncType);
        try {
            return signTopRequest(urlParams, appSecret, body);  //签名参数
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 给TOP请求签名。
     *
     * @param params 所有字符型的TOP请求参数
     * @param secret 签名密钥
     * @return 签名
     * @throws Exception
     */
    private String signTopRequest(Map<String, String> params,
                                  String secret,
                                  String body) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        // 第一步：检查参数是否已经排序
        ArrayList<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        // 第二步：把所有参数名和参数值串在一起
        StringBuilder query = new StringBuilder();
        query.append(secret);
        for (String key : keys) {
            String value = params.get(key);
            if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
                query.append(key).append(value);
            }
        }

        JsonNode node = mapper.readTree(body);
        body = mapper.writeValueAsString(node);
        query.append(body);

        query.append(secret);
        // 第三步：使用MD5加密
        byte[] bytes;
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        if (md5 != null) {
            bytes = md5.digest(query.toString().getBytes(StandardCharsets.UTF_8));
            // 第四步：把二进制转化为大写的十六进制
            StringBuilder sign = new StringBuilder();
            for (byte b : bytes) {
                String hex = Integer.toHexString(b & 0xFF);
                if (hex.length() == 1) {
                    sign.append("0");
                }
                sign.append(hex.toUpperCase());
            }
            return sign.toString();
        }
        return "";
    }

}
