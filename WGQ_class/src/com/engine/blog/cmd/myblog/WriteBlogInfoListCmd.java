/*    */ package com.engine.blog.cmd.myblog;
/*    */ 
/*    */ import com.cloudstore.eccom.constant.WeaBoolAttr;
/*    */ import com.cloudstore.eccom.pc.table.WeaTable;
/*    */ import com.cloudstore.eccom.pc.table.WeaTableColumn;
/*    */ import com.cloudstore.eccom.result.WeaResultMsg;
/*    */ import com.engine.core.interceptor.AbstractCommand;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.general.PageIdConst;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WriteBlogInfoListCmd
/*    */   extends AbstractCommand<Map<String, Object>>
/*    */ {
/*    */   public WriteBlogInfoListCmd(User paramUser, Map<String, Object> paramMap) {
/* 25 */     this.user = paramUser;
/* 26 */     this.params = paramMap;
/*    */   }
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 30 */     String str1 = (String)this.params.get("startDate");
/* 31 */     String str2 = (String)this.params.get("endDate");
/* 32 */     String str3 = null;
/* 33 */     if ("".equals(Util.null2String(this.params.get("hrmid")))) {
/* 34 */       str3 = this.user.getUID() + "";
/*    */     } else {
/* 36 */       str3 = (String)this.params.get("hrmid");
/*    */     } 
/* 38 */     StringBuilder stringBuilder = new StringBuilder(" where userid=");
/* 39 */     stringBuilder.append(str3);
/* 40 */     stringBuilder.append(" and createdate>=");
/* 41 */     stringBuilder.append("'");
/* 42 */     stringBuilder.append(str1);
/* 43 */     stringBuilder.append("'");
/* 44 */     stringBuilder.append(" and  createdate<= ");
/* 45 */     stringBuilder.append("'");
/* 46 */     stringBuilder.append(str2);
/* 47 */     stringBuilder.append("'");
/* 48 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     try {
/* 50 */       String str = "d15a02c7-17e4-be3c-34ca-423b84e58916";
/* 51 */       WeaTable weaTable = new WeaTable();
/* 52 */       weaTable.setPageUID(str + "_" + this.user.getUID());
/* 53 */       weaTable.setPageID(str);
/* 54 */       weaTable.setPagesize(PageIdConst.getPageSize(str, this.user.getUID()));
/* 55 */       weaTable.setBackfields(" id,userid,createdate,workdate,'1' as operationType ");
/* 56 */       weaTable.setSqlform(" blog_discuss ");
/* 57 */       weaTable.setSqlprimarykey("id");
/* 58 */       weaTable.setSqlwhere(stringBuilder.toString());
/* 59 */       weaTable.setCheckboxList(null);
/* 60 */       weaTable.setCheckboxpopedom(null);
/* 61 */       weaTable.setSqlorderby("id");
/* 62 */       weaTable.getColumns().addAll(tableFields());
/* 63 */       WeaResultMsg weaResultMsg = new WeaResultMsg(false);
/* 64 */       weaResultMsg.putAll(weaTable.makeDataResult());
/* 65 */       weaResultMsg.success();
/* 66 */       hashMap.putAll(weaResultMsg.getResultMap());
/* 67 */     } catch (Exception exception) {
/* 68 */       exception.printStackTrace();
/* 69 */       hashMap.put("api_status", Boolean.valueOf(false));
/*    */     } 
/* 71 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   private List<WeaTableColumn> tableFields() {
/* 75 */     final String otherParaobj = "column:userid";
/* 76 */     return new ArrayList<WeaTableColumn>() {
/*    */       
/*    */       };
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/blog/cmd/myblog/WriteBlogInfoListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */