/*    */ package com.engine.blog.cmd.myblog;
/*    */ 
/*    */ import com.engine.blog.util.BlogCommonUtil;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.weaver.general.Util;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class BlogAddSpecialAttCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   private User user;
/*    */   private Map<String, Object> params;
/*    */   
/*    */   public BlogAddSpecialAttCmd(User paramUser, Map<String, Object> paramMap) {
/* 28 */     this.user = paramUser;
/* 29 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 36 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 41 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 42 */     String str1 = (String)this.params.get("specids");
/* 43 */     String str2 = str1;
/* 44 */     boolean bool = false;
/* 45 */     bool = BlogCommonUtil.addSpecialUser(this.user.getUID(), Util.getIntValue(str2));
/* 46 */     hashMap.put("userid", str2);
/* 47 */     hashMap.put("status", Boolean.valueOf(bool));
/* 48 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/blog/cmd/myblog/BlogAddSpecialAttCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */