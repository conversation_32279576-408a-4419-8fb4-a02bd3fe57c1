/*     */ package com.engine.blog.cmd.shareset;
/*     */ 
/*     */ import com.api.browser.bean.BrowserBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BlogShareSetEditConditionCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private User user;
/*     */   private Map<String, Object> params;
/*     */   private String cUserId;
/*  35 */   private int languageid = 7;
/*     */   
/*     */   public BlogShareSetEditConditionCmd(User paramUser, Map<String, Object> paramMap) {
/*  38 */     this.user = paramUser;
/*  39 */     this.params = paramMap;
/*  40 */     if (paramUser != null) {
/*  41 */       this.cUserId = String.valueOf(paramUser.getUID());
/*  42 */       this.languageid = paramUser.getLanguage();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  48 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  49 */     String str1 = Util.null2String(this.params.get("shareId"));
/*     */     
/*  51 */     RecordSet recordSet = new RecordSet();
/*  52 */     recordSet.executeQuery("select * from blog_share where id=" + str1, new Object[0]);
/*  53 */     String str2 = "";
/*  54 */     String str3 = "";
/*  55 */     String str4 = "";
/*  56 */     String str5 = "";
/*  57 */     int i = 0;
/*  58 */     int j = 0;
/*  59 */     int k = 0;
/*  60 */     int m = 0;
/*  61 */     String str6 = "";
/*  62 */     int n = 0;
/*  63 */     if (recordSet.next()) {
/*  64 */       str2 = Util.null2String(recordSet.getString("type"));
/*  65 */       str3 = Util.null2String(recordSet.getString("content"));
/*  66 */       str4 = Util.null2String(recordSet.getString("canViewMinTime"));
/*  67 */       str5 = Util.null2String(recordSet.getString("canViewMaxTime"));
/*  68 */       i = recordSet.getInt("seclevel");
/*  69 */       j = recordSet.getInt("seclevelmax");
/*  70 */       k = recordSet.getInt("rolelevel");
/*  71 */       m = recordSet.getInt("jobtitlelevel");
/*  72 */       str6 = Util.null2String(recordSet.getString("jobtitlescopeid"));
/*  73 */       n = recordSet.getInt("containlower");
/*     */     } 
/*     */     
/*  76 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  77 */     if ("1".equals(str2)) {
/*     */       
/*  79 */       SearchConditionItem searchConditionItem1 = new SearchConditionItem();
/*     */       
/*  81 */       ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*  82 */       arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(179, this.languageid), true));
/*  83 */       searchConditionItem1 = new SearchConditionItem(ConditionType.SELECT, SystemEnv.getHtmlLabelName(63, this.languageid), "", new String[] { "shareType" }, arrayList1, 7, 17, null);
/*  84 */       searchConditionItem1.setViewAttr(1);
/*     */ 
/*     */       
/*  87 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  88 */       ArrayList<ArrayList<SearchConditionItem>> arrayList2 = new ArrayList();
/*  89 */       ArrayList<SearchConditionItem> arrayList3 = new ArrayList();
/*     */ 
/*     */       
/*  92 */       ArrayList<SearchConditionItem> arrayList4 = new ArrayList();
/*     */ 
/*     */       
/*  95 */       BrowserBean browserBean = new BrowserBean("17");
/*  96 */       browserBean.setViewAttr(3);
/*  97 */       if (!"".equals(str3)) {
/*     */         try {
/*  99 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 100 */           ArrayList<HashMap<Object, Object>> arrayList6 = new ArrayList();
/*     */           
/* 102 */           String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 103 */           for (String str : arrayOfString) {
/* 104 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 105 */             hashMap2.put("id", str);
/* 106 */             hashMap2.put("name", resourceComInfo.getLastname(str));
/* 107 */             arrayList6.add(hashMap2);
/*     */           } 
/*     */           
/* 110 */           browserBean.setReplaceDatas(arrayList6);
/* 111 */         } catch (Exception exception) {
/* 112 */           writeLog(exception.getMessage());
/*     */         } 
/*     */       }
/* 115 */       arrayList4.add(new SearchConditionItem(ConditionType.BROWSER, SystemEnv.getHtmlLabelName(106, this.languageid), "", new String[] { "shareValue" }, null, 7, 17, browserBean));
/* 116 */       SearchConditionItem searchConditionItem2 = new SearchConditionItem(ConditionType.RANGEPICKER, SystemEnv.getHtmlLabelName(509722, this.languageid), "", new String[] { "canViewMinTime", "canViewMaxTime" }, null, 7, 17, true);
/*     */       
/* 118 */       ArrayList<String> arrayList5 = new ArrayList();
/*     */       
/* 120 */       if ("-1".equals(str4)) {
/* 121 */         str4 = "";
/*     */       }
/* 123 */       if ("-1".equals(str5)) {
/* 124 */         str5 = "";
/*     */       }
/* 126 */       arrayList5.add(str4);
/* 127 */       arrayList5.add(str5);
/* 128 */       searchConditionItem2.setValue(arrayList5);
/* 129 */       arrayList3.add(searchConditionItem2);
/* 130 */       arrayList2.add(arrayList4);
/* 131 */       arrayList2.add(arrayList3);
/*     */       
/* 133 */       hashMap1.put("1", arrayList2);
/*     */       
/* 135 */       arrayList.add(searchConditionItem1);
/* 136 */       arrayList.add(hashMap1);
/*     */     }
/* 138 */     else if ("2".equals(str2)) {
/*     */       
/* 140 */       SearchConditionItem searchConditionItem1 = new SearchConditionItem();
/*     */       
/* 142 */       ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 143 */       arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(141, this.languageid), true));
/* 144 */       searchConditionItem1 = new SearchConditionItem(ConditionType.SELECT, SystemEnv.getHtmlLabelName(63, this.languageid), "", new String[] { "shareType" }, arrayList1, 7, 17, null);
/* 145 */       searchConditionItem1.setViewAttr(1);
/*     */ 
/*     */       
/* 148 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */       
/* 150 */       ArrayList<ArrayList<SearchConditionItem>> arrayList2 = new ArrayList();
/* 151 */       ArrayList<SearchConditionItem> arrayList3 = new ArrayList();
/* 152 */       ArrayList<SearchConditionItem> arrayList4 = new ArrayList();
/*     */       
/* 154 */       ArrayList<SearchConditionItem> arrayList5 = new ArrayList();
/* 155 */       BrowserBean browserBean = new BrowserBean("194");
/* 156 */       browserBean.setViewAttr(3);
/* 157 */       if (!"".equals(str3)) {
/*     */         try {
/* 159 */           SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 160 */           ArrayList<HashMap<Object, Object>> arrayList7 = new ArrayList();
/* 161 */           String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 162 */           for (String str : arrayOfString) {
/* 163 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 164 */             hashMap2.put("id", str);
/* 165 */             hashMap2.put("name", subCompanyComInfo.getSubCompanyname(str));
/* 166 */             arrayList7.add(hashMap2);
/*     */           } 
/* 168 */           browserBean.setReplaceDatas(arrayList7);
/* 169 */         } catch (Exception exception) {
/* 170 */           writeLog(exception.getMessage());
/*     */         } 
/*     */       }
/* 173 */       arrayList4.add(new SearchConditionItem(ConditionType.BROWSER, SystemEnv.getHtmlLabelName(106, this.languageid), "", new String[] { "shareValue" }, null, 7, 17, browserBean));
/*     */       
/* 175 */       SearchConditionItem searchConditionItem2 = new SearchConditionItem(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(125963, Util.getIntValue(this.languageid)), new String[] { "containLower" });
/* 176 */       if (n == 0) {
/* 177 */         searchConditionItem2.setValue("0");
/*     */       } else {
/* 179 */         searchConditionItem2.setValue("1");
/*     */       } 
/*     */       
/* 182 */       arrayList4.add(searchConditionItem2);
/* 183 */       SearchConditionItem searchConditionItem3 = new SearchConditionItem(ConditionType.INPUT_INTERVAL, SystemEnv.getHtmlLabelName(683, this.languageid), "", new String[] { "secLevel", "secLevelMax" }, null, 7, 17, null);
/* 184 */       searchConditionItem3.setValue(new int[] { i, j });
/* 185 */       searchConditionItem3.setMax("100");
/* 186 */       searchConditionItem3.setMin("-100");
/* 187 */       arrayList5.add(searchConditionItem3);
/*     */ 
/*     */       
/* 190 */       SearchConditionItem searchConditionItem4 = new SearchConditionItem(ConditionType.RANGEPICKER, SystemEnv.getHtmlLabelName(509722, this.languageid), "", new String[] { "canViewMinTime", "canViewMaxTime" }, null, 7, 17, true);
/*     */       
/* 192 */       ArrayList<String> arrayList6 = new ArrayList();
/*     */       
/* 194 */       if ("-1".equals(str4)) {
/* 195 */         str4 = "";
/*     */       }
/* 197 */       if ("-1".equals(str5)) {
/* 198 */         str5 = "";
/*     */       }
/* 200 */       arrayList6.add(str4);
/* 201 */       arrayList6.add(str5);
/* 202 */       searchConditionItem4.setValue(arrayList6);
/*     */       
/* 204 */       arrayList3.add(searchConditionItem4);
/* 205 */       arrayList2.add(arrayList4);
/* 206 */       arrayList2.add(arrayList5);
/* 207 */       arrayList2.add(arrayList3);
/* 208 */       hashMap1.put("2", arrayList2);
/*     */       
/* 210 */       arrayList.add(searchConditionItem1);
/* 211 */       arrayList.add(hashMap1);
/*     */     }
/* 213 */     else if ("3".equals(str2)) {
/*     */       
/* 215 */       SearchConditionItem searchConditionItem1 = new SearchConditionItem();
/*     */       
/* 217 */       ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 218 */       arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(124, this.languageid), true));
/* 219 */       searchConditionItem1 = new SearchConditionItem(ConditionType.SELECT, SystemEnv.getHtmlLabelName(63, this.languageid), "", new String[] { "shareType" }, arrayList1, 7, 17, null);
/* 220 */       searchConditionItem1.setViewAttr(1);
/* 221 */       ArrayList<SearchConditionItem> arrayList2 = new ArrayList();
/*     */ 
/*     */       
/* 224 */       BrowserBean browserBean = new BrowserBean("57");
/* 225 */       browserBean.setViewAttr(3);
/* 226 */       if (!"".equals(str3)) {
/*     */         try {
/* 228 */           DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 229 */           ArrayList<HashMap<Object, Object>> arrayList7 = new ArrayList();
/*     */           
/* 231 */           String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 232 */           for (String str : arrayOfString) {
/* 233 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 234 */             hashMap2.put("id", str);
/* 235 */             hashMap2.put("name", departmentComInfo.getDepartmentname(str));
/* 236 */             arrayList7.add(hashMap2);
/*     */           } 
/* 238 */           browserBean.setReplaceDatas(arrayList7);
/* 239 */         } catch (Exception exception) {
/* 240 */           writeLog(exception.getMessage());
/*     */         } 
/*     */       }
/* 243 */       arrayList2.add(new SearchConditionItem(ConditionType.BROWSER, SystemEnv.getHtmlLabelName(106, this.languageid), "", new String[] { "shareValue" }, null, 7, 17, browserBean));
/* 244 */       SearchConditionItem searchConditionItem2 = new SearchConditionItem(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(125963, Util.getIntValue(this.languageid)), new String[] { "containLower" });
/* 245 */       if (n == 0) {
/* 246 */         searchConditionItem2.setValue("0");
/*     */       } else {
/* 248 */         searchConditionItem2.setValue("1");
/*     */       } 
/* 250 */       arrayList2.add(searchConditionItem2);
/* 251 */       ArrayList<SearchConditionItem> arrayList3 = new ArrayList();
/* 252 */       SearchConditionItem searchConditionItem3 = new SearchConditionItem(ConditionType.INPUT_INTERVAL, SystemEnv.getHtmlLabelName(683, this.languageid), "", new String[] { "secLevel", "secLevelMax" }, null, 7, 17, null);
/* 253 */       searchConditionItem3.setValue(new int[] { i, j });
/* 254 */       searchConditionItem3.setMax("100");
/* 255 */       searchConditionItem3.setMin("-100");
/* 256 */       arrayList3.add(searchConditionItem3);
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 261 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */       
/* 263 */       ArrayList<ArrayList<SearchConditionItem>> arrayList4 = new ArrayList();
/* 264 */       ArrayList<SearchConditionItem> arrayList5 = new ArrayList();
/* 265 */       SearchConditionItem searchConditionItem4 = new SearchConditionItem(ConditionType.RANGEPICKER, SystemEnv.getHtmlLabelName(509722, this.languageid), "", new String[] { "canViewMinTime", "canViewMaxTime" }, null, 7, 17, true);
/*     */       
/* 267 */       ArrayList<String> arrayList6 = new ArrayList();
/*     */       
/* 269 */       if ("-1".equals(str4)) {
/* 270 */         str4 = "";
/*     */       }
/* 272 */       if ("-1".equals(str5)) {
/* 273 */         str5 = "";
/*     */       }
/* 275 */       arrayList6.add(str4);
/* 276 */       arrayList6.add(str5);
/* 277 */       searchConditionItem4.setValue(arrayList6);
/* 278 */       arrayList4.add(arrayList2);
/* 279 */       arrayList4.add(arrayList3);
/* 280 */       arrayList5.add(searchConditionItem4);
/* 281 */       arrayList4.add(arrayList5);
/*     */       
/* 283 */       hashMap1.put("3", arrayList4);
/*     */       
/* 285 */       arrayList.add(searchConditionItem1);
/* 286 */       arrayList.add(hashMap1);
/*     */     }
/* 288 */     else if ("4".equals(str2)) {
/*     */       
/* 290 */       SearchConditionItem searchConditionItem1 = new SearchConditionItem();
/*     */       
/* 292 */       ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 293 */       arrayList1.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(122, this.languageid), true));
/* 294 */       searchConditionItem1 = new SearchConditionItem(ConditionType.SELECT, SystemEnv.getHtmlLabelName(63, this.languageid), "", new String[] { "shareType" }, arrayList1, 7, 17, null);
/* 295 */       searchConditionItem1.setViewAttr(1);
/*     */ 
/*     */       
/* 298 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */       
/* 300 */       ArrayList<ArrayList<SearchConditionItem>> arrayList2 = new ArrayList();
/*     */ 
/*     */       
/* 303 */       ArrayList<SearchConditionItem> arrayList3 = new ArrayList();
/* 304 */       BrowserBean browserBean = new BrowserBean("65");
/* 305 */       browserBean.setViewAttr(3);
/* 306 */       if (!"".equals(str3)) {
/*     */         try {
/* 308 */           RolesComInfo rolesComInfo = new RolesComInfo();
/* 309 */           ArrayList<HashMap<Object, Object>> arrayList8 = new ArrayList();
/*     */           
/* 311 */           String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 312 */           for (String str : arrayOfString) {
/* 313 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 314 */             hashMap2.put("id", str);
/* 315 */             hashMap2.put("name", rolesComInfo.getRolesname(str));
/* 316 */             arrayList8.add(hashMap2);
/*     */           } 
/* 318 */           browserBean.setReplaceDatas(arrayList8);
/* 319 */         } catch (Exception exception) {
/* 320 */           writeLog(exception.getMessage());
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/* 325 */       browserBean.setTitle(SystemEnv.getHtmlLabelName(122, this.languageid));
/* 326 */       arrayList3.add(new SearchConditionItem(ConditionType.BROWSER, SystemEnv.getHtmlLabelName(106, this.languageid), "", new String[] { "shareValue" }, null, 7, 17, browserBean));
/*     */       
/* 328 */       ArrayList<SearchConditionOption> arrayList4 = new ArrayList();
/* 329 */       if (k == 0) {
/*     */         
/* 331 */         arrayList4.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(140, this.languageid)));
/* 332 */         arrayList4.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(141, this.languageid)));
/* 333 */         arrayList4.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(124, this.languageid), true));
/*     */       
/*     */       }
/* 336 */       else if (k == 1) {
/* 337 */         arrayList4.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(140, this.languageid)));
/* 338 */         arrayList4.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(141, this.languageid), true));
/* 339 */         arrayList4.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(124, this.languageid)));
/*     */       } else {
/*     */         
/* 342 */         arrayList4.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(140, this.languageid), true));
/* 343 */         arrayList4.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(141, this.languageid)));
/* 344 */         arrayList4.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(124, this.languageid)));
/*     */       } 
/*     */       
/* 347 */       SearchConditionItem searchConditionItem2 = new SearchConditionItem(ConditionType.SELECT, SystemEnv.getHtmlLabelName(139, this.languageid), new String[] { "roleLevel" });
/* 348 */       searchConditionItem2.setOptions(arrayList4);
/* 349 */       arrayList3.add(searchConditionItem2);
/*     */ 
/*     */ 
/*     */       
/* 353 */       ArrayList<SearchConditionItem> arrayList5 = new ArrayList();
/* 354 */       SearchConditionItem searchConditionItem3 = new SearchConditionItem(ConditionType.INPUT_INTERVAL, SystemEnv.getHtmlLabelName(683, this.languageid), "", new String[] { "secLevel", "secLevelMax" }, null, 7, 17, null);
/* 355 */       searchConditionItem3.setValue(new int[] { i, j });
/* 356 */       searchConditionItem3.setMax("100");
/* 357 */       searchConditionItem3.setMin("-100");
/* 358 */       arrayList5.add(searchConditionItem3);
/*     */ 
/*     */ 
/*     */       
/* 362 */       ArrayList<SearchConditionItem> arrayList6 = new ArrayList();
/* 363 */       SearchConditionItem searchConditionItem4 = new SearchConditionItem(ConditionType.RANGEPICKER, SystemEnv.getHtmlLabelName(509722, this.languageid), "", new String[] { "canViewMinTime", "canViewMaxTime" }, null, 7, 17, true);
/*     */       
/* 365 */       ArrayList<String> arrayList7 = new ArrayList();
/*     */       
/* 367 */       if ("-1".equals(str4)) {
/* 368 */         str4 = "";
/*     */       }
/* 370 */       if ("-1".equals(str5)) {
/* 371 */         str5 = "";
/*     */       }
/* 373 */       arrayList7.add(str4);
/* 374 */       arrayList7.add(str5);
/* 375 */       searchConditionItem4.setValue(arrayList7);
/* 376 */       arrayList6.add(searchConditionItem4);
/* 377 */       arrayList2.add(arrayList3);
/* 378 */       arrayList2.add(arrayList5);
/* 379 */       arrayList2.add(arrayList6);
/*     */       
/* 381 */       hashMap1.put("4", arrayList2);
/*     */       
/* 383 */       arrayList.add(searchConditionItem1);
/* 384 */       arrayList.add(hashMap1);
/*     */     }
/* 386 */     else if ("5".equals(str2)) {
/*     */       
/* 388 */       SearchConditionItem searchConditionItem1 = new SearchConditionItem();
/*     */       
/* 390 */       ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 391 */       arrayList1.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(1340, this.languageid), true));
/* 392 */       searchConditionItem1 = new SearchConditionItem(ConditionType.SELECT, SystemEnv.getHtmlLabelName(63, this.languageid), "", new String[] { "shareType" }, arrayList1, 7, 17, null);
/* 393 */       searchConditionItem1.setViewAttr(1);
/*     */ 
/*     */       
/* 396 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */       
/* 398 */       ArrayList<ArrayList<SearchConditionItem>> arrayList2 = new ArrayList();
/* 399 */       ArrayList<SearchConditionItem> arrayList3 = new ArrayList();
/* 400 */       SearchConditionItem searchConditionItem2 = new SearchConditionItem(ConditionType.RANGEPICKER, SystemEnv.getHtmlLabelName(509722, this.languageid), "", new String[] { "canViewMinTime", "canViewMaxTime" }, null, 7, 17, true);
/*     */       
/* 402 */       ArrayList<String> arrayList4 = new ArrayList();
/*     */       
/* 404 */       if ("-1".equals(str4)) {
/* 405 */         str4 = "";
/*     */       }
/* 407 */       if ("-1".equals(str5)) {
/* 408 */         str5 = "";
/*     */       }
/* 410 */       arrayList4.add(str4);
/* 411 */       arrayList4.add(str5);
/* 412 */       searchConditionItem2.setValue(arrayList4);
/* 413 */       arrayList3.add(searchConditionItem2);
/* 414 */       arrayList2.add(arrayList3);
/*     */       
/* 416 */       hashMap1.put("5", arrayList2);
/*     */       
/* 418 */       arrayList.add(searchConditionItem1);
/* 419 */       arrayList.add(hashMap1);
/*     */     }
/* 421 */     else if ("6".equals(str2)) {
/*     */       
/* 423 */       SearchConditionItem searchConditionItem1 = new SearchConditionItem();
/*     */       
/* 425 */       ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 426 */       arrayList1.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(6086, this.languageid), true));
/* 427 */       searchConditionItem1 = new SearchConditionItem(ConditionType.SELECT, SystemEnv.getHtmlLabelName(63, this.languageid), "", new String[] { "shareType" }, arrayList1, 7, 17, null);
/* 428 */       searchConditionItem1.setViewAttr(1);
/*     */ 
/*     */       
/* 431 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */       
/* 433 */       ArrayList<ArrayList<SearchConditionItem>> arrayList2 = new ArrayList();
/*     */ 
/*     */       
/* 436 */       ArrayList<SearchConditionItem> arrayList3 = new ArrayList();
/* 437 */       BrowserBean browserBean1 = new BrowserBean("278");
/* 438 */       browserBean1.setViewAttr(3);
/* 439 */       if (!"".equals(str3)) {
/*     */         try {
/* 441 */           JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/* 442 */           ArrayList<HashMap<Object, Object>> arrayList9 = new ArrayList();
/*     */           
/* 444 */           String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 445 */           for (String str : arrayOfString) {
/* 446 */             HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 447 */             hashMap3.put("id", str);
/* 448 */             hashMap3.put("name", jobTitlesComInfo.getJobTitlesname(str));
/* 449 */             arrayList9.add(hashMap3);
/*     */           } 
/* 451 */           browserBean1.setReplaceDatas(arrayList9);
/* 452 */         } catch (Exception exception) {
/* 453 */           writeLog(exception.getMessage());
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/* 458 */       browserBean1.setTitle(SystemEnv.getHtmlLabelName(6086, this.languageid));
/* 459 */       arrayList3.add(new SearchConditionItem(ConditionType.BROWSER, SystemEnv.getHtmlLabelName(106, this.languageid), "", new String[] { "shareValue" }, null, 7, 17, browserBean1));
/*     */ 
/*     */       
/* 462 */       ArrayList<SearchConditionItem> arrayList4 = new ArrayList();
/* 463 */       ArrayList<SearchConditionOption> arrayList5 = new ArrayList();
/*     */       
/* 465 */       if (m == 0) {
/* 466 */         arrayList5.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(140, this.languageid), true));
/* 467 */         arrayList5.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(19437, this.languageid), false));
/* 468 */         arrayList5.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(19438, this.languageid), false));
/* 469 */       } else if (m == 1) {
/* 470 */         arrayList5.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(140, this.languageid), false));
/* 471 */         arrayList5.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(19437, this.languageid), true));
/* 472 */         arrayList5.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(19438, this.languageid), false));
/*     */       } else {
/* 474 */         arrayList5.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(140, this.languageid), false));
/* 475 */         arrayList5.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(19437, this.languageid), false));
/* 476 */         arrayList5.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(19438, this.languageid), true));
/*     */       } 
/*     */ 
/*     */       
/* 480 */       SearchConditionItem searchConditionItem2 = new SearchConditionItem(ConditionType.SELECT_LINKAGE, SystemEnv.getHtmlLabelName(28169, this.languageid), "", new String[] { "jobtitleLevel" }, arrayList5, 7, 17, null);
/* 481 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */ 
/*     */       
/* 484 */       BrowserBean browserBean2 = new BrowserBean("194");
/* 485 */       browserBean2.setViewAttr(3);
/* 486 */       SearchConditionItem searchConditionItem3 = new SearchConditionItem(ConditionType.BROWSER, "", "", new String[] { "jobtitleScopeid" }, null, 7, 17, browserBean2);
/* 487 */       searchConditionItem3.setViewAttr(3);
/* 488 */       ArrayList<HashMap<Object, Object>> arrayList6 = new ArrayList();
/*     */       
/* 490 */       if (!"".equals(str6)) {
/*     */         try {
/* 492 */           SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 493 */           DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*     */           
/* 495 */           String[] arrayOfString = Util.TokenizerString2(str6, ",");
/* 496 */           for (String str : arrayOfString) {
/* 497 */             HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 498 */             hashMap3.put("id", str);
/* 499 */             hashMap3.put("name", (m == 1) ? subCompanyComInfo.getSubCompanyname(str) : departmentComInfo.getDepartmentname(str));
/* 500 */             arrayList6.add(hashMap3);
/*     */           } 
/* 502 */         } catch (Exception exception) {
/* 503 */           writeLog(exception.getMessage());
/*     */         } 
/*     */       }
/* 506 */       hashMap2.put("1", searchConditionItem3);
/*     */       
/* 508 */       BrowserBean browserBean3 = new BrowserBean("57");
/* 509 */       browserBean3.setViewAttr(3);
/* 510 */       SearchConditionItem searchConditionItem4 = new SearchConditionItem(ConditionType.BROWSER, "", "", new String[] { "jobtitleScopeid" }, null, 7, 17, browserBean3);
/* 511 */       searchConditionItem4.setViewAttr(3);
/* 512 */       hashMap2.put("2", searchConditionItem4);
/* 513 */       searchConditionItem2.setSelectLinkageDatas(hashMap2);
/* 514 */       if (m == 1) {
/* 515 */         browserBean2.setReplaceDatas(arrayList6);
/*     */       } else {
/* 517 */         browserBean3.setReplaceDatas(arrayList6);
/*     */       } 
/* 519 */       arrayList4.add(searchConditionItem2);
/*     */       
/* 521 */       ArrayList<SearchConditionItem> arrayList7 = new ArrayList();
/* 522 */       SearchConditionItem searchConditionItem5 = new SearchConditionItem(ConditionType.RANGEPICKER, SystemEnv.getHtmlLabelName(509722, this.languageid), "", new String[] { "canViewMinTime", "canViewMaxTime" }, null, 7, 17, true);
/*     */       
/* 524 */       ArrayList<String> arrayList8 = new ArrayList();
/*     */       
/* 526 */       if ("-1".equals(str4)) {
/* 527 */         str4 = "";
/*     */       }
/* 529 */       if ("-1".equals(str5)) {
/* 530 */         str5 = "";
/*     */       }
/* 532 */       arrayList8.add(str4);
/* 533 */       arrayList8.add(str5);
/* 534 */       searchConditionItem5.setValue(arrayList8);
/*     */       
/* 536 */       arrayList2.add(arrayList3);
/* 537 */       arrayList2.add(arrayList4);
/* 538 */       arrayList7.add(searchConditionItem5);
/* 539 */       arrayList2.add(arrayList7);
/*     */       
/* 541 */       hashMap1.put("6", arrayList2);
/*     */       
/* 543 */       arrayList.add(searchConditionItem1);
/* 544 */       arrayList.add(hashMap1);
/*     */     }
/* 546 */     else if ("7".equals(str2)) {
/*     */       
/* 548 */       SearchConditionItem searchConditionItem = new SearchConditionItem();
/*     */       
/* 550 */       ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 551 */       arrayList1.add(new SearchConditionOption("7", SystemEnv.getHtmlLabelName(368, this.languageid), true));
/* 552 */       searchConditionItem = new SearchConditionItem(ConditionType.SELECT, SystemEnv.getHtmlLabelName(63, this.languageid), "", new String[] { "shareType" }, arrayList1, 7, 17, null);
/* 553 */       searchConditionItem.setViewAttr(1);
/*     */ 
/*     */       
/* 556 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */       
/* 558 */       ArrayList<ArrayList<SearchConditionItem>> arrayList2 = new ArrayList();
/* 559 */       ArrayList<SearchConditionItem> arrayList3 = new ArrayList();
/* 560 */       BrowserBean browserBean = new BrowserBean("17");
/* 561 */       browserBean.setViewAttr(3);
/* 562 */       if (!"".equals(str3)) {
/*     */         try {
/* 564 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 565 */           ArrayList<HashMap<Object, Object>> arrayList4 = new ArrayList();
/*     */           
/* 567 */           String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 568 */           for (String str : arrayOfString) {
/* 569 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 570 */             hashMap2.put("id", str);
/* 571 */             hashMap2.put("name", resourceComInfo.getLastname(str));
/* 572 */             arrayList4.add(hashMap2);
/*     */           } 
/*     */           
/* 575 */           browserBean.setReplaceDatas(arrayList4);
/* 576 */         } catch (Exception exception) {
/* 577 */           writeLog(exception.getMessage());
/*     */         } 
/*     */       }
/* 580 */       arrayList3.add(new SearchConditionItem(ConditionType.BROWSER, SystemEnv.getHtmlLabelName(106, this.languageid), "", new String[] { "shareValue" }, null, 7, 17, browserBean));
/* 581 */       arrayList2.add(arrayList3);
/*     */       
/* 583 */       hashMap1.put("7", arrayList2);
/*     */       
/* 585 */       arrayList.add(searchConditionItem);
/* 586 */       arrayList.add(hashMap1);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 591 */     hashMap.put("conditioninfo", arrayList);
/* 592 */     hashMap.put("status", "1");
/* 593 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 598 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/blog/cmd/shareset/BlogShareSetEditConditionCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */