/*    */ package com.engine.workplan.cmd.calendar;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.WorkPlan.exchange.WorkPlanExchangeUtil;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SyncEwsToWPCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public SyncEwsToWPCmd(User paramUser, Map<String, Object> paramMap) {
/* 30 */     this.user = paramUser;
/* 31 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 40 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 51 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 52 */     String str = Util.null2String(this.params.get("selectDate"));
/* 53 */     WorkPlanExchangeUtil workPlanExchangeUtil = new WorkPlanExchangeUtil();
/* 54 */     workPlanExchangeUtil.setType(1);
/* 55 */     workPlanExchangeUtil.setSelectDate(str);
/* 56 */     workPlanExchangeUtil.EWSpush2OASystem(this.user.getUID());
/* 57 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workplan/cmd/calendar/SyncEwsToWPCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */