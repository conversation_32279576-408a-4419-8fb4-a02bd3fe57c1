/*     */ package com.engine.workplan.cmd.workplantypeset;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.biz.SimpleBizLogger;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4Workplan;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SysMaintenanceLog;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class DoSaveTypeSetCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>> {
/*     */   public DoSaveTypeSetCmd(User paramUser, Map<String, Object> paramMap) {
/*  25 */     this.user = paramUser;
/*  26 */     this.params = paramMap;
/*  27 */     this.logger = new SimpleBizLogger();
/*     */   }
/*     */   private SimpleBizLogger logger;
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  32 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<BizLogContext> getLogContexts() {
/*  37 */     return this.logger.getBizLogContexts();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public SimpleBizLogger logBefore() {
/*  45 */     BizLogContext bizLogContext = new BizLogContext();
/*  46 */     bizLogContext.setLogType(BizLogType.WKP_ENGINE);
/*     */ 
/*     */ 
/*     */     
/*  50 */     bizLogContext.setTargetId("1");
/*  51 */     bizLogContext.setTargetName(SystemEnv.getHtmlLabelName(19773, this.user.getLanguage()));
/*  52 */     bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/*  53 */     bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workplan.WORKPLAN_ENGINE_TYPE_SET);
/*  54 */     bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4Workplan.WORKPLAN_ENGINE_TYPE_SET);
/*  55 */     bizLogContext.setBelongTypeTargetName(SystemEnv.getHtmlLabelName(19773, this.user.getLanguage()));
/*  56 */     bizLogContext.setParams(this.params);
/*  57 */     this.logger.setUser(this.user);
/*  58 */     String str1 = "select  * from (select workplanname as completeScheduleName,workplancolor as completeScheduleColor,wavailable as completeScheduleAvailable from overworkplan where id = 1) t1,\n(select workplanname as archivalScheduleName,workplancolor as archivalScheduleColor,wavailable as archivalScheduleAvailable from overworkplan where id = 2) t2";
/*     */     
/*  60 */     this.logger.setMainSql(str1);
/*  61 */     this.logger.setMainTargetNameColumn("");
/*  62 */     SimpleBizLogger.SubLogInfo subLogInfo = this.logger.getNewSubLogInfo();
/*  63 */     String str2 = "";
/*     */     
/*  65 */     str2 = "select * from workplantype ";
/*  66 */     subLogInfo.setSubSql(str2, "workplantypeid");
/*  67 */     subLogInfo.setSubTargetNameColumn("workPlanTypeName");
/*     */     
/*  69 */     this.logger.addSubLogInfo(subLogInfo);
/*  70 */     this.logger.before(bizLogContext);
/*  71 */     return this.logger;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  76 */     logBefore();
/*  77 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  78 */     boolean bool = false;
/*  79 */     if (!HrmUserVarify.checkUserRight("WorkPlanTypeSet:Set", this.user)) {
/*     */       
/*  81 */       hashMap.put("ret", "noright");
/*  82 */       return (Map)hashMap;
/*     */     } 
/*  84 */     String str1 = Util.null2String(this.params.get("workPlanTypeID"));
/*  85 */     String[] arrayOfString1 = str1.split(",", -1);
/*  86 */     String str2 = Util.null2String(this.params.get("workPlanTypeName"));
/*  87 */     String[] arrayOfString2 = str2.split(",");
/*  88 */     for (byte b = 0; b < arrayOfString2.length; b++) {
/*  89 */       arrayOfString2[b] = Util.convertInput2DB(arrayOfString2[b]);
/*     */     }
/*  91 */     String str3 = Util.null2String(this.params.get("workPlanTypeColor"));
/*  92 */     String[] arrayOfString3 = str3.split(",");
/*  93 */     String str4 = Util.null2String(this.params.get("available"));
/*  94 */     String[] arrayOfString4 = str4.split(",");
/*  95 */     String str5 = Util.null2String(this.params.get("workPlanIDs"));
/*  96 */     String[] arrayOfString5 = str5.split(",", -1);
/*  97 */     String str6 = Util.null2String(this.params.get("workplanname"));
/*  98 */     String[] arrayOfString6 = str6.split(",");
/*  99 */     String str7 = Util.null2String(this.params.get("wcolor"));
/* 100 */     String[] arrayOfString7 = str7.split(",");
/* 101 */     String str8 = Util.null2String(this.params.get("wavailable"));
/* 102 */     String[] arrayOfString8 = str8.split(",");
/* 103 */     RecordSet recordSet1 = new RecordSet();
/* 104 */     RecordSet recordSet2 = new RecordSet();
/* 105 */     SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/* 106 */     String str9 = "-1";
/* 107 */     String str10 = "-1";
/* 108 */     String str11 = Util.null2String(this.params.get("delids"));
/* 109 */     String str12 = Util.null2String(this.params.get("wdelids"));
/*     */     
/* 111 */     ArrayList arrayList = new ArrayList();
/*     */     int i;
/* 113 */     for (i = 0; i < arrayOfString1.length; i++) {
/*     */       
/* 115 */       if (!"".equals(arrayOfString1[i]))
/*     */       {
/* 117 */         str9 = str9 + "," + arrayOfString1[i];
/*     */       }
/*     */     } 
/*     */     
/* 121 */     for (i = 0; i < arrayOfString5.length; i++) {
/*     */       
/* 123 */       if (!"".equals(arrayOfString5[i]))
/*     */       {
/* 125 */         str10 = str10 + "," + arrayOfString5[i];
/*     */       }
/*     */     } 
/*     */     
/* 129 */     if (str9.length() > 0) {
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 134 */       recordSet1.executeSql("SELECT WorkPlanTypeId FROM WorkPlanType WHERE workPlanTypeID NOT IN (" + str9 + ")");
/* 135 */       while (recordSet1.next()) {
/*     */         
/* 137 */         i = recordSet1.getInt("WorkPlanTypeId");
/* 138 */         recordSet2.executeSql("SELECT 1 FROM WorkPlan WHERE type_n = " + i);
/* 139 */         if (recordSet2.next()) {
/*     */           
/* 141 */           bool = true;
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 147 */     if (StringUtils.isNotBlank(str11)) {
/* 148 */       recordSet1.execute("delete from WorkPlanType where workplantypeid in (" + str11 + ")");
/*     */     }
/* 150 */     if (StringUtils.isNotBlank(str12)) {
/* 151 */       recordSet1.execute("delete from overworkplan where id in (" + str12 + ")");
/*     */     }
/* 153 */     if (bool) {
/*     */       
/* 155 */       hashMap.put("note", new String(SystemEnv.getHtmlLabelName(20210, this.user.getLanguage())));
/*     */     }
/*     */     else {
/*     */       
/* 159 */       recordSet1.executeSql("DELETE FROM WorkPlanType WHERE workPlanTypeID NOT IN (" + str9 + ")");
/* 160 */       recordSet1.executeSql("DELETE FROM OverWorkPlan WHERE id NOT IN (" + str10 + ")");
/* 161 */       recordSet1.executeSql("DELETE FROM WorkPlanMonitor WHERE workPlanTypeID NOT IN (" + str9 + ")");
/*     */       
/* 163 */       for (i = 0; i < arrayOfString1.length; i++) {
/*     */         
/* 165 */         if ("".equals(arrayOfString1[i])) {
/*     */ 
/*     */           
/* 168 */           recordSet1.executeSql("INSERT INTO WorkPlanType(workPlanTypeName, workPlanTypeAttribute, workPlanTypeColor, available, displayOrder,isSys) VALUES('" + arrayOfString2[i] + "', 0, '" + arrayOfString3[i] + "', '" + arrayOfString4[i] + "', " + i + ",0)");
/*     */         
/*     */         }
/*     */         else {
/*     */           
/* 173 */           recordSet1.executeSql("UPDATE WorkPlanType SET workPlanTypeName = '" + arrayOfString2[i] + "', workPlanTypeColor = '" + arrayOfString3[i] + "', available = '" + arrayOfString4[i] + "', displayOrder = " + i + " WHERE workPlanTypeID = " + arrayOfString1[i]);
/*     */         } 
/*     */       } 
/* 176 */       for (i = 0; i < arrayOfString5.length; i++) {
/*     */         
/* 178 */         String str = "";
/* 179 */         if ("".equals(arrayOfString5[i])) {
/*     */           
/* 181 */           str = "insert into overworkplan(id,workplanname,workplancolor,wavailable) values(" + i + ",'" + arrayOfString6[i] + "','" + arrayOfString7[i] + "'," + arrayOfString8[i] + ")";
/*     */         }
/*     */         else {
/*     */           
/* 185 */           str = "UPDATE overworkplan set workplanname='" + arrayOfString6[i] + "',workplancolor='" + arrayOfString7[i] + "',wavailable=" + arrayOfString8[i] + " where id=" + arrayOfString5[i];
/*     */         } 
/* 187 */         if (!"".equals(str))
/* 188 */           recordSet1.executeSql(str); 
/*     */       } 
/* 190 */       sysMaintenanceLog.resetParameter();
/* 191 */       sysMaintenanceLog.insSysLogInfo(this.user, 0, "日程类型", "日程类型设置", "211", "2", 0, Util.null2String(this.params.get("param_ip")));
/*     */     } 
/* 193 */     hashMap.put("ret", "true");
/* 194 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workplan/cmd/workplantypeset/DoSaveTypeSetCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */