/*     */ package com.engine.workplan.cmd.workplanmonitor;
/*     */ 
/*     */ import com.api.meeting.util.PageUidFactory;
/*     */ import com.api.workplan.util.TimeZoneCastUtil;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogSmallType4Workplan;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.meeting.MeetingUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkPlanMonitorListDataCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public WorkPlanMonitorListDataCmd(User paramUser, Map<String, Object> paramMap) {
/*  29 */     this.user = paramUser;
/*  30 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  36 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  41 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  42 */     RecordSet recordSet = new RecordSet();
/*  43 */     boolean bool = false;
/*  44 */     recordSet.execute("select 1 FROM WorkPlanMonitor WHERE hrmID = " + this.user.getUID());
/*  45 */     if (!recordSet.next()) {
/*  46 */       bool = true;
/*     */     }
/*     */     
/*  49 */     String str1 = String.valueOf(this.user.getUID());
/*  50 */     String str2 = Util.null2String(this.params.get("planName"));
/*  51 */     String str3 = Util.null2String(this.params.get("urgentLevel"));
/*  52 */     String str4 = Util.null2String(this.params.get("planType"));
/*  53 */     String str5 = Util.null2String(this.params.get("planStatus"));
/*  54 */     String str6 = Util.null2String(this.params.get("createrId"));
/*  55 */     String str7 = Util.null2String(this.params.get("receiveId"));
/*  56 */     String str8 = Util.null2String(this.params.get("beginDate"));
/*  57 */     String str9 = Util.null2String(this.params.get("endDate"));
/*  58 */     String str10 = Util.null2String(this.params.get("beginDaten"));
/*  59 */     String str11 = Util.null2String(this.params.get("endDaten"));
/*  60 */     String str12 = Util.null2String(this.params.get("ids"));
/*  61 */     if (str5.equals("-1"))
/*     */     {
/*  63 */       str5 = "";
/*     */     }
/*  65 */     Integer integer = Integer.valueOf(StringUtils.isBlank(Util.null2String(this.params.get("timeSag"))) ? "0" : Util.null2String(this.params.get("timeSag")));
/*  66 */     String str13 = Util.null2String(this.params.get("begindate"));
/*  67 */     String str14 = Util.null2String(this.params.get("enddate"));
/*     */     
/*  69 */     String str15 = PageUidFactory.getPageUid("wokrPlanMonitorList");
/*  70 */     String str16 = str15;
/*  71 */     String str17 = PageIdConst.getPageSize(str16, this.user.getUID());
/*     */     
/*  73 */     String str18 = " workPlan.createDate, workPlan.createTime ";
/*  74 */     String str19 = " workPlan.ID, workPlan.name, workPlan.urgentLevel, workPlan.type_n, workPlan.createrID, workPlan.status as status, workPlan.beginDate, workPlan.endDate,workPlan.beginTime, workPlan.endTime, workPlan.createDate, workPlan.createTime, workPlan.secretLevel ";
/*  75 */     String str20 = " WorkPlan workPlan, WorkPlanMonitor workPlanMonitor ";
/*  76 */     String str21 = " workPlan.type_n = workPlanMonitor.workPlanTypeID AND workPlanMonitor.hrmID = " + str1;
/*  77 */     if (bool) {
/*  78 */       str21 = str21 + " and 1 = 2 ";
/*     */     }
/*  80 */     if (!"".equals(str2) && null != str2) {
/*     */       
/*  82 */       str2 = str2.replaceAll("\"", "＂");
/*  83 */       str2 = str2.replaceAll("'", "＇");
/*  84 */       str21 = str21 + " AND workPlan.name LIKE '%" + str2 + "%'";
/*     */     } 
/*  86 */     if (!"".equals(str3) && null != str3)
/*     */     {
/*  88 */       str21 = str21 + " AND workPlan.urgentLevel = '" + str3 + "'";
/*     */     }
/*  90 */     if (!"".equals(str4) && null != str4)
/*     */     {
/*  92 */       str21 = str21 + " AND workPlan.type_n = '" + str4 + "'";
/*     */     }
/*  94 */     if (!"".equals(str5) && null != str5)
/*     */     {
/*  96 */       str21 = str21 + " AND workPlan.status = '" + str5 + "'";
/*     */     }
/*  98 */     if (!"".equals(str6) && null != str6) {
/*     */       
/* 100 */       if (str6.startsWith(",")) {
/* 101 */         str6 = str6.substring(1);
/*     */       }
/* 103 */       if (str6.endsWith(",")) {
/* 104 */         str6 = str6.substring(0, str6.length() - 1);
/*     */       }
/* 106 */       str21 = str21 + " AND workPlan.createrID in (" + str6 + ") ";
/*     */     } 
/*     */     
/* 109 */     if (!"".equals(str7) && null != str7) {
/*     */       
/* 111 */       if (str7.startsWith(",")) {
/* 112 */         str7 = str7.substring(1);
/*     */       }
/* 114 */       if (str7.endsWith(",")) {
/* 115 */         str7 = str7.substring(0, str7.length() - 1);
/*     */       }
/* 117 */       if (str7.length() > 0) {
/* 118 */         str21 = str21 + " AND ( ";
/* 119 */         ArrayList<String> arrayList = Util.TokenizerString(str7, ",");
/*     */         
/* 121 */         String str = "";
/* 122 */         if (recordSet.getDBType().equals("oracle")) {
/* 123 */           for (byte b = 0; b < arrayList.size(); b++) {
/* 124 */             str = str + " or concat(concat(',' , workPlan.resourceid) , ',')  like '%," + arrayList.get(b) + ",%' ";
/*     */           }
/*     */         }
/* 127 */         else if (recordSet.getDBType().equals("postgresql")) {
/* 128 */           for (byte b = 0; b < arrayList.size(); b++) {
/* 129 */             str = str + " or concat(concat(',' , workPlan.resourceid) , ',')  like '%," + arrayList.get(b) + ",%' ";
/*     */           }
/*     */         }
/* 132 */         else if (recordSet.getDBType().equals("mysql")) {
/* 133 */           for (byte b = 0; b < arrayList.size(); b++) {
/* 134 */             str = str + " or concat(',' , workPlan.resourceid , ',')  like '%," + arrayList.get(b) + ",%' ";
/*     */           }
/*     */         } else {
/* 137 */           for (byte b = 0; b < arrayList.size(); b++) {
/* 138 */             str = str + " or ','+workPlan.resourceid+',' like '%," + arrayList.get(b) + ",%' ";
/*     */           }
/*     */         } 
/* 141 */         str21 = str21 + str.substring(3);
/* 142 */         str21 = str21 + " ) ";
/*     */       } 
/*     */     } 
/*     */     
/* 146 */     if (integer.intValue() > 0) {
/* 147 */       str21 = str21 + TimeZoneCastUtil.getDateTimeSql(integer.intValue(), str13, str14, "");
/*     */     }
/* 149 */     if (!str12.equals("")) {
/* 150 */       ArrayList<String> arrayList = Util.TokenizerString(str12, ",");
/*     */       
/* 152 */       String str = "";
/* 153 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 154 */         str = str + (str.equals("") ? (" workPlan.id = " + arrayList.get(b)) : (" or workPlan.id = " + arrayList.get(b)));
/*     */       }
/* 156 */       str21 = str21 + " AND ( " + str + " )";
/*     */     } 
/*     */     
/* 159 */     str21 = str21 + TimeZoneCastUtil.getWPMonitorDateTimeSql(str8, str9, str10, str11);
/* 160 */     str21 = str21 + MeetingUtil.getSecretSql(this.user, "workPlan.");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 177 */     String str22 = "<table pagesize=\"" + str17 + "\" tabletype=\"checkbox\" pageUid=\"" + str15 + "\" > <checkboxpopedom  id=\"checkbox\"  popedompara=\"column:status+column:secretLevel+" + this.user.getUID() + "\" showmethod=\"weaver.splitepage.transform.SptmForWorkPlan.monitorChkBtn\" /><sql backfields=\"" + str19 + "\" sqlform=\"" + str20 + "\" sqlprimarykey=\"workPlan.ID\" sqlorderby=\"" + str18 + "\"  sqlsortway=\"DESC\" sqlwhere=\"" + Util.toHtmlForSplitPage(str21) + "\"/><head><col width=\"10%\"  text=\"" + SystemEnv.getHtmlLabelName(882, this.user.getLanguage()) + "\" column=\"createrID\" orderkey=\"createrID\" transmethod=\"com.engine.workplan.util.WorkPlanUtil.getResourceName\" /><col width=\"20%\"  text=\"" + SystemEnv.getHtmlLabelName(229, this.user.getLanguage()) + "\" column=\"ID\" otherpara=\"column:name+column:type_n\" transmethod=\"com.engine.workplan.util.WorkPlanUtil.getWorkPlanName\"/><col width=\"10%\"  text=\"" + SystemEnv.getHtmlLabelName(15534, this.user.getLanguage()) + "\" column=\"urgentLevel\" otherpara=\"" + this.user.getLanguage() + "\" orderkey=\"urgentLevel\" transmethod=\"weaver.splitepage.transform.SptmForWorkPlan.getUrgentName\" /><col width=\"10%\"  text=\"" + SystemEnv.getHtmlLabelName(16094, this.user.getLanguage()) + "\" column=\"type_n\" orderkey=\"type_n\" transmethod=\"weaver.splitepage.transform.SptmForWorkPlan.getWorkPlanType\"/><col width=\"10%\"  text=\"" + SystemEnv.getHtmlLabelName(33467, this.user.getLanguage()) + "\" column=\"status\" otherpara=\"" + this.user.getLanguage() + "\" orderkey=\"status\" transmethod=\"weaver.splitepage.transform.SptmForWorkPlan.getStatusName\"/><col width=\"20%\"  text=\"" + SystemEnv.getHtmlLabelName(740, this.user.getLanguage()) + "\" column=\"beginDate\" orderkey=\"beginDate,beginTime\" otherpara=\"column:beginTime\" transmethod=\"com.api.meeting.util.MeetingTransMethod.getMeetingDateTime\"/><col width=\"20%\"  text=\"" + SystemEnv.getHtmlLabelName(741, this.user.getLanguage()) + "\" column=\"endDate\" orderkey=\"endDate,endTime\" otherpara=\"column:endTime\" transmethod=\"com.api.meeting.util.MeetingTransMethod.getMeetingDateTime\"/></head>\t\t<operates>\t\t<popedom column=\"id\" otherpara=\"column:status+column:secretLevel+" + this.user.getUID() + "\" transmethod=\"weaver.splitepage.transform.SptmForWorkPlan.showFinishBtn\"></popedom> \t\t<operate href=\"javascript:doFinish();\" text=\"" + SystemEnv.getHtmlLabelName(384651, this.user.getLanguage()) + "\" target=\"_self\" index=\"0\" />\t\t</operates></table>";
/*     */ 
/*     */ 
/*     */     
/* 181 */     String str23 = str15 + "_" + Util.getEncrypt(Util.getRandom());
/* 182 */     Util_TableMap.setVal(str23, str22);
/* 183 */     hashMap.put("sessionkey", str23);
/* 184 */     hashMap.put("logType", Integer.valueOf(BizLogType.WKP_ENGINE.getCode()));
/* 185 */     hashMap.put("logSmallType", Integer.valueOf(BizLogSmallType4Workplan.WORKPLAN_ENGINE_MONITOR.getCode()));
/* 186 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workplan/cmd/workplanmonitor/WorkPlanMonitorListDataCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */