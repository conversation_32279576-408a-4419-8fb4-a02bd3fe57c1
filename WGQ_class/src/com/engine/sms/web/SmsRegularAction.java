/*     */ package com.engine.sms.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.alibaba.fastjson.serializer.SerializerFeature;
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import com.engine.sms.service.SmsRegularService;
/*     */ import com.engine.sms.service.impl.SmsRegularServiceImpl;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.POST;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SmsRegularAction
/*     */ {
/*     */   private SmsRegularService getService(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  41 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  42 */     return getService(user);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SmsRegularService getService(User paramUser) {
/*  51 */     return (SmsRegularService)ServiceUtil.getService(SmsRegularServiceImpl.class, paramUser);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/createRegularSms")
/*     */   @Produces({"text/plain"})
/*     */   public String createRegularSms(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  65 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/*  67 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  68 */       Map map = ParamUtil.request2Map(paramHttpServletRequest);
/*  69 */       hashMap.putAll(getService(user).createRegularSms(map));
/*  70 */       hashMap.put("api_status", Boolean.valueOf(true));
/*  71 */     } catch (Exception exception) {
/*  72 */       exception.printStackTrace();
/*  73 */       hashMap.put("api_status", Boolean.valueOf(false));
/*  74 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/*  76 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/stopSmsRegular")
/*     */   @Produces({"text/plain"})
/*     */   public String stopSmsRegular(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  90 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/*  92 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  93 */       Map map = ParamUtil.request2Map(paramHttpServletRequest);
/*  94 */       hashMap.putAll(getService(user).stopSmsRegular(map));
/*  95 */       hashMap.put("api_status", Boolean.valueOf(true));
/*  96 */     } catch (Exception exception) {
/*  97 */       exception.printStackTrace();
/*  98 */       hashMap.put("api_status", Boolean.valueOf(false));
/*  99 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 101 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/sendSmsRegular")
/*     */   @Produces({"text/plain"})
/*     */   public String sendSmsRegular(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 115 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 117 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 118 */       Map map = ParamUtil.request2Map(paramHttpServletRequest);
/* 119 */       hashMap.putAll(getService(user).sendSmsRegular(map));
/* 120 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 121 */     } catch (Exception exception) {
/* 122 */       exception.printStackTrace();
/* 123 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 124 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 126 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/saveSmsRegularMemberInfo")
/*     */   @Produces({"text/plain"})
/*     */   public String saveSmsRegularMemberInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 140 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 142 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 143 */       Map map = ParamUtil.request2Map(paramHttpServletRequest);
/* 144 */       hashMap.putAll(getService(user).saveSmsRegularMemberInfo(map));
/* 145 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 146 */     } catch (Exception exception) {
/* 147 */       exception.printStackTrace();
/* 148 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 149 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 151 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/saveSmsRegularInfo")
/*     */   @Produces({"text/plain"})
/*     */   public String saveSmsRegularInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 165 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 167 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 168 */       Map map = ParamUtil.request2Map(paramHttpServletRequest);
/* 169 */       hashMap.putAll(getService(user).saveSmsRegularInfo(map));
/* 170 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 171 */     } catch (Exception exception) {
/* 172 */       exception.printStackTrace();
/* 173 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 174 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 176 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/getSmsRegularMemberList")
/*     */   @Produces({"text/plain"})
/*     */   public String getSmsRegularMemberList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 190 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 192 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 193 */       Map map = ParamUtil.request2Map(paramHttpServletRequest);
/* 194 */       hashMap.putAll(getService(user).getSmsRegularMemberList(map));
/* 195 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 196 */     } catch (Exception exception) {
/* 197 */       exception.printStackTrace();
/* 198 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 199 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 201 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/getSmsRegularMemberInfo")
/*     */   @Produces({"text/plain"})
/*     */   public String getSmsRegularMemberInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 215 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 217 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 218 */       Map map = ParamUtil.request2Map(paramHttpServletRequest);
/* 219 */       hashMap.putAll(getService(user).getSmsRegularMemberInfo(map));
/* 220 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 221 */     } catch (Exception exception) {
/* 222 */       exception.printStackTrace();
/* 223 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 224 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 226 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/deleteSmsRegularMemberInfo")
/*     */   @Produces({"text/plain"})
/*     */   public String deleteSmsRegularMemberInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 240 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 242 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 243 */       Map map = ParamUtil.request2Map(paramHttpServletRequest);
/* 244 */       hashMap.putAll(getService(user).deleteSmsRegularMemberInfo(map));
/* 245 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 246 */     } catch (Exception exception) {
/* 247 */       exception.printStackTrace();
/* 248 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 249 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 251 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/getSmsRegularList")
/*     */   @Produces({"text/plain"})
/*     */   public String getSmsRegularList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 265 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 267 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 268 */       Map map = ParamUtil.request2Map(paramHttpServletRequest);
/* 269 */       hashMap.putAll(getService(user).getSmsRegularList(map));
/* 270 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 271 */     } catch (Exception exception) {
/* 272 */       exception.printStackTrace();
/* 273 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 274 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 276 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/getSmsRegularInfo")
/*     */   @Produces({"text/plain"})
/*     */   public String getSmsRegularInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 290 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 292 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 293 */       Map map = ParamUtil.request2Map(paramHttpServletRequest);
/* 294 */       hashMap.putAll(getService(user).getSmsRegularInfo(map));
/* 295 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 296 */     } catch (Exception exception) {
/* 297 */       exception.printStackTrace();
/* 298 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 299 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 301 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/getSmsRegularCondition")
/*     */   @Produces({"text/plain"})
/*     */   public String getSmsRegularCondition(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 315 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 317 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 318 */       Map map = ParamUtil.request2Map(paramHttpServletRequest);
/* 319 */       hashMap.putAll(getService(user).getSmsRegularCondition(map));
/* 320 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 321 */     } catch (Exception exception) {
/* 322 */       exception.printStackTrace();
/* 323 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 324 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 326 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/deleteSmsRegular")
/*     */   @Produces({"text/plain"})
/*     */   public String deleteSmsRegular(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 340 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 342 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 343 */       Map map = ParamUtil.request2Map(paramHttpServletRequest);
/* 344 */       hashMap.putAll(getService(user).deleteSmsRegular(map));
/* 345 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 346 */     } catch (Exception exception) {
/* 347 */       exception.printStackTrace();
/* 348 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 349 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 351 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/sms/web/SmsRegularAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */