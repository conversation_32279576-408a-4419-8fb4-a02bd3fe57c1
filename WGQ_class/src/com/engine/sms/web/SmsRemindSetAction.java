/*     */ package com.engine.sms.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.alibaba.fastjson.serializer.SerializerFeature;
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import com.engine.sms.service.SmsRemindSetService;
/*     */ import com.engine.sms.service.impl.SmsRemindSetServiceImpl;
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.POST;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SmsRemindSetAction
/*     */ {
/*     */   private SmsRemindSetService getService(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  40 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  41 */     return getService(user);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SmsRemindSetService getService(User paramUser) {
/*  51 */     return (SmsRemindSetService)ServiceUtil.getService(SmsRemindSetServiceImpl.class, paramUser);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/getRemindSetDatas")
/*     */   @Produces({"text/plain"})
/*     */   public String getRemindSetDatas(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  65 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/*  67 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  68 */       hashMap.putAll(getService(user).getRemindSetDatas(ParamUtil.request2Map(paramHttpServletRequest)));
/*  69 */       hashMap.put("api_status", Boolean.valueOf(true));
/*  70 */     } catch (Exception exception) {
/*  71 */       exception.printStackTrace();
/*  72 */       hashMap.put("api_status", Boolean.valueOf(false));
/*  73 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/*  75 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/getRemindSetRightMenu")
/*     */   @Produces({"text/plain"})
/*     */   public String getRemindSetRightMenu(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  89 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/*  91 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  92 */       hashMap.putAll(getService(user).getRemindSetRightMenu(ParamUtil.request2Map(paramHttpServletRequest)));
/*  93 */       hashMap.put("api_status", Boolean.valueOf(true));
/*  94 */     } catch (Exception exception) {
/*  95 */       exception.printStackTrace();
/*  96 */       hashMap.put("api_status", Boolean.valueOf(false));
/*  97 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/*  99 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/saveRemindSetDatas")
/*     */   @Produces({"text/plain"})
/*     */   public String saveRemindSetDatas(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 114 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 116 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 117 */       hashMap.putAll(getService(user).saveRemindSetDatas(ParamUtil.request2Map(paramHttpServletRequest)));
/* 118 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 119 */     } catch (Exception exception) {
/* 120 */       exception.printStackTrace();
/* 121 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 122 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 124 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/sms/web/SmsRemindSetAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */