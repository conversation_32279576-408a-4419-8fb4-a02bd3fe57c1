/*    */ package com.engine.sms.cmd.smsInterfaceSet;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.meeting.util.MeetingNoRightUtil;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.sms.SmsTemplateUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SaveDetachBaseInfoCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public SaveDetachBaseInfoCmd(User paramUser, Map<String, Object> paramMap) {
/* 32 */     this.user = paramUser;
/* 33 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 43 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 48 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 49 */     if (!HrmUserVarify.checkUserRight("SmsTemplate:Interface", this.user))
/*    */     {
/* 51 */       return MeetingNoRightUtil.getNoRightMap();
/*    */     }
/* 53 */     RecordSet recordSet = new RecordSet();
/* 54 */     int i = Util.getIntValue(Util.null2String(this.params.get("allowSubUse")), 0);
/* 55 */     int j = Util.getIntValue(Util.null2String(this.params.get("interfaceResource")), 0);
/* 56 */     int k = Util.getIntValue(Util.null2String(this.params.get("subcompanyid")), 0);
/* 57 */     if (k < 0) {
/* 58 */       k = 0;
/*    */     }
/*    */     
/* 61 */     Map map = SmsTemplateUtil.getTemplateInterfaceRight(this.user, k);
/* 62 */     boolean bool = Util.str2bool(Util.null2String(map.get("hasRight")));
/* 63 */     if (!bool) {
/* 64 */       return MeetingNoRightUtil.getNoRightMap();
/*    */     }
/* 66 */     int m = Util.getIntValue(Util.null2String(map.get("detachable")), 0);
/*    */ 
/*    */     
/* 69 */     if (m == 1) {
/* 70 */       if (k > 0) {
/* 71 */         boolean bool1 = SmsTemplateUtil.getAllowSubUse();
/* 72 */         recordSet.executeUpdate("update smstemplateSyncSet set interfaceResource = ? where  subcompanyid = ?", new Object[] { Integer.valueOf(j), Integer.valueOf(k) });
/* 73 */         boolean bool2 = false;
/* 74 */         recordSet.executeQuery("select * from  smsTemplateProperties where subcompanyid = ?", new Object[] { Integer.valueOf(k) });
/* 75 */         if (recordSet.next()) {
/* 76 */           bool2 = true;
/*    */         }
/*    */         
/* 79 */         if (!bool2 && j == 1 && bool1) {
/* 80 */           recordSet.executeUpdate("insert into smstemplateBaseFields ( commontemplate ,templateid ,templateType ,subcompanyid ) select commontemplate,templateid ,templateType ," + k + " from  smstemplateBaseFields where subcompanyid = 0 ", new Object[0]);
/* 81 */           recordSet.executeUpdate("insert into smsTemplateProperties ( prop,val,subcompanyid ) select prop,val," + k + " from  smsTemplateProperties where subcompanyid = 0 ", new Object[0]);
/*    */         }
/*    */       
/*    */       } else {
/*    */         
/* 86 */         recordSet.executeUpdate("update smstemplateSyncSet set allowSubUse = ? where  subcompanyid = 0", new Object[] { Integer.valueOf(i) });
/* 87 */         if (i == 0)
/*    */         {
/* 89 */           recordSet.executeUpdate("update smstemplateSyncSet set interfaceResource = 1 where  subcompanyid = <>0", new Object[0]);
/*    */         }
/*    */       } 
/*    */     } else {
/*    */       
/* 94 */       return MeetingNoRightUtil.getNoRightMap();
/*    */     } 
/* 96 */     hashMap.put("status", Boolean.valueOf(true));
/* 97 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/sms/cmd/smsInterfaceSet/SaveDetachBaseInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */