/*    */ package com.engine.sms.cmd.smsRegular;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.Arrays;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DeleteSmsRegularMemberInfoCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public DeleteSmsRegularMemberInfoCmd(User paramUser, Map<String, Object> paramMap) {
/* 32 */     this.user = paramUser;
/* 33 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 43 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 53 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 54 */     String str = Util.null2String(this.params.get("ids"));
/* 55 */     RecordSet recordSet = new RecordSet();
/* 56 */     if (!str.equals("")) {
/* 57 */       Arrays.<String>asList(str.split(",")).stream().forEach(paramString -> paramRecordSet.executeUpdate("delete from smsRegularMember where id = ?", new Object[] { paramString }));
/*    */     }
/*    */ 
/*    */     
/* 61 */     hashMap.put("status", Boolean.valueOf(true));
/* 62 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/sms/cmd/smsRegular/DeleteSmsRegularMemberInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */