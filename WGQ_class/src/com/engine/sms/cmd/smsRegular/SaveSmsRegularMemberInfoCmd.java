/*    */ package com.engine.sms.cmd.smsRegular;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SaveSmsRegularMemberInfoCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public SaveSmsRegularMemberInfoCmd(User paramUser, Map<String, Object> paramMap) {
/* 31 */     this.user = paramUser;
/* 32 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 42 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 52 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 53 */     String str1 = Util.null2String(this.params.get("id"));
/* 54 */     String str2 = Util.null2String(this.params.get("receiverNumber"));
/* 55 */     String str3 = Util.null2String(this.params.get("msg"));
/* 56 */     String str4 = Util.null2String(this.params.get("receiverType"));
/* 57 */     RecordSet recordSet = new RecordSet();
/* 58 */     if (str4.equals("1")) {
/* 59 */       recordSet.executeUpdate("update smsRegularMember set receiverNumber = ?,customSmsContent = ? where id = ?", new Object[] { str2, str3, str1 });
/* 60 */     } else if (str4.equals("2")) {
/* 61 */       recordSet.executeUpdate("update smsRegularMember set receiverNumber = ? where id = ?", new Object[] { str2, str1 });
/*    */     } 
/*    */     
/* 64 */     hashMap.put("status", Boolean.valueOf(true));
/* 65 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/sms/cmd/smsRegular/SaveSmsRegularMemberInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */