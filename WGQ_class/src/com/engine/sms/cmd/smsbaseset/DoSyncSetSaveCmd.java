/*     */ package com.engine.sms.cmd.smsbaseset;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.biz.SimpleBizLogger;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4SMS;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.sms.SmsUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoSyncSetSaveCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private SimpleBizLogger logger;
/*     */   private BizLogContext bizLogContext;
/*     */   
/*     */   public DoSyncSetSaveCmd(User paramUser, Map<String, Object> paramMap) {
/*  48 */     this.user = paramUser;
/*  49 */     this.params = paramMap;
/*  50 */     this.logger = new SimpleBizLogger();
/*  51 */     this.bizLogContext = new BizLogContext();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  61 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  63 */     if (!HrmUserVarify.checkUserRight("Sms:Set", this.user)) {
/*  64 */       hashMap.put("status", "noright");
/*  65 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  68 */     String str1 = Util.null2String(this.params.get("subcanuse"));
/*  69 */     if (str1.equals("")) {
/*  70 */       str1 = "0";
/*     */     }
/*  72 */     String str2 = Util.null2String(this.params.get("applicationfrom"));
/*  73 */     if (str2.equals("")) {
/*  74 */       str2 = "0";
/*     */     }
/*  76 */     int i = Util.getIntValue(Util.null2String(this.params.get("subcompanyid")), 0);
/*  77 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/*  79 */       String str = "update sms_syncset set subcanuse = ?,applicationfrom = ? where subcompanyid = ?";
/*     */       
/*  81 */       if (i == 0) {
/*  82 */         this.bizLogContext.setTargetId("0");
/*  83 */         this.bizLogContext.setOperateType(BizLogOperateType.UPDATE);
/*  84 */         beforeLog();
/*  85 */         recordSet.executeUpdate(str, new Object[] { str1, str2, Integer.valueOf(i) });
/*     */       } else {
/*     */         
/*  88 */         recordSet.executeQuery("SELECT * FROM sms_syncset where subcompanyid = ?", new Object[] { Integer.valueOf(i) });
/*  89 */         if (recordSet.next()) {
/*  90 */           this.bizLogContext.setTargetId(i + "");
/*  91 */           this.bizLogContext.setOperateType(BizLogOperateType.UPDATE);
/*  92 */           beforeLog();
/*  93 */           recordSet.executeUpdate(str, new Object[] { "0", str2, Integer.valueOf(i) });
/*     */         } else {
/*  95 */           this.bizLogContext.setTargetId(i + "");
/*  96 */           this.bizLogContext.setOperateType(BizLogOperateType.ADD);
/*  97 */           beforeLog();
/*  98 */           recordSet.executeUpdate("insert into sms_syncset (subcanuse,applicationfrom,subcompanyid) values(?,?,?)", new Object[] { "0", str2, Integer.valueOf(i) });
/*     */         } 
/*     */         
/* 101 */         recordSet.executeQuery("select * from sms_syncset where subcompanyid = 0", new Object[0]);
/* 102 */         if (recordSet.next()) str1 = Util.null2String(recordSet.getString("subcanuse")); 
/* 103 */         if ("1".equals(str2) && Util.getIntValue(str1) > 0) {
/* 104 */           SmsUtil.dealSmsInitData(i + "");
/*     */         }
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 114 */       hashMap.put("status", "true");
/* 115 */     } catch (Exception exception) {
/* 116 */       exception.printStackTrace();
/* 117 */       hashMap.put("status", "false");
/*     */     } 
/* 119 */     hashMap.put("applicationfrom", str2);
/* 120 */     hashMap.put("subcanuse", str1);
/*     */     
/* 122 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public void beforeLog() {
/* 126 */     this.bizLogContext.setDateObject(new Date());
/* 127 */     this.bizLogContext.setUserid(this.user.getUID());
/* 128 */     this.bizLogContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/* 129 */     this.bizLogContext.setTargetName("" + SystemEnv.getHtmlLabelName(524032, ThreadVarLanguage.getLang()) + "");
/* 130 */     this.bizLogContext.setLogType(BizLogType.SMS_ENGINE);
/* 131 */     this.bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4SMS.SMS_ENGINE_SYNC_SET);
/* 132 */     this.bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4SMS.SMS_ENGINE_SYNC_SET);
/* 133 */     this.bizLogContext.setBelongTypeTargetName("" + SystemEnv.getHtmlLabelName(524032, ThreadVarLanguage.getLang()) + "");
/* 134 */     this.bizLogContext.setParams(this.params);
/* 135 */     this.bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/* 136 */     this.logger.setUser(this.user);
/* 137 */     this.logger.setParams(this.params);
/*     */     
/* 139 */     this.logger.setMainSql("select * from sms_syncset where subcompanyid = " + this.bizLogContext.getTargetId(), "subcompanyid");
/* 140 */     this.logger.setMainTargetNameColumn("" + SystemEnv.getHtmlLabelName(524032, ThreadVarLanguage.getLang()) + "");
/* 141 */     this.logger.before(this.bizLogContext);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 149 */     return this.logger.getBizLogContext();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/sms/cmd/smsbaseset/DoSyncSetSaveCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */