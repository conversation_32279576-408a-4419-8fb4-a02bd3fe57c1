/*     */ package com.engine.crm.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import com.engine.crm.service.ContactLogService;
/*     */ import com.engine.crm.service.impl.ContactLogServiceImpl;
/*     */ import com.engine.workflow.util.CommonUtil;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.POST;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ContactLogAction
/*     */ {
/*     */   private ContactLogService getService(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  23 */     User user = CommonUtil.getUserByRequest(paramHttpServletRequest, paramHttpServletResponse);
/*  24 */     return (ContactLogService)ServiceUtil.getService(ContactLogServiceImpl.class, user);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/contactCreate")
/*     */   @Produces({"text/plain"})
/*     */   public String ContacterSearch(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  37 */     paramHttpServletResponse.setCharacterEncoding("utf-8");
/*  38 */     return JSONObject.toJSONString(getService(paramHttpServletRequest, paramHttpServletResponse).contactLogCreate(ParamUtil.request2Map(paramHttpServletRequest)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/contactLogRp")
/*     */   @Produces({"text/plain"})
/*     */   public String getContactLogRp(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  51 */     paramHttpServletResponse.setCharacterEncoding("utf-8");
/*  52 */     return JSONObject.toJSONString(getService(paramHttpServletRequest, paramHttpServletResponse).getContactLogRp(ParamUtil.request2Map(paramHttpServletRequest)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/condition")
/*     */   @Produces({"text/plain"})
/*     */   public String getCondition(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  65 */     paramHttpServletResponse.setCharacterEncoding("utf-8");
/*  66 */     return JSONObject.toJSONString(getService(paramHttpServletRequest, paramHttpServletResponse).getCondition(ParamUtil.request2Map(paramHttpServletRequest)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/contactLogRemind")
/*     */   @Produces({"text/plain"})
/*     */   public String getContactLogRemind(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  79 */     paramHttpServletResponse.setCharacterEncoding("utf-8");
/*  80 */     return JSONObject.toJSONString(getService(paramHttpServletRequest, paramHttpServletResponse).getContactLogRemind(ParamUtil.request2Map(paramHttpServletRequest)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/finishWorkPlan")
/*     */   @Produces({"text/plain"})
/*     */   public String finishWorkPlan(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  93 */     paramHttpServletResponse.setCharacterEncoding("utf-8");
/*  94 */     return JSONObject.toJSONString(getService(paramHttpServletRequest, paramHttpServletResponse).finishWorkPlan(ParamUtil.request2Map(paramHttpServletRequest)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/remindSetting")
/*     */   @Produces({"text/plain"})
/*     */   public String remindSetting(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 107 */     paramHttpServletResponse.setCharacterEncoding("utf-8");
/* 108 */     return JSONObject.toJSONString(getService(paramHttpServletRequest, paramHttpServletResponse).remindSetting(ParamUtil.request2Map(paramHttpServletRequest)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/remindInfo")
/*     */   @Produces({"text/plain"})
/*     */   public String remindInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 121 */     paramHttpServletResponse.setCharacterEncoding("utf-8");
/* 122 */     return JSONObject.toJSONString(getService(paramHttpServletRequest, paramHttpServletResponse).remindInfo(ParamUtil.request2Map(paramHttpServletRequest)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/remindCount")
/*     */   @Produces({"text/plain"})
/*     */   public String remindCount(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 135 */     paramHttpServletResponse.setCharacterEncoding("utf-8");
/* 136 */     return JSONObject.toJSONString(getService(paramHttpServletRequest, paramHttpServletResponse).remindCount(ParamUtil.request2Map(paramHttpServletRequest)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getImpInitDatas")
/*     */   @Produces({"text/plain"})
/*     */   public String getImpInitDatas(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 149 */     paramHttpServletResponse.setCharacterEncoding("utf-8");
/* 150 */     return JSONObject.toJSONString(getService(paramHttpServletRequest, paramHttpServletResponse).getImpInitDatas(ParamUtil.request2Map(paramHttpServletRequest)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/doContactLogImp")
/*     */   @Produces({"text/plain"})
/*     */   public String doContactLogImp(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 163 */     paramHttpServletResponse.setCharacterEncoding("utf-8");
/* 164 */     return JSONObject.toJSONString(getService(paramHttpServletRequest, paramHttpServletResponse).doContactLogImp(ParamUtil.request2Map(paramHttpServletRequest)));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/crm/web/ContactLogAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */