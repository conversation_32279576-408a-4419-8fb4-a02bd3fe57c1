/*    */ package com.engine.doc.service.impl;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.doc.cmd.maintaince.DocMaintainceBaseSaveCmd;
/*    */ import com.engine.doc.cmd.maintaince.DocMaintainceCodeRuleSaveCmd;
/*    */ import com.engine.doc.cmd.maintaince.DocMaintainceDefaultAuthSaveCmd;
/*    */ import com.engine.doc.cmd.maintaince.DocMaintainceInitCmd;
/*    */ import com.engine.doc.cmd.maintaince.DocMaintainceTemplateSaveCmd;
/*    */ import java.util.Map;
/*    */ 
/*    */ public class DocMultiMaintainServiceImpl extends Service implements DocMultiMaintainService {
/*    */   public Map<String, Object> init(Map<String, Object> paramMap) {
/* 12 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DocMaintainceInitCmd(paramMap, this.user));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> initAuth(Map<String, Object> paramMap) {
/* 17 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DocMaintainceAuthInitCmd(paramMap, this.user));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> saveBase(Map<String, Object> paramMap) {
/* 22 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DocMaintainceBaseSaveCmd(paramMap, this.user));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> saveDefaultAuth(Map<String, Object> paramMap) {
/* 27 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DocMaintainceDefaultAuthSaveCmd(paramMap, this.user));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> saveAuth(Map<String, Object> paramMap) {
/* 32 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DocMaintainceAuthSaveCmd(paramMap, this.user));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> saveEdition(Map<String, Object> paramMap) {
/* 37 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DocMaintainceEditionSaveCmd(paramMap, this.user));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> saveTemplate(Map<String, Object> paramMap) {
/* 42 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DocMaintainceTemplateSaveCmd(paramMap, this.user));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> saveCodeRule(Map<String, Object> paramMap) {
/* 47 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DocMaintainceCodeRuleSaveCmd(paramMap, this.user));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> initTemplate(Map<String, Object> paramMap) {
/* 52 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DocMaintainceTemplateInitCmd(paramMap, this.user));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/service/impl/DocMultiMaintainServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */