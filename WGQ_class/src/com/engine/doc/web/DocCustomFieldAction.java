/*    */ package com.engine.doc.web;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import com.engine.common.util.ParamUtil;
/*    */ import com.engine.common.util.ServiceUtil;
/*    */ import com.engine.doc.service.DocCustomFieldService;
/*    */ import com.engine.doc.service.impl.DocCustomFieldServiceImpl;
/*    */ import com.engine.workflow.util.CommonUtil;
/*    */ import com.google.common.collect.Maps;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import javax.ws.rs.GET;
/*    */ import javax.ws.rs.POST;
/*    */ import javax.ws.rs.Path;
/*    */ import javax.ws.rs.core.Context;
/*    */ 
/*    */ public class DocCustomFieldAction
/*    */ {
/*    */   private DocCustomFieldService getService(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 21 */     return (DocCustomFieldService)ServiceUtil.getService(DocCustomFieldServiceImpl.class, CommonUtil.getUserByRequest(paramHttpServletRequest, paramHttpServletResponse));
/*    */   }
/*    */   @GET
/*    */   @Path("/table")
/*    */   public String getTable(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*    */     Map map;
/*    */     try {
/* 28 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).getTable(ParamUtil.request2Map(paramHttpServletRequest));
/* 29 */     } catch (Exception exception) {
/* 30 */       exception.printStackTrace();
/* 31 */       map = Maps.newHashMap();
/* 32 */       map.put("api_status", Boolean.valueOf(false));
/*    */     } 
/* 34 */     return JSONObject.toJSONString(map);
/*    */   }
/*    */ 
/*    */   
/*    */   @GET
/*    */   @Path("/info")
/*    */   public String getCustomInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*    */     Map map;
/*    */     try {
/* 43 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).getCustomInfo(ParamUtil.request2Map(paramHttpServletRequest));
/* 44 */     } catch (Exception exception) {
/* 45 */       exception.printStackTrace();
/* 46 */       map = Maps.newHashMap();
/* 47 */       map.put("api_status", Boolean.valueOf(false));
/*    */     } 
/* 49 */     return JSONObject.toJSONString(map);
/*    */   }
/*    */   
/*    */   @POST
/*    */   @Path("/add")
/*    */   public String addCustomInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*    */     Map map;
/*    */     try {
/* 57 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).addCustomInfo(ParamUtil.request2Map(paramHttpServletRequest));
/* 58 */     } catch (Exception exception) {
/* 59 */       exception.printStackTrace();
/* 60 */       map = Maps.newHashMap();
/* 61 */       map.put("api_status", Boolean.valueOf(false));
/*    */     } 
/* 63 */     return JSONObject.toJSONString(map);
/*    */   }
/*    */ 
/*    */   
/*    */   @POST
/*    */   @Path("/update")
/*    */   public String editCustomInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*    */     Map map;
/*    */     try {
/* 72 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).updateCustomInfo(ParamUtil.request2Map(paramHttpServletRequest));
/* 73 */     } catch (Exception exception) {
/* 74 */       exception.printStackTrace();
/* 75 */       map = Maps.newHashMap();
/* 76 */       map.put("api_status", Boolean.valueOf(false));
/*    */     } 
/* 78 */     return JSONObject.toJSONString(map);
/*    */   }
/*    */   @POST
/*    */   @Path("/delete")
/*    */   public String deleteCustomInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*    */     Map map;
/*    */     try {
/* 85 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).deleteCustomInfo(ParamUtil.request2Map(paramHttpServletRequest));
/* 86 */     } catch (Exception exception) {
/* 87 */       exception.printStackTrace();
/* 88 */       map = Maps.newHashMap();
/* 89 */       map.put("api_status", Boolean.valueOf(false));
/*    */     } 
/* 91 */     return JSONObject.toJSONString(map);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/web/DocCustomFieldAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */