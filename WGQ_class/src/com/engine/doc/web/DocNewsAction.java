/*     */ package com.engine.doc.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import com.engine.doc.service.DocNewsService;
/*     */ import com.engine.doc.service.impl.DocNewsServiceImpl;
/*     */ import com.engine.workflow.util.CommonUtil;
/*     */ import com.google.common.collect.Maps;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.POST;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.core.Context;
/*     */ 
/*     */ public class DocNewsAction
/*     */ {
/*     */   private DocNewsService getService(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  21 */     return (DocNewsService)ServiceUtil.getService(DocNewsServiceImpl.class, CommonUtil.getUserByRequest(paramHttpServletRequest, paramHttpServletResponse));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/type/table")
/*     */   public String getTypeTable(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/*  35 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).getTypeTable(ParamUtil.request2Map(paramHttpServletRequest));
/*  36 */     } catch (Exception exception) {
/*  37 */       exception.printStackTrace();
/*  38 */       map = Maps.newHashMap();
/*  39 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/*  41 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/type/add")
/*     */   public String addType(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/*  55 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).addType(ParamUtil.request2Map(paramHttpServletRequest));
/*  56 */     } catch (Exception exception) {
/*  57 */       exception.printStackTrace();
/*  58 */       map = Maps.newHashMap();
/*  59 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/*  61 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/type/update")
/*     */   public String updateType(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/*  75 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).updateType(ParamUtil.request2Map(paramHttpServletRequest));
/*  76 */     } catch (Exception exception) {
/*  77 */       exception.printStackTrace();
/*  78 */       map = Maps.newHashMap();
/*  79 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/*  81 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/type/delete")
/*     */   public String deleteType(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/*  95 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).deleteType(ParamUtil.request2Map(paramHttpServletRequest));
/*  96 */     } catch (Exception exception) {
/*  97 */       exception.printStackTrace();
/*  98 */       map = Maps.newHashMap();
/*  99 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/* 101 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/table")
/*     */   public String getTable(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/* 115 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).getTable(ParamUtil.request2Map(paramHttpServletRequest));
/* 116 */     } catch (Exception exception) {
/* 117 */       exception.printStackTrace();
/* 118 */       map = Maps.newHashMap();
/* 119 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/* 121 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/info")
/*     */   public String getNewsInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/* 135 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).getNewsInfo(ParamUtil.request2Map(paramHttpServletRequest));
/* 136 */     } catch (Exception exception) {
/* 137 */       exception.printStackTrace();
/* 138 */       map = Maps.newHashMap();
/* 139 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/* 141 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/add")
/*     */   public String addNews(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/* 155 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).addNews(ParamUtil.request2Map(paramHttpServletRequest));
/* 156 */     } catch (Exception exception) {
/* 157 */       exception.printStackTrace();
/* 158 */       map = Maps.newHashMap();
/* 159 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/* 161 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/update")
/*     */   public String updateNews(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/* 175 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).updateNews(ParamUtil.request2Map(paramHttpServletRequest));
/* 176 */     } catch (Exception exception) {
/* 177 */       exception.printStackTrace();
/* 178 */       map = Maps.newHashMap();
/* 179 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/* 181 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/delete")
/*     */   public String deleteNews(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/* 195 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).deleteNews(ParamUtil.request2Map(paramHttpServletRequest));
/* 196 */     } catch (Exception exception) {
/* 197 */       exception.printStackTrace();
/* 198 */       map = Maps.newHashMap();
/* 199 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/* 201 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/pic/table")
/*     */   public String getNewsPicTable(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/* 215 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).getNewsPicTable(ParamUtil.request2Map(paramHttpServletRequest));
/* 216 */     } catch (Exception exception) {
/* 217 */       exception.printStackTrace();
/* 218 */       map = Maps.newHashMap();
/* 219 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/* 221 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/pic/info")
/*     */   public String getNewsPicInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/* 235 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).getNewsPicInfo(ParamUtil.request2Map(paramHttpServletRequest));
/* 236 */     } catch (Exception exception) {
/* 237 */       exception.printStackTrace();
/* 238 */       map = Maps.newHashMap();
/* 239 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/* 241 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/pic/save")
/*     */   public String saveNewsPic(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/* 255 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).saveNewsPic(ParamUtil.request2Map(paramHttpServletRequest));
/* 256 */     } catch (Exception exception) {
/* 257 */       exception.printStackTrace();
/* 258 */       map = Maps.newHashMap();
/* 259 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/* 261 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/pic/delete")
/*     */   public String deleteNewsPic(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/* 275 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).deleteNewsPic(ParamUtil.request2Map(paramHttpServletRequest));
/* 276 */     } catch (Exception exception) {
/* 277 */       exception.printStackTrace();
/* 278 */       map = Maps.newHashMap();
/* 279 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/* 281 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/picsys")
/*     */   public String getNewsPicSysInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/* 294 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).getNewsPicSysInfo(ParamUtil.request2Map(paramHttpServletRequest));
/* 295 */     } catch (Exception exception) {
/* 296 */       exception.printStackTrace();
/* 297 */       map = Maps.newHashMap();
/* 298 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/* 300 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/picsys")
/*     */   public String saveNewsPicSys(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map map;
/*     */     try {
/* 314 */       map = getService(paramHttpServletRequest, paramHttpServletResponse).saveNewsPicSys(ParamUtil.request2Map(paramHttpServletRequest));
/* 315 */     } catch (Exception exception) {
/* 316 */       exception.printStackTrace();
/* 317 */       map = Maps.newHashMap();
/* 318 */       map.put("api_status", Boolean.valueOf(false));
/*     */     } 
/* 320 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/web/DocNewsAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */