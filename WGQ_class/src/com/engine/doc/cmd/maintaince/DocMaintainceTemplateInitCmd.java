/*    */ package com.engine.doc.cmd.maintaince;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONArray;
/*    */ import com.api.browser.bean.BrowserBean;
/*    */ import com.api.browser.util.BrowserInitUtil;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.doc.cmd.secCategoryInfo.DocTemplateEditCmd;
/*    */ import com.google.common.collect.Maps;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ public class DocMaintainceTemplateInitCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public DocMaintainceTemplateInitCmd(Map<String, Object> paramMap, User paramUser) {
/* 22 */     this.params = paramMap;
/* 23 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 34 */     HashMap<String, Boolean> hashMap = Maps.newHashMap();
/* 35 */     hashMap.put("api_status", Boolean.valueOf(true));
/*    */     
/*    */     try {
/* 38 */       boolean bool = HrmUserVarify.checkUserRight("DocSecCategoryEdit:Edit", this.user);
/* 39 */       hashMap.put("canEdit", Boolean.valueOf(bool));
/*    */       
/* 41 */       JSONArray jSONArray = DocTemplateEditCmd.createTemplateColumns(this.user);
/* 42 */       hashMap.put("columns", jSONArray);
/*    */       
/* 44 */       BrowserInitUtil browserInitUtil = new BrowserInitUtil();
/* 45 */       BrowserBean browserBean1 = new BrowserBean("docTemplate", SystemEnv.getHtmlLabelName(16369, this.user.getLanguage()));
/* 46 */       browserBean1.setHasAdvanceSerach(false);
/* 47 */       browserInitUtil.initBrowser(browserBean1, this.user.getLanguage());
/* 48 */       hashMap.put("docEditMouldBrowser", browserBean1);
/* 49 */       BrowserBean browserBean2 = new BrowserBean("docViewMould", SystemEnv.getHtmlLabelName(16370, this.user.getLanguage()));
/* 50 */       browserBean2.setHasAdvanceSerach(false);
/* 51 */       browserInitUtil.initBrowser(browserBean2, this.user.getLanguage());
/* 52 */       hashMap.put("docViewMouldBrowser", browserBean2);
/* 53 */     } catch (Exception exception) {
/* 54 */       exception.printStackTrace();
/* 55 */       hashMap.put("api_status", Boolean.valueOf(false));
/*    */     } 
/* 57 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 62 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/cmd/maintaince/DocMaintainceTemplateInitCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */