/*     */ package com.engine.doc.cmd.news;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4Doc;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.google.common.collect.Lists;
/*     */ import com.google.common.collect.Maps;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.news.DocNewsComInfo;
/*     */ import weaver.docs.news.DocNewsManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class DocNewsDeleteCmd extends AbstractCommonCommand<Map<String, Object>> {
/*  27 */   private List<JSONObject> logParams = Lists.newArrayList(); private boolean markLog = true;
/*     */   
/*     */   public DocNewsDeleteCmd(Map<String, Object> paramMap, User paramUser) {
/*  30 */     this.params = paramMap;
/*  31 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  42 */     HashMap<String, Boolean> hashMap = Maps.newHashMap();
/*     */     try {
/*  44 */       if (!HrmUserVarify.checkUserRight("DocFrontpageEdit:Delete", this.user)) {
/*  45 */         hashMap.put("api_status", Boolean.valueOf(false));
/*  46 */         hashMap.put("msg", SystemEnv.getHtmlLabelName(2012, this.user.getLanguage()));
/*  47 */         this.markLog = false;
/*  48 */         return (Map)hashMap;
/*     */       } 
/*  50 */       String str = Util.null2String(this.params.get("id"));
/*  51 */       for (String str1 : str.split(",")) {
/*  52 */         RecordSet recordSet = new RecordSet();
/*  53 */         recordSet.executeSql("select * from DocFrontpage where id=" + str1);
/*  54 */         String str2 = "";
/*  55 */         if (recordSet.next()) {
/*  56 */           str2 = Util.fromScreen(recordSet.getString("frontpagename"), this.user.getLanguage());
/*     */         }
/*     */         
/*  59 */         JSONObject jSONObject = new JSONObject();
/*  60 */         jSONObject.put("id", str1);
/*  61 */         jSONObject.put("name", str2);
/*  62 */         this.logParams.add(jSONObject);
/*     */         
/*  64 */         DocNewsManager docNewsManager = new DocNewsManager();
/*  65 */         docNewsManager.resetParameter();
/*  66 */         docNewsManager.setClientip(Util.null2String(this.params.get("param_ip")));
/*  67 */         docNewsManager.setUserid(this.user.getUID());
/*     */         
/*  69 */         docNewsManager.setId(Util.getIntValue(str1));
/*  70 */         docNewsManager.setFrontpagename(str2);
/*     */         
/*  72 */         docNewsManager.setAction("delete");
/*  73 */         docNewsManager.DeleteDocNewsInfo();
/*     */       } 
/*  75 */       (new DocNewsComInfo()).removeDocNewsCache();
/*  76 */     } catch (Exception exception) {
/*  77 */       exception.printStackTrace();
/*  78 */       this.markLog = false;
/*  79 */       hashMap.put("api_status", Boolean.valueOf(false));
/*  80 */       return (Map)hashMap;
/*     */     } 
/*  82 */     hashMap.put("api_status", Boolean.valueOf(true));
/*  83 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  88 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<BizLogContext> getLogContexts() {
/*  93 */     if (!this.markLog) return null; 
/*  94 */     ArrayList<BizLogContext> arrayList = Lists.newArrayList();
/*  95 */     for (JSONObject jSONObject : this.logParams) {
/*  96 */       BizLogContext bizLogContext = new BizLogContext();
/*  97 */       bizLogContext.setDateObject(new Date());
/*  98 */       bizLogContext.setUserid(this.user.getUID());
/*  99 */       bizLogContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/* 100 */       bizLogContext.setTargetId(Util.null2String(jSONObject.get("id")));
/* 101 */       bizLogContext.setTargetName(Util.null2String(jSONObject.get("name")));
/* 102 */       bizLogContext.setLogType(BizLogType.DOC_ENGINE);
/* 103 */       bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Doc.DOC_ENGINE_NEWS);
/* 104 */       bizLogContext.setOperateType(BizLogOperateType.DELETE);
/* 105 */       bizLogContext.setDesc("Doc_NEWS_DELETE");
/* 106 */       bizLogContext.setParams(this.params);
/* 107 */       bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/* 108 */       arrayList.add(bizLogContext);
/*     */     } 
/* 110 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/cmd/news/DocNewsDeleteCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */