/*    */ package com.engine.doc.cmd.news;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import com.cloudstore.dev.api.util.TextUtil;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.google.common.collect.Maps;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.docs.tools.PicUploadManager;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ public class DocNewsPicInfoCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>> {
/*    */   public DocNewsPicInfoCmd(Map<String, Object> paramMap, User paramUser) {
/* 18 */     this.params = paramMap;
/* 19 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 30 */     HashMap<String, JSONObject> hashMap = Maps.newHashMap();
/* 31 */     String str = Util.null2String(this.params.get("id"));
/*    */     try {
/* 33 */       PicUploadManager picUploadManager = new PicUploadManager();
/* 34 */       picUploadManager.resetParameter();
/* 35 */       picUploadManager.setId(Util.getIntValue(str));
/* 36 */       picUploadManager.selectImageById();
/* 37 */       picUploadManager.next();
/* 38 */       JSONObject jSONObject = JSONObject.parseObject(JSONObject.toJSONString(picUploadManager));
/* 39 */       jSONObject.put("picname", TextUtil.toBase64ForMultilang(jSONObject.getString("picname")));
/* 40 */       hashMap.put("data", jSONObject);
/* 41 */     } catch (Exception exception) {
/* 42 */       exception.printStackTrace();
/* 43 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 44 */       return (Map)hashMap;
/*    */     } 
/* 46 */     hashMap.put("api_status", Boolean.valueOf(true));
/* 47 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 52 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/cmd/news/DocNewsPicInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */