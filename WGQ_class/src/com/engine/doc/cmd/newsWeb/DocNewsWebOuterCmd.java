/*    */ package com.engine.doc.cmd.newsWeb;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONArray;
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.google.common.collect.Maps;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.docs.news.DocNewsComInfo;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.company.CompanyComInfo;
/*    */ 
/*    */ public class DocNewsWebOuterCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>> {
/*    */   public DocNewsWebOuterCmd(Map<String, Object> paramMap, User paramUser) {
/* 18 */     this.params = paramMap;
/* 19 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 30 */     HashMap<String, String> hashMap = Maps.newHashMap();
/*    */     
/*    */     try {
/* 33 */       CompanyComInfo companyComInfo = new CompanyComInfo();
/* 34 */       String str = companyComInfo.getCompanyname("1");
/* 35 */       hashMap.put("companyName", str);
/*    */ 
/*    */       
/* 38 */       DocNewsComInfo docNewsComInfo = new DocNewsComInfo();
/* 39 */       JSONArray jSONArray = new JSONArray();
/* 40 */       while (docNewsComInfo.next()) {
/* 41 */         String str1 = docNewsComInfo.getDocNewsid();
/* 42 */         String str2 = docNewsComInfo.getDocNewsname();
/* 43 */         String str3 = docNewsComInfo.getDocNewsstatus();
/* 44 */         String str4 = docNewsComInfo.getPublishtype();
/* 45 */         if (!str4.equals("0") || 
/* 46 */           !str3.equals("1"))
/* 47 */           continue;  JSONObject jSONObject = new JSONObject();
/* 48 */         jSONObject.put("id", str1);
/* 49 */         jSONObject.put("name", str2);
/* 50 */         jSONObject.put("status", str3);
/* 51 */         jSONObject.put("publishtype", str4);
/* 52 */         jSONArray.add(jSONObject);
/*    */       } 
/* 54 */       hashMap.put("newsList", jSONArray);
/* 55 */     } catch (Exception exception) {
/* 56 */       exception.printStackTrace();
/* 57 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 58 */       return (Map)hashMap;
/*    */     } 
/* 60 */     hashMap.put("api_status", Boolean.valueOf(true));
/* 61 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 66 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/cmd/newsWeb/DocNewsWebOuterCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */