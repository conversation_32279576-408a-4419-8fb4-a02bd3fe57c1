/*    */ package com.engine.doc.cmd.mobileSetting;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.doc.util.DocApplySettingUtil;
/*    */ import com.engine.doc.util.MobileSettingUtil;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class NewFileDefaultFolderCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public NewFileDefaultFolderCmd(Map<String, Object> paramMap, User paramUser) {
/* 23 */     this.params = paramMap;
/* 24 */     this.user = paramUser;
/*    */   }
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 28 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 33 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     try {
/* 35 */       hashMap.put("api_status", Boolean.valueOf(true));
/*    */       
/* 37 */       int i = Util.getIntValue((String)this.params.get("filtertype"));
/* 38 */       String str1 = Util.null2String(this.params.get("ids"));
/* 39 */       int j = Util.getIntValue((String)this.params.get("scope"));
/*    */       
/* 41 */       String str2 = "delete from MobileDocNewFileSetting where scope = ? and (docappsettingtype is null or docappsettingtype=?)";
/* 42 */       RecordSet recordSet1 = new RecordSet();
/* 43 */       recordSet1.executeUpdate(str2, new Object[] { Integer.valueOf(j), DocApplySettingUtil.DOCCREATEAPP });
/* 44 */       while (recordSet1.next());
/* 45 */       String str3 = "delete from MobileDocNewFileCategory where scope = ? and (docappsettingtype is null or docappsettingtype=?)";
/* 46 */       RecordSet recordSet2 = new RecordSet();
/* 47 */       recordSet2.executeUpdate(str3, new Object[] { Integer.valueOf(j), DocApplySettingUtil.DOCCREATEAPP });
/* 48 */       while (recordSet2.next());
/*    */ 
/*    */       
/* 51 */       String str4 = "insert into MobileDocNewFileSetting(FilterType,scope,docappsettingtype) values(?,?,?)";
/* 52 */       RecordSet recordSet3 = new RecordSet();
/* 53 */       recordSet3.executeUpdate(str4, new Object[] { Integer.valueOf(i), Integer.valueOf(j), DocApplySettingUtil.DOCCREATEAPP });
/* 54 */       while (recordSet3.next());
/*    */ 
/*    */       
/* 57 */       List<String> list = MobileSettingUtil.comma2list(str1);
/*    */       try {
/* 59 */         RecordSet recordSet = new RecordSet();
/*    */         
/* 61 */         for (byte b = 0; b < list.size(); b++) {
/* 62 */           int k = Util.getIntValue(list.get(b));
/* 63 */           recordSet.executeUpdate("insert into MobileDocNewFileCategory(categoryid,scope,docappsettingtype) values(?,?,?)", new Object[] {
/* 64 */                 Integer.valueOf(k), Integer.valueOf(j), DocApplySettingUtil.DOCCREATEAPP });
/*    */         } 
/* 66 */       } catch (Exception exception) {
/* 67 */         exception.printStackTrace();
/* 68 */         hashMap.put("api_status", Boolean.valueOf(false));
/* 69 */         hashMap.put("msg", "error");
/*    */         
/* 71 */         writeLog("NewFileDefaultFolderCmd--->:" + exception.getMessage());
/*    */       }
/*    */       finally {}
/* 74 */     } catch (Exception exception) {
/* 75 */       exception.printStackTrace();
/* 76 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 77 */       hashMap.put("msg", "error");
/*    */       
/* 79 */       writeLog("NewFileDefaultFolderCmd--->:" + exception.getMessage());
/*    */     } 
/*    */     
/* 82 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/cmd/mobileSetting/NewFileDefaultFolderCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */