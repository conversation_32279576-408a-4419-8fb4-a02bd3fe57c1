/*     */ package com.engine.doc.cmd.docMould;
/*     */ 
/*     */ import com.api.odoc.util.OdocFileUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4Doc;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.google.common.collect.Maps;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.mould.DocMouldComInfo;
/*     */ import weaver.docs.mould.MouldManager;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ public class DocMouldSaveCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>> {
/*     */   private boolean markLog = true;
/*     */   private HttpServletRequest request;
/*     */   private int id;
/*  34 */   protected Map<String, Object> newValues = new HashMap<>();
/*  35 */   protected Map<String, Object> oldValues = new HashMap<>();
/*     */   
/*     */   public DocMouldSaveCmd(HttpServletRequest paramHttpServletRequest, User paramUser) {
/*  38 */     this.request = paramHttpServletRequest;
/*  39 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  50 */     HashMap<String, Boolean> hashMap = Maps.newHashMap();
/*     */     
/*     */     try {
/*  53 */       boolean bool1 = (new ManageDetachComInfo()).isUseDocManageDetach();
/*  54 */       boolean bool2 = HrmUserVarify.checkUserRight("DocMouldEdit:Edit", this.user);
/*  55 */       boolean bool3 = HrmUserVarify.checkUserRight("DocMouldAdd:add", this.user);
/*  56 */       String str1 = "";
/*  57 */       FileUpload fileUpload = new FileUpload(this.request);
/*  58 */       str1 = fileUpload.getParameter("operation");
/*  59 */       String str2 = Util.null2String(this.request.getParameter("subcompanyid"));
/*  60 */       if (bool1) {
/*  61 */         if (StringUtils.isBlank(str2)) {
/*  62 */           str2 = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "DocMouldAdd:add", -1);
/*     */         }
/*  64 */         CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  65 */         bool2 = (checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "DocMouldEdit:Edit", Util.getIntValue(str2, 0)) > 0);
/*  66 */         bool3 = (checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "DocMouldAdd:add", Util.getIntValue(str2, 0)) > 0);
/*     */       } 
/*  68 */       if ("add".equals(str1)) {
/*  69 */         if (!bool3) {
/*  70 */           hashMap.put("api_status", Boolean.valueOf(false));
/*  71 */           hashMap.put("msg", SystemEnv.getHtmlLabelName(2012, this.user.getLanguage()));
/*  72 */           return (Map)hashMap;
/*     */         }
/*     */       
/*     */       }
/*  76 */       else if (!bool2) {
/*  77 */         hashMap.put("api_status", Boolean.valueOf(false));
/*  78 */         hashMap.put("msg", SystemEnv.getHtmlLabelName(2012, this.user.getLanguage()));
/*  79 */         return (Map)hashMap;
/*     */       } 
/*     */ 
/*     */       
/*  83 */       int i = Util.getIntValue(Util.null2String(fileUpload.getParameter("id"), "-1"));
/*     */       
/*  85 */       if (i != -1) {
/*  86 */         getOldValue(i);
/*     */       }
/*  88 */       MouldManager mouldManager = new MouldManager();
/*  89 */       mouldManager.UploadMould(this.request);
/*     */       
/*  91 */       (new DocMouldComInfo()).removeDocMouldCache();
/*  92 */       this.id = mouldManager.getId();
/*     */       
/*     */       try {
/*  95 */         int j = OdocFileUtil.selDocMouldFileType(this.id);
/*  96 */         if (this.id > 0 && j == 2) {
/*  97 */           OdocFileUtil.addDocMouldFileByDocMould(this.id, str1);
/*     */         }
/*  99 */       } catch (Exception exception) {
/* 100 */         exception.printStackTrace();
/*     */       } 
/*     */       
/* 103 */       getNewValue(this.id, str1);
/* 104 */     } catch (Exception exception) {
/* 105 */       exception.printStackTrace();
/* 106 */       this.markLog = false;
/* 107 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 108 */       return (Map)hashMap;
/*     */     } 
/* 110 */     hashMap.put("api_status", Boolean.valueOf(true));
/* 111 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 116 */     if (!this.markLog) return null; 
/* 117 */     BizLogContext bizLogContext = new BizLogContext();
/* 118 */     bizLogContext.setDateObject(new Date());
/* 119 */     bizLogContext.setUserid(this.user.getUID());
/* 120 */     bizLogContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/* 121 */     bizLogContext.setTargetId(Util.null2String(Integer.valueOf(this.id)));
/* 122 */     bizLogContext.setTargetName(Util.null2String(this.request.getParameter("mouldname")));
/* 123 */     bizLogContext.setLogType(BizLogType.DOC_ENGINE);
/* 124 */     bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Doc.DOC_ENGINE_MOULD);
/* 125 */     String str = this.request.getParameter("operation");
/* 126 */     if (str.equals("add")) {
/* 127 */       bizLogContext.setOperateType(BizLogOperateType.ADD);
/* 128 */       bizLogContext.setDesc("Doc_MOULD_ADD");
/* 129 */       bizLogContext.setNewValues(this.newValues);
/* 130 */     } else if (str.equals("edit")) {
/* 131 */       bizLogContext.setOperateType(BizLogOperateType.UPDATE);
/* 132 */       bizLogContext.setDesc("Doc_MOULD_Update");
/* 133 */       bizLogContext.setNewValues(this.newValues);
/* 134 */       bizLogContext.setOldValues(this.oldValues);
/*     */     } else {
/* 136 */       bizLogContext.setOperateType(BizLogOperateType.DELETE);
/* 137 */       bizLogContext.setDesc("Doc_MOULD_Delete");
/*     */     } 
/* 139 */     bizLogContext.setParams(this.params);
/* 140 */     bizLogContext.setClientIp(Util.null2String(Util.getIpAddr(this.request)));
/* 141 */     return bizLogContext;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void getNewValue(int paramInt, String paramString) {
/* 149 */     String str = "select * from DocMould where id = ?";
/* 150 */     RecordSet recordSet = new RecordSet();
/* 151 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt) });
/*     */     
/* 153 */     if ("add".equals(paramString)) {
/* 154 */       if (recordSet.next()) {
/* 155 */         String[] arrayOfString = recordSet.getColumnName();
/* 156 */         for (String str1 : arrayOfString) {
/* 157 */           this.newValues.put(str1, recordSet.getString(str1));
/*     */         }
/*     */       } 
/* 160 */     } else if ("edit".equals(paramString)) {
/*     */       
/* 162 */       if (recordSet.next()) {
/* 163 */         String[] arrayOfString = recordSet.getColumnName();
/* 164 */         for (String str1 : arrayOfString) {
/* 165 */           String str2 = this.oldValues.get(str1).toString();
/* 166 */           String str3 = recordSet.getString(str1);
/* 167 */           if (!str2.equals(str3)) {
/*     */             
/* 169 */             this.newValues.put(str1, str3);
/*     */           } else {
/*     */             
/* 172 */             this.oldValues.remove(str1);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void getOldValue(int paramInt) {
/* 183 */     String str = "select * from DocMould where id = ?";
/* 184 */     RecordSet recordSet = new RecordSet();
/* 185 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt) });
/* 186 */     if (recordSet.next()) {
/* 187 */       String[] arrayOfString = recordSet.getColumnName();
/* 188 */       for (String str1 : arrayOfString)
/* 189 */         this.oldValues.put(str1, recordSet.getString(str1)); 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/cmd/docMould/DocMouldSaveCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */