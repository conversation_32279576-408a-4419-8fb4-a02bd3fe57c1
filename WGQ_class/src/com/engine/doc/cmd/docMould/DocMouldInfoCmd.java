/*    */ package com.engine.doc.cmd.docMould;
/*    */ 
/*    */ import com.cloudstore.dev.api.util.TextUtil;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.google.common.collect.Maps;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.docs.mould.MouldManager;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.company.SubCompanyComInfo;
/*    */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*    */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*    */ 
/*    */ public class DocMouldInfoCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>> {
/*    */   public DocMouldInfoCmd(Map<String, Object> paramMap, User paramUser) {
/* 21 */     this.params = paramMap;
/* 22 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 33 */     HashMap<String, Boolean> hashMap = Maps.newHashMap();
/*    */     
/*    */     try {
/* 36 */       int i = Util.getIntValue(Util.null2String(this.params.get("id")), 0);
/* 37 */       MouldManager mouldManager = new MouldManager();
/* 38 */       mouldManager.setId(i);
/* 39 */       mouldManager.getMouldInfoById();
/* 40 */       String str = Util.null2String(mouldManager.getMouldName());
/* 41 */       int j = mouldManager.getMouldType();
/*    */       
/* 43 */       int k = mouldManager.getSubcompanyid();
/* 44 */       int m = 0;
/* 45 */       boolean bool1 = (new ManageDetachComInfo()).isUseDocManageDetach() ? true : false;
/* 46 */       if (bool1 == true) {
/* 47 */         m = (new CheckSubCompanyRight()).ChkComRightByUserRightCompanyId(this.user.getUID(), "DocMouldEdit:Edit", k);
/*    */       }
/* 49 */       else if (HrmUserVarify.checkUserRight("DocMouldEdit:Edit", this.user)) {
/* 50 */         m = 2;
/*    */       } 
/* 52 */       boolean bool2 = (m > 0) ? true : false;
/* 53 */       hashMap.put("canEdit", Boolean.valueOf(bool2));
/* 54 */       hashMap.put("mouldname", TextUtil.toBase64ForMultilang(str));
/* 55 */       hashMap.put("mouldType", Integer.valueOf(j));
/* 56 */       if (j <= 1) {
/* 57 */         String str1 = mouldManager.getMouldText();
/* 58 */         hashMap.put("mouldtext", str1);
/*    */       } 
/* 60 */       hashMap.put("subcompanyid", Integer.valueOf(k));
/* 61 */       hashMap.put("subcompanyidspan", (new SubCompanyComInfo()).getSubCompanyname(k + ""));
/* 62 */       hashMap.put("id", Integer.valueOf(i));
/* 63 */       mouldManager.closeStatement();
/* 64 */     } catch (Exception exception) {
/* 65 */       exception.printStackTrace();
/* 66 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 67 */       return (Map)hashMap;
/*    */     } 
/*    */     
/* 70 */     hashMap.put("api_status", Boolean.valueOf(true));
/* 71 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 78 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/cmd/docMould/DocMouldInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */