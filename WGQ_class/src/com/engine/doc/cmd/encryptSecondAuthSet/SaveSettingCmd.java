/*     */ package com.engine.doc.cmd.encryptSecondAuthSet;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.biz.EncryptConfigBiz;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.common.entity.EncryptSecondAuthEntity;
/*     */ import com.engine.common.enums.EncryptMould;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class SaveSettingCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>> {
/*     */   public SaveSettingCmd(Map<String, Object> paramMap, User paramUser) {
/*  19 */     this.user = paramUser;
/*  20 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  25 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  30 */     String str = Util.null2String(this.params.get("type"));
/*  31 */     if (str.equals("base"))
/*  32 */       return saveBaseSetting(); 
/*  33 */     if (str.equals("del")) {
/*  34 */       return delNodeSetting();
/*     */     }
/*     */     
/*  37 */     return saveNodeSetting(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> saveBaseSetting() {
/*  44 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  46 */     String str = Util.null2String(this.params.get("itemcode"));
/*  47 */     EncryptSecondAuthEntity encryptSecondAuthEntity = new EncryptSecondAuthEntity();
/*  48 */     encryptSecondAuthEntity.setMouldCode(EncryptMould.DOCUMENT.getCode());
/*  49 */     encryptSecondAuthEntity.setItemCode(str);
/*  50 */     encryptSecondAuthEntity.setIsEnable(Util.null2String(this.params.get("secondauth")));
/*  51 */     encryptSecondAuthEntity.setDoubleAuth(Util.null2String(this.params.get("doubleauth")));
/*  52 */     encryptSecondAuthEntity.setVerifier(Util.null2String(this.params.get("verifier")));
/*  53 */     encryptSecondAuthEntity.setAuthType(Util.null2String(this.params.get("authtype")));
/*     */     
/*  55 */     (new EncryptConfigBiz()).saveEncryptSecondAuthConfig(encryptSecondAuthEntity, this.user);
/*     */     
/*  57 */     hashMap.put("status", "1");
/*  58 */     hashMap.put("message", SystemEnv.getHtmlLabelName(83551, this.user.getLanguage()));
/*  59 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> saveNodeSetting(String paramString) {
/*  64 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  65 */     String str1 = Util.null2String(this.params.get("isEnableSecondAuth"));
/*  66 */     String str2 = Util.null2String(this.params.get("isEnableDoubleAuth"));
/*  67 */     String str3 = Util.null2String(this.params.get("authVerifier"));
/*     */     
/*  69 */     String str4 = "";
/*  70 */     if (paramString.equals("add")) {
/*  71 */       String str = Util.null2String(this.params.get("selectCategoryids"));
/*  72 */       if (!str.equals(""))
/*  73 */         str4 = "update docseccategory set isEnableSecondAuth =?,isEnableDoubleAuth=?,authverifier=? where id in(" + str + ") "; 
/*  74 */     } else if (paramString.equals("edit")) {
/*  75 */       String str = Util.null2String(this.params.get("categoryids"));
/*  76 */       str4 = "update docseccategory set isEnableSecondAuth =?,isEnableDoubleAuth=?,authverifier=? where id = " + str;
/*     */     } 
/*     */     
/*  79 */     RecordSet recordSet = new RecordSet();
/*  80 */     recordSet.executeUpdate(str4, new Object[] { str1, str2, str3 });
/*  81 */     hashMap.put("status", "1");
/*  82 */     hashMap.put("message", SystemEnv.getHtmlLabelName(83551, this.user.getLanguage()));
/*  83 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> delNodeSetting() {
/*  88 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  89 */     String str = Util.null2String(this.params.get("selectKeys"));
/*     */     
/*  91 */     if (!str.equals("")) {
/*  92 */       String str1 = "update docseccategory set isEnableSecondAuth =0,isEnableDoubleAuth=0,authverifier='' where id in(" + str + ") ";
/*  93 */       RecordSet recordSet = new RecordSet();
/*  94 */       recordSet.executeUpdate(str1, new Object[0]);
/*  95 */       hashMap.put("status", "1");
/*  96 */       hashMap.put("message", SystemEnv.getHtmlLabelName(83551, this.user.getLanguage()));
/*     */     } else {
/*  98 */       hashMap.put("status", "0");
/*  99 */       hashMap.put("message", "selectKeys is null");
/*     */     } 
/*     */     
/* 102 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/cmd/encryptSecondAuthSet/SaveSettingCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */