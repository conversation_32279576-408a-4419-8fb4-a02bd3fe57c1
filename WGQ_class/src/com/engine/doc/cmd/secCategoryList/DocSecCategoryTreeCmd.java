/*     */ package com.engine.doc.cmd.secCategoryList;
/*     */ 
/*     */ import com.cloudstore.dev.api.dao.Dao_TableSqlServer;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.google.common.collect.Maps;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.category.MultiCategoryTree;
/*     */ import weaver.docs.category.security.MultiAclManager;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DocSecCategoryTreeCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public DocSecCategoryTreeCmd(Map<String, Object> paramMap, User paramUser) {
/*  32 */     this.params = paramMap;
/*  33 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  43 */     HashMap<String, Boolean> hashMap = Maps.newHashMap();
/*  44 */     String str1 = "DocSecCategoryAdd:Add";
/*  45 */     boolean bool = (Util.getIntValue(Util.null2String(this.params.get("isEdit"))) > 0) ? true : false;
/*  46 */     String str2 = Util.null2String(this.params.get("subCompanyId"));
/*  47 */     if (Util.getIntValue(str2, 0) == 0) {
/*  48 */       str2 = "";
/*     */     }
/*  50 */     String str3 = Util.null2String(this.params.get("categoryname"));
/*  51 */     String str4 = str2;
/*  52 */     boolean bool1 = (new ManageDetachComInfo()).isUseDocManageDetach();
/*  53 */     BaseBean baseBean = new BaseBean();
/*     */     
/*  55 */     String str5 = Util.null2s(baseBean.getPropValue("doc_engine_tree_async", "isopen"), "0");
/*  56 */     if (bool1) {
/*     */       try {
/*  58 */         str4 = Util.null2String((new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), str1, bool ? 0 : -1));
/*  59 */       } catch (Exception exception) {
/*  60 */         exception.printStackTrace();
/*  61 */         hashMap.put("api_status", Boolean.valueOf(false));
/*  62 */         hashMap.put("msg", SystemEnv.getHtmlLabelName(382661, this.user.getLanguage()));
/*  63 */         return (Map)hashMap;
/*     */       } 
/*     */     }
/*  66 */     int i = -1;
/*  67 */     if (!str4.equals("")) {
/*  68 */       if (str4.indexOf(",") > -1) {
/*  69 */         i = Util.getIntValue(str4.substring(0, str4.indexOf(",")), -1);
/*     */       } else {
/*  71 */         i = Util.getIntValue(str4, -1);
/*     */       } 
/*  73 */       if (!"".equals(str2) && ("," + str4 + ",").indexOf("," + str2 + ",") < 0) {
/*  74 */         str4 = "-1";
/*  75 */         str2 = "";
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  80 */     MultiAclManager multiAclManager = new MultiAclManager();
/*  81 */     if (bool1) {
/*  82 */       if (bool && Util.getIntValue(str2) > 0) {
/*  83 */         CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  84 */         int j = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), str1, Util.getIntValue(str2));
/*  85 */         if (j <= 0) {
/*  86 */           hashMap.put("treeDatas", (Boolean)new ArrayList());
/*  87 */           hashMap.put("api_status", Boolean.valueOf(true));
/*  88 */           return (Map)hashMap;
/*     */         } 
/*     */       } 
/*  91 */       multiAclManager.setHasRightSub(StringUtils.isBlank(str2) ? str4 : str2);
/*     */     } else {
/*  93 */       multiAclManager.setHasRightSub(str4);
/*     */     } 
/*     */     
/*  96 */     if (bool && bool1 && StringUtils.isBlank(str4)) {
/*  97 */       hashMap.put("treeDatas", (Boolean)new ArrayList());
/*     */     } else {
/*  99 */       MultiCategoryTree multiCategoryTree = null;
/* 100 */       String str = Util.null2s((String)this.params.get("asyncLoad"), "");
/*     */       
/* 102 */       if ("1".equals(str) && "1".equals(str5)) {
/* 103 */         List<Map<String, Object>> list = getAsyncTreeDatas(Boolean.valueOf(bool1), StringUtils.isBlank(str2) ? str4 : str2);
/* 104 */         hashMap.put("treeDatas", list);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*     */       }
/*     */       else {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 149 */         multiCategoryTree = multiAclManager.getPermittedTree(this.user.getUID(), this.user.getType(), Util.getIntValue(this.user.getSeclevel(), 0), -1, str3, Util.getIntValue(str2, i));
/*     */ 
/*     */         
/* 152 */         hashMap.put("treeDatas", (multiCategoryTree == null) ? new ArrayList() : packTree(multiCategoryTree.getTreeCategories()));
/*     */       } 
/*     */     } 
/* 155 */     hashMap.put("api_status", Boolean.valueOf(true));
/* 156 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public List<Map<String, Object>> packTree(JSONArray paramJSONArray) {
/* 160 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 161 */     for (byte b = 0; b < paramJSONArray.length(); b++) {
/*     */       try {
/* 163 */         JSONObject jSONObject = (JSONObject)paramJSONArray.get(b);
/* 164 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 165 */         JSONArray jSONArray = jSONObject.names();
/* 166 */         for (byte b1 = 0; b1 < jSONArray.length(); b1++) {
/* 167 */           String str = Util.null2String(jSONArray.get(b1));
/* 168 */           if (str.startsWith("sec_")) {
/* 169 */             JSONObject jSONObject1 = jSONObject.getJSONObject(str);
/* 170 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 171 */             hashMap1.put("categoryid", jSONObject1.getString("categoryid"));
/* 172 */             hashMap1.put("hasChildren", jSONObject1.getString("hasChildren"));
/* 173 */             hashMap1.put("hasRight", jSONObject1.getString("hasRight"));
/* 174 */             hashMap1.put("isOpen", jSONObject1.getString("isOpen"));
/* 175 */             hashMap1.put("name", jSONObject1.getString("name"));
/* 176 */             hashMap1.put("title", jSONObject1.getString("title"));
/* 177 */             hashMap1.put("__domid__", jSONObject1.getString("__domid__"));
/* 178 */             List<Map<String, Object>> list = packTree(jSONObject1.getJSONArray("submenus"));
/* 179 */             hashMap1.put("submenus", list);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 196 */             hashMap.put(str, hashMap1);
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 201 */         arrayList.add(hashMap);
/*     */       }
/* 203 */       catch (Exception exception) {
/* 204 */         writeLog("^^^^^^树结构重组异常^^^^^^^" + exception);
/*     */       } 
/*     */     } 
/* 207 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   public List<Map<String, Object>> getAsyncTreeDatas(Boolean paramBoolean, String paramString) {
/* 211 */     String str1 = Util.null2s((String)this.params.get("parentid"), "");
/* 212 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 213 */     RecordSet recordSet = new RecordSet();
/* 214 */     String str2 = recordSet.getDBType();
/* 215 */     Dao_TableSqlServer dao_TableSqlServer = new Dao_TableSqlServer();
/* 216 */     String str3 = dao_TableSqlServer.getDealWithThousandsField("t.secorder", "t.secorder", str2);
/* 217 */     String str4 = "select (select count(1) from docseccategory d where d.parentid = t.id ) count,t.*  from docseccategory t ";
/* 218 */     if (!"".equals(str1)) {
/* 219 */       str4 = str4 + " where parentid = ? order by " + str3 + " asc,t.id asc";
/* 220 */       recordSet.executeQuery(str4, new Object[] { str1 });
/*     */     } else {
/* 222 */       str4 = str4 + " where (parentid is null or parentid=0)";
/*     */       
/* 224 */       if (paramBoolean.booleanValue() && !paramString.isEmpty()) {
/* 225 */         str4 = str4 + " and" + Util.getSubINClause(paramString, "t.subcompanyid", "in");
/*     */       }
/* 227 */       str4 = str4 + " order by " + str3 + " asc,t.id asc";
/* 228 */       recordSet.executeQuery(str4, new Object[0]);
/*     */     } 
/*     */     
/* 231 */     while (recordSet.next()) {
/* 232 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 233 */       int i = recordSet.getInt("count");
/* 234 */       String str5 = recordSet.getString("id");
/* 235 */       String str6 = recordSet.getString("categoryname");
/* 236 */       int j = Util.getIntValue(recordSet.getString("parentid"), 0);
/* 237 */       if (i > 0) {
/* 238 */         hashMap.put("childs", new ArrayList());
/*     */       }
/* 240 */       hashMap.put("domid", str5);
/* 241 */       hashMap.put("haschild", (i > 0) ? "true" : "false");
/* 242 */       hashMap.put("isLeaf", Boolean.valueOf(!(i > 0)));
/* 243 */       hashMap.put("isopen", "false");
/* 244 */       hashMap.put("key", str5);
/* 245 */       hashMap.put("name", str6);
/* 246 */       hashMap.put("pid", Integer.valueOf(j));
/* 247 */       arrayList.add(hashMap);
/*     */     } 
/* 249 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 253 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/cmd/secCategoryList/DocSecCategoryTreeCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */