/*    */ package com.engine.doc.cmd.magazine;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.constant.BizLogOperateType;
/*    */ import com.engine.common.constant.BizLogSmallType;
/*    */ import com.engine.common.constant.BizLogSmallType4Doc;
/*    */ import com.engine.common.constant.BizLogType;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.google.common.collect.Lists;
/*    */ import com.google.common.collect.Maps;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Date;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class DocMagazineTypeDelCmd extends AbstractCommonCommand<Map<String, Object>> {
/* 25 */   private List<JSONObject> logParams = Lists.newArrayList(); private boolean markLog = true;
/*    */   
/*    */   public DocMagazineTypeDelCmd(Map<String, Object> paramMap, User paramUser) {
/* 28 */     this.params = paramMap;
/* 29 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 40 */     HashMap<String, Boolean> hashMap = Maps.newHashMap();
/*    */     try {
/* 42 */       if (!HrmUserVarify.checkUserRight("WebMagazine:Main", this.user)) {
/* 43 */         hashMap.put("api_status", Boolean.valueOf(false));
/* 44 */         hashMap.put("msg", SystemEnv.getHtmlLabelName(2012, this.user.getLanguage()));
/* 45 */         this.markLog = false;
/* 46 */         return (Map)hashMap;
/*    */       } 
/* 48 */       String str = Util.null2String(this.params.get("ids"));
/* 49 */       RecordSet recordSet = new RecordSet();
/* 50 */       for (String str1 : str.split(",")) {
/*    */         
/* 52 */         JSONObject jSONObject = new JSONObject();
/* 53 */         jSONObject.put("id", str1);
/* 54 */         recordSet.executeQuery("select name from WebMagazineType where id = ?", new Object[] { str1 });
/* 55 */         recordSet.next();
/* 56 */         jSONObject.put("name", recordSet.getString("name"));
/* 57 */         this.logParams.add(jSONObject);
/*    */         
/* 59 */         recordSet.executeUpdate("delete from WebMagazineType where id = ?", new Object[] { str1 });
/*    */       } 
/* 61 */     } catch (Exception exception) {
/* 62 */       exception.printStackTrace();
/* 63 */       this.markLog = false;
/* 64 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 65 */       return (Map)hashMap;
/*    */     } 
/* 67 */     hashMap.put("api_status", Boolean.valueOf(true));
/* 68 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 72 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public List<BizLogContext> getLogContexts() {
/* 77 */     if (!this.markLog) return null; 
/* 78 */     ArrayList<BizLogContext> arrayList = Lists.newArrayList();
/* 79 */     for (JSONObject jSONObject : this.logParams) {
/* 80 */       BizLogContext bizLogContext = new BizLogContext();
/* 81 */       bizLogContext.setDateObject(new Date());
/* 82 */       bizLogContext.setUserid(this.user.getUID());
/* 83 */       bizLogContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/* 84 */       bizLogContext.setTargetId(jSONObject.getString("id"));
/* 85 */       bizLogContext.setTargetName(jSONObject.getString("name"));
/* 86 */       bizLogContext.setLogType(BizLogType.DOC_ENGINE);
/* 87 */       bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Doc.DOC_ENGINE_MAGAZINE);
/* 88 */       bizLogContext.setOperateType(BizLogOperateType.DELETE);
/* 89 */       bizLogContext.setDesc("Doc_MAGAZINE_TYPE_DEL");
/* 90 */       bizLogContext.setParams(this.params);
/* 91 */       bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/* 92 */       arrayList.add(bizLogContext);
/*    */     } 
/* 94 */     return arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/cmd/magazine/DocMagazineTypeDelCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */