/*    */ package com.engine.doc.cmd.secCategoryInfo;
/*    */ 
/*    */ import com.api.browser.bean.BrowserBean;
/*    */ import com.api.browser.util.BrowserInitUtil;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.doc.util.CheckPermission;
/*    */ import com.google.common.collect.Maps;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.workflow.workflow.WorkflowComInfo;
/*    */ 
/*    */ public class DocApproveWfInfoCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>> {
/*    */   public DocApproveWfInfoCmd(Map<String, Object> paramMap, User paramUser) {
/* 20 */     this.params = paramMap;
/* 21 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 31 */     HashMap<String, String> hashMap = Maps.newHashMap();
/* 32 */     String str = Util.null2String(this.params.get("id"));
/*    */     
/*    */     try {
/* 35 */       boolean bool = CheckPermission.checkEditPermission(this.user, str);
/* 36 */       RecordSet recordSet = new RecordSet();
/* 37 */       recordSet.executeProc("Doc_SecCategory_SelectByID", str + "");
/* 38 */       recordSet.next();
/* 39 */       String str1 = Util.null2String(recordSet.getString("validityApproveWf"));
/* 40 */       String str2 = Util.null2String(recordSet.getString("invalidityApproveWf"));
/* 41 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 42 */       String str3 = workflowComInfo.getWorkflowname(str1);
/* 43 */       String str4 = workflowComInfo.getWorkflowname(str2);
/* 44 */       String str5 = Util.null2String(recordSet.getString("isOpenApproveWf"));
/* 45 */       String str6 = Util.null2String(recordSet.getString("approveWorkflowId"));
/* 46 */       String str7 = workflowComInfo.getWorkflowname(str6);
/*    */       
/* 48 */       hashMap.put("isOpenApproveWf", str5);
/* 49 */       hashMap.put("validityWfId", str1);
/* 50 */       hashMap.put("validityWfName", str3);
/* 51 */       hashMap.put("invalidityWfId", str2);
/* 52 */       hashMap.put("invalidityWfName", str4);
/* 53 */       hashMap.put("approveWorkflowId", str6);
/* 54 */       hashMap.put("approveWorkflowName", str7);
/* 55 */       hashMap.put("canEdit", Boolean.valueOf(bool));
/*    */ 
/*    */       
/* 58 */       BrowserInitUtil browserInitUtil = new BrowserInitUtil();
/* 59 */       BrowserBean browserBean = new BrowserBean("-99991");
/* 60 */       browserInitUtil.initBrowser(browserBean, this.user.getLanguage());
/* 61 */       hashMap.put("workflowTypeBrowser", browserBean);
/* 62 */     } catch (Exception exception) {
/* 63 */       exception.printStackTrace();
/* 64 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 65 */       return (Map)hashMap;
/*    */     } 
/* 67 */     hashMap.put("api_status", Boolean.valueOf(true));
/* 68 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 74 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/cmd/secCategoryInfo/DocApproveWfInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */