/*     */ package com.engine.doc.cmd.systembill;
/*     */ 
/*     */ import com.engine.voting.util.VotingOperation;
/*     */ import com.engine.voting.util.VotingShare;
/*     */ import java.sql.Timestamp;
/*     */ import java.util.Date;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.CrmViewer;
/*     */ import weaver.docs.docs.DocViewer;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.proj.PrjViewer;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.voting.VotingStatusUtils;
/*     */ import weaver.workflow.request.RequestManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Systembill241AfterAction
/*     */   extends BaseBean
/*     */   implements Action
/*     */ {
/*  26 */   protected RequestManager requestManager = null;
/*     */   
/*     */   protected RequestInfo request;
/*     */   
/*     */   public String execute(RequestInfo paramRequestInfo) {
/*  31 */     HttpServletRequest httpServletRequest = paramRequestInfo.getRequestManager().getRequest();
/*  32 */     if (httpServletRequest != null) {
/*  33 */       String str1 = paramRequestInfo.getRequestid();
/*  34 */       User user = paramRequestInfo.getRequestManager().getUser();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  66 */       String str2 = paramRequestInfo.getRequestManager().getSrc();
/*  67 */       writeLog("Systembill241AfterAction---src:" + str2);
/*     */       
/*  69 */       String str3 = paramRequestInfo.getRequestManager().getIscreate();
/*  70 */       String str4 = paramRequestInfo.getRequestManager().getNodetype();
/*  71 */       String str5 = "";
/*  72 */       writeLog("Systembill241AfterAction----isCreate:" + str3 + "---requestId:" + str1);
/*  73 */       RecordSet recordSet = new RecordSet();
/*  74 */       if (!"1".equals(str3)) {
/*  75 */         recordSet.executeQuery("select votingname from bill_VotingApprove where requestid=?", new Object[] { str1 });
/*  76 */         if (recordSet.next()) {
/*  77 */           str5 = recordSet.getString("votingname");
/*     */         }
/*     */       } 
/*     */       
/*  81 */       byte b = 2;
/*  82 */       writeLog("Systembill241AfterAction---nodeType:" + str4 + "------votingid:" + str5);
/*  83 */       if (("1".equals(str4) || "2".equals(str4) || "3".equals(str4)) && "submit".equals(str2)) {
/*     */         
/*  85 */         if (!"".equals(str5)) {
/*  86 */           Date date = new Date();
/*  87 */           long l = date.getTime();
/*  88 */           Timestamp timestamp = new Timestamp(l);
/*  89 */           String str6 = timestamp.toString().substring(0, 4) + "-" + timestamp.toString().substring(5, 7) + "-" + timestamp.toString().substring(8, 10);
/*  90 */           String str7 = timestamp.toString().substring(11, 13) + ":" + timestamp.toString().substring(14, 16) + ":" + timestamp.toString().substring(17, 19);
/*  91 */           String str8 = user.getUID() + "";
/*     */ 
/*     */           
/*  94 */           int i = 0;
/*  95 */           int j = 0;
/*  96 */           int k = 0;
/*  97 */           recordSet.executeQuery("select a.docid,a.crmid,a.projid,a.requestid,b.* from voting a,votingshare b where a.id=b.votingid and a.id=?", new Object[] { str5 });
/*     */ 
/*     */           
/* 100 */           while (recordSet.next()) {
/* 101 */             i = Util.getIntValue(recordSet.getString("docid"));
/* 102 */             j = Util.getIntValue(recordSet.getString("crmid"));
/* 103 */             k = Util.getIntValue(recordSet.getString("projid"));
/* 104 */             String str10 = Util.null2String(recordSet.getString("sharetype"));
/* 105 */             String str11 = Util.null2String(recordSet.getString("seclevel"));
/* 106 */             String str12 = Util.null2String(recordSet.getString("rolelevel"));
/* 107 */             String str13 = Util.null2String(recordSet.getString("resourceid"));
/* 108 */             String str14 = Util.null2String(recordSet.getString("subcompanyid"));
/* 109 */             String str15 = Util.null2String(recordSet.getString("departmentid"));
/* 110 */             String str16 = Util.null2String(recordSet.getString("roleid"));
/* 111 */             String str17 = Util.null2String(recordSet.getString("foralluser"));
/* 112 */             String str18 = "";
/* 113 */             if (i > 0) {
/* 114 */               str18 = i + "";
/* 115 */               str18 = str18 + b + str10;
/* 116 */               str18 = str18 + b + str11;
/* 117 */               str18 = str18 + b + str12;
/* 118 */               str18 = str18 + b + "1";
/* 119 */               str18 = str18 + b + str13;
/* 120 */               str18 = str18 + b + str14;
/* 121 */               str18 = str18 + b + str15;
/* 122 */               str18 = str18 + b + str16;
/* 123 */               str18 = str18 + b + str17;
/* 124 */               str18 = str18 + b + "0";
/* 125 */               recordSet.executeProc("DocShare_IFromDocSecCategory", str18);
/*     */             } 
/* 127 */             if (j > 0 && !str10.equals("2")) {
/* 128 */               if (str10.equals("3")) str10 = "2"; 
/* 129 */               if (str10.equals("4")) str10 = "3"; 
/* 130 */               if (str10.equals("5")) str10 = "4"; 
/* 131 */               str18 = j + "";
/* 132 */               str18 = str18 + b + str10;
/* 133 */               str18 = str18 + b + str11;
/* 134 */               str18 = str18 + b + str12;
/* 135 */               str18 = str18 + b + "1";
/* 136 */               str18 = str18 + b + str13;
/* 137 */               str18 = str18 + b + str15;
/* 138 */               str18 = str18 + b + str16;
/* 139 */               str18 = str18 + b + str17;
/* 140 */               recordSet.executeProc("CRM_ShareInfo_Insert", str18);
/*     */             } 
/* 142 */             if (k > 0 && !str10.equals("2")) {
/* 143 */               if (str10.equals("3")) str10 = "2"; 
/* 144 */               if (str10.equals("4")) str10 = "3"; 
/* 145 */               if (str10.equals("5")) str10 = "4"; 
/* 146 */               str18 = k + "";
/* 147 */               str18 = str18 + b + str10;
/* 148 */               str18 = str18 + b + str11;
/* 149 */               str18 = str18 + b + str12;
/* 150 */               str18 = str18 + b + "1";
/* 151 */               str18 = str18 + b + str13;
/* 152 */               str18 = str18 + b + str15;
/* 153 */               str18 = str18 + b + str16;
/* 154 */               str18 = str18 + b + str17;
/*     */               
/* 156 */               recordSet.executeProc("Prj_ShareInfo_Insert", str18);
/*     */             } 
/*     */           } 
/* 159 */           if (i > 0) {
/* 160 */             DocViewer docViewer = new DocViewer();
/*     */             try {
/* 162 */               docViewer.setDocShareByDoc("" + i);
/* 163 */             } catch (Exception exception) {
/* 164 */               writeLog(exception);
/*     */             } 
/*     */           } 
/* 167 */           if (j > 0) {
/* 168 */             CrmViewer crmViewer = new CrmViewer();
/*     */             try {
/* 170 */               crmViewer.setCrmShareByCrm("" + j);
/* 171 */             } catch (Exception exception) {
/* 172 */               writeLog(exception);
/*     */             } 
/*     */           } 
/* 175 */           if (k > 0) {
/* 176 */             PrjViewer prjViewer = new PrjViewer();
/*     */             try {
/* 178 */               prjViewer.setPrjShareByPrj("" + k);
/* 179 */             } catch (Exception exception) {
/* 180 */               writeLog(exception);
/*     */             } 
/*     */           } 
/*     */ 
/*     */           
/* 185 */           VotingOperation.updateStatus(Util.getIntValue(str5), 1);
/* 186 */           String str9 = "update voting set approverid=" + str8 + ",approvedate='" + str6 + "',approvetime='" + str7 + "' where id=" + str5;
/* 187 */           recordSet.executeUpdate(str9, new Object[0]);
/*     */           
/* 189 */           VotingShare votingShare = new VotingShare();
/* 190 */           votingShare.updateVotingData(Util.getIntValue(str5));
/*     */ 
/*     */           
/* 193 */           recordSet.executeQuery("select autoshowvote from voting where id = ?", new Object[] { str5 });
/* 194 */           if (recordSet.next()) {
/* 195 */             String str = recordSet.getString("autoshowvote");
/* 196 */             if ("on".equals(str)) {
/* 197 */               VotingStatusUtils.setVotingCacheByVotingId(str5);
/*     */             }
/*     */           }
/*     */         
/*     */         } 
/* 202 */       } else if (str2.equals("reject")) {
/*     */         
/* 204 */         VotingOperation.updateStatus(Util.getIntValue(str5), 0);
/*     */       }
/* 206 */       else if ("submit".equals(str2)) {
/*     */         
/* 208 */         VotingOperation.updateStatus(Util.getIntValue(str5), 3);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 213 */     return "1";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/cmd/systembill/Systembill241AfterAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */