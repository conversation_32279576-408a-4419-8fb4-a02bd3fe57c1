/*    */ package com.engine.doc.cmd.systembill;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.hrm.User;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.workflow.request.RequestManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Systembill200AfterAction
/*    */   extends BaseBean
/*    */   implements Action
/*    */ {
/* 17 */   protected RequestManager requestManager = null;
/*    */   protected RequestInfo request;
/*    */   
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 21 */     String str1 = paramRequestInfo.getRequestManager().getSrc();
/* 22 */     String str2 = paramRequestInfo.getRequestManager().getIscreate();
/* 23 */     HttpServletRequest httpServletRequest = paramRequestInfo.getRequestManager().getRequest();
/* 24 */     if (httpServletRequest != null) {
/* 25 */       String str = paramRequestInfo.getRequestid();
/* 26 */       User user = paramRequestInfo.getRequestManager().getUser();
/* 27 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 28 */       hashMap.put("src", str1);
/* 29 */       hashMap.put("iscreate", str2);
/* 30 */       hashMap.put("requestid", str);
/* 31 */       hashMap.put("isneedsave", "notneedsave");
/* 32 */       hashMap.put("workflowid", Integer.valueOf(paramRequestInfo.getRequestManager()
/* 33 */             .getWorkflowid()));
/* 34 */       hashMap.put("workflowtype", paramRequestInfo.getRequestManager()
/* 35 */           .getWorkflowtype());
/* 36 */       hashMap.put("isremark", Integer.valueOf(paramRequestInfo.getRequestManager()
/* 37 */             .getIsremark()));
/* 38 */       hashMap.put("formid", Integer.valueOf(paramRequestInfo.getRequestManager().getFormid()));
/* 39 */       hashMap.put("isbill", Integer.valueOf(paramRequestInfo.getRequestManager().getIsbill()));
/* 40 */       hashMap.put("billid", Integer.valueOf(paramRequestInfo.getRequestManager().getBillid()));
/* 41 */       hashMap.put("nodeid", Integer.valueOf(paramRequestInfo.getRequestManager().getNodeid()));
/* 42 */       hashMap.put("nodetype", paramRequestInfo.getRequestManager()
/* 43 */           .getNodetype());
/* 44 */       hashMap.put("requestname", paramRequestInfo.getRequestManager()
/* 45 */           .getRequestname());
/* 46 */       hashMap.put("requestlevel", paramRequestInfo.getRequestManager()
/* 47 */           .getRequestlevel());
/* 48 */       hashMap.put("messageType", paramRequestInfo.getRequestManager()
/* 49 */           .getMessageType());
/* 50 */       hashMap.put("remark", paramRequestInfo.getRequestManager().getRemark());
/*    */     } 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 56 */     return "1";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/doc/cmd/systembill/Systembill200AfterAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */