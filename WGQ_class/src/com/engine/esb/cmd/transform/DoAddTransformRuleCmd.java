/*     */ package com.engine.esb.cmd.transform;
/*     */ 
/*     */ import com.api.integration.util.JavaUtil;
/*     */ import com.engine.common.biz.SimpleBizLogger;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4Esb;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.esb.bean.transform.EsbTransformBean;
/*     */ import com.engine.esb.bean.transform.TransformParamBean;
/*     */ import com.engine.esb.constant.EsbSql;
/*     */ import com.engine.esb.enums.EsbEnum;
/*     */ import com.engine.esb.util.EsbUtil;
/*     */ import com.engine.integration.cmd.BaseCmd;
/*     */ import com.engine.integration.constant.Message;
/*     */ import com.engine.integration.constant.MessageCode;
/*     */ import java.util.List;
/*     */ import org.apache.commons.beanutils.BeanUtils;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoAddTransformRuleCmd
/*     */   extends BaseCmd<Message>
/*     */ {
/*  45 */   private final Logger log = LoggerFactory.getLogger(DoAddTransformRuleCmd.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private EsbTransformBean bean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<TransformParamBean> list;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SimpleBizLogger logger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DoAddTransformRuleCmd(User paramUser, EsbTransformBean paramEsbTransformBean, List<TransformParamBean> paramList) {
/*  69 */     super(paramUser, DoAddTransformRuleCmd.class);
/*  70 */     this.bean = paramEsbTransformBean;
/*  71 */     this.list = paramList;
/*     */   }
/*     */ 
/*     */   
/*     */   protected String getRightKey() {
/*  76 */     return "ESBApplicationCenter:transformmanage";
/*     */   }
/*     */ 
/*     */   
/*     */   protected String getNonstandardStatus() {
/*  81 */     return "099";
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  86 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<BizLogContext> getLogContexts() {
/*  96 */     if (this.logger == null) {
/*  97 */       return null;
/*     */     }
/*  99 */     return this.logger.getBizLogContexts();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void before() {
/* 108 */     this.logger = new SimpleBizLogger();
/*     */     
/*     */     try {
/* 111 */       this.params = BeanUtils.describe(this.bean);
/* 112 */     } catch (Exception exception) {
/* 113 */       printError(exception);
/*     */     } 
/*     */     
/* 116 */     BizLogContext bizLogContext = new BizLogContext();
/*     */     
/* 118 */     bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4Esb.INTEGRATION_ENGINE_ESB_TRANSFORM);
/* 119 */     bizLogContext.setLogType(BizLogType.ESB_ENGINE);
/* 120 */     bizLogContext.setBelongTypeTargetName(SystemEnv.getHtmlLabelName(456, this.user.getLanguage()));
/* 121 */     bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Esb.INTEGRATION_ENGINE_ESB_TRANSFORM);
/* 122 */     bizLogContext.setOperateType(BizLogOperateType.ADD);
/* 123 */     bizLogContext.setParams(this.params);
/*     */     
/* 125 */     this.logger.setUser(this.user);
/* 126 */     this.logger.setParams(this.params);
/* 127 */     String str1 = "SELECT * FROM esb_transform WHERE transformCode = '" + StringEscapeUtils.escapeSql(this.bean.getTransformCode()) + "'";
/* 128 */     this.logger.setMainSql(str1, "transformcode");
/* 129 */     this.logger.setMainTargetNameColumn("transformname");
/*     */     
/* 131 */     SimpleBizLogger.SubLogInfo subLogInfo = this.logger.getNewSubLogInfo();
/* 132 */     String str2 = "select * from esb_transform_params WHERE transformCode = '" + StringEscapeUtils.escapeSql(this.bean.getTransformCode()) + "'";
/* 133 */     subLogInfo.setSubSql(str2, "paramkey");
/* 134 */     subLogInfo.setSubTargetNameColumn("paramname");
/* 135 */     subLogInfo.setGroupId("0");
/* 136 */     subLogInfo.setSubGroupNameLabel(84026);
/* 137 */     this.logger.addSubLogInfo(subLogInfo);
/*     */     
/* 139 */     this.logger.before(bizLogContext);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Message execute(CommandContext paramCommandContext) {
/* 146 */     if (!isNonstandardStatus()) {
/* 147 */       return MessageCode.CLOSE.getMessage();
/*     */     }
/*     */     
/* 150 */     if (!isRight()) {
/* 151 */       return MessageCode.NO_RIGHT.getMessage().setError(SystemEnv.getHtmlLabelName(2012, this.language));
/*     */     }
/* 153 */     if (this.bean == null || EsbUtil.isExistTransform(this.bean.getTransformCode())) {
/* 154 */       return MessageCode.EXIST.getMessage().setError(SystemEnv.getHtmlLabelName(32323, this.language));
/*     */     }
/*     */     
/* 157 */     before();
/* 158 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*     */     try {
/* 160 */       recordSetTrans.setAutoCommit(Boolean.FALSE.booleanValue());
/* 161 */       recordSetTrans.executeUpdate(EsbSql.ADD_TRANSFORM, new Object[] { this.bean
/* 162 */             .getTransformCode(), this.bean
/* 163 */             .getTransformName(), this.bean
/* 164 */             .getProductCode(), this.bean
/* 165 */             .getModuleCode(), this.bean
/* 166 */             .getResourceId(), 
/* 167 */             Integer.valueOf(this.bean.getTransformType()), this.bean
/* 168 */             .getTransformMethod(), this.bean
/* 169 */             .getDescription(), EsbEnum.ESB_TRANSFORM_NORMAL_STATUS
/* 170 */             .getValue() });
/*     */ 
/*     */ 
/*     */       
/* 174 */       if (this.list != null) {
/* 175 */         for (TransformParamBean transformParamBean : this.list) {
/* 176 */           recordSetTrans.executeUpdate(EsbSql.ADD_TRANSFORM_PARAMS, new Object[] { this.bean
/* 177 */                 .getTransformCode(), this.bean
/* 178 */                 .getProductCode(), transformParamBean
/* 179 */                 .getParamKey(), transformParamBean
/* 180 */                 .getParamName(), transformParamBean
/* 181 */                 .getShowName(), transformParamBean
/* 182 */                 .getParamType(), transformParamBean
/* 183 */                 .getParentName(), 
/* 184 */                 Integer.valueOf(transformParamBean.isArrs() ? 1 : 0), 
/* 185 */                 Integer.valueOf(transformParamBean.isRequired() ? 1 : 0), transformParamBean
/* 186 */                 .getLevels(), transformParamBean
/* 187 */                 .getIsAnaly(), transformParamBean
/* 188 */                 .getExt() });
/*     */         } 
/*     */       }
/*     */       
/* 192 */       recordSetTrans.commit();
/* 193 */       return MessageCode.SUCCESS.getMessage().setMessage(SystemEnv.getHtmlLabelName(18758, this.language));
/* 194 */     } catch (Exception exception) {
/* 195 */       recordSetTrans.rollback();
/* 196 */       this.log.error(JavaUtil.getExceptionDetail(exception));
/* 197 */       printError(exception, new String[] { EsbSql.ADD_TRANSFORM, EsbSql.ADD_TRANSFORM_PARAMS }, new Object[] { this.bean, this.list });
/*     */       
/* 199 */       return MessageCode.ERROR.getMessage().setError(SystemEnv.getHtmlLabelName(21809, this.language));
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/esb/cmd/transform/DoAddTransformRuleCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */