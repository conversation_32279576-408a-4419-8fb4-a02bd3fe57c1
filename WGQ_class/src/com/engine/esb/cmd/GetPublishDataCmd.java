/*    */ package com.engine.esb.cmd;
/*    */ 
/*    */ import com.api.integration.esb.bean.PublishBean;
/*    */ import com.api.integration.util.RecordSetObj;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.esb.constant.EsbSql;
/*    */ import com.engine.integration.cmd.BaseCmd;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetPublishDataCmd
/*    */   extends BaseCmd<PublishBean>
/*    */ {
/*    */   private String publishId;
/*    */   
/*    */   public GetPublishDataCmd(User paramUser, String paramString) {
/* 32 */     super(paramUser, GetPublishDataCmd.class);
/* 33 */     this.publishId = Util.null2String(paramString).trim();
/*    */   }
/*    */ 
/*    */   
/*    */   protected String getRightKey() {
/* 38 */     return "esb:all";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 48 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public PublishBean execute(CommandContext paramCommandContext) {
/* 53 */     if (isRight()) {
/* 54 */       RecordSetObj recordSetObj = new RecordSetObj();
/* 55 */       boolean bool = recordSetObj.executeQuery(EsbSql.SELECT_PUBLISH, new Object[] { this.publishId });
/* 56 */       if (bool && recordSetObj.next()) {
/* 57 */         return (PublishBean)recordSetObj.getBean(PublishBean.class);
/*    */       }
/*    */     } 
/* 60 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/esb/cmd/GetPublishDataCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */