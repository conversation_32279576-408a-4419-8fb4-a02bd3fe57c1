/*     */ package com.engine.esb.cmd.DataInit;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.integration.esb.constant.EsbConstant;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.esb.bean.product.EsbProductSettingBean;
/*     */ import com.engine.esb.util.EsbDataImportUtil;
/*     */ import com.engine.esb.util.EsbUtil;
/*     */ import com.engine.integration.cmd.BaseCmd;
/*     */ import com.engine.integration.constant.Message;
/*     */ import com.engine.integration.constant.MessageCode;
/*     */ import java.io.File;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetDataInitTypesCmd
/*     */   extends BaseCmd<Message>
/*     */ {
/*  38 */   private final Logger log = LoggerFactory.getLogger(GetDataInitTypesCmd.class);
/*     */   
/*  40 */   private static final String TRANSFORM_TEMP_DIR = EsbConstant.ESB_DATA_INIT_PATH + "fileTransfer" + File.separator;
/*     */ 
/*     */   
/*  43 */   private String tempProdVal = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public GetDataInitTypesCmd(User paramUser, Map<String, Object> paramMap) {
/*  52 */     super(paramUser, GetDataInitTypesCmd.class);
/*  53 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   protected String getRightKey() {
/*  58 */     return "ESBConfigurationCenter:datainit";
/*     */   }
/*     */ 
/*     */   
/*     */   protected String getNonstandardStatus() {
/*  63 */     return "099";
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  68 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Message execute(CommandContext paramCommandContext) {
/*  73 */     JSONObject jSONObject = new JSONObject();
/*  74 */     JSONArray jSONArray1 = new JSONArray();
/*  75 */     JSONArray jSONArray2 = new JSONArray();
/*  76 */     String str1 = Util.null2String(this.params.get("fileName"));
/*  77 */     if (str1.endsWith(".zip")) {
/*  78 */       str1 = StringUtils.substringBeforeLast(str1, ".zip");
/*     */     }
/*  80 */     String str2 = TRANSFORM_TEMP_DIR + str1 + "Temp" + File.separator;
/*  81 */     File file = new File(str2);
/*  82 */     if (!file.exists()) {
/*  83 */       this.log.info("文件不存在");
/*  84 */       return MessageCode.ERROR.getMessage();
/*     */     } 
/*     */     
/*  87 */     String str3 = Util.null2String(this.params.get("replaceDatas"));
/*  88 */     JSONArray jSONArray3 = JSONArray.parseArray(str3);
/*  89 */     for (byte b = 0; b < jSONArray3.size(); b++) {
/*  90 */       JSONObject jSONObject1 = jSONArray3.getJSONObject(b);
/*  91 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  92 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  93 */       String str = "";
/*  94 */       Iterator<String> iterator = jSONObject1.keySet().iterator();
/*  95 */       while (iterator.hasNext()) {
/*     */         
/*  97 */         String str4 = iterator.next();
/*  98 */         String str5 = jSONObject1.getString(str4);
/*  99 */         if (str4.startsWith("_product_")) {
/* 100 */           str = str4;
/* 101 */           this.tempProdVal = str5;
/* 102 */           hashMap2.put(str, this.tempProdVal);
/*     */           continue;
/*     */         } 
/* 105 */         hashMap1.put(str4, str5);
/*     */       } 
/*     */       
/* 108 */       File[] arrayOfFile = file.listFiles();
/* 109 */       for (File file1 : arrayOfFile) {
/* 110 */         if (file1.isDirectory()) {
/* 111 */           String str4 = file1.getName();
/* 112 */           if (str.startsWith("_product_") && str.equals(str4)) {
/*     */             
/* 114 */             File[] arrayOfFile1 = file1.listFiles();
/* 115 */             jSONObject = getResultByFile(arrayOfFile1, (Map)hashMap2);
/* 116 */             JSONArray jSONArray4 = jSONObject.getJSONArray("product");
/* 117 */             if (jSONArray4 != null && jSONArray4.size() > 0) {
/* 118 */               jSONArray1.addAll((Collection)jSONArray4);
/*     */             }
/* 120 */             JSONArray jSONArray5 = jSONObject.getJSONArray("publish");
/* 121 */             if (jSONArray5 != null && jSONArray5.size() > 0) {
/* 122 */               jSONArray2.addAll((Collection)jSONArray5);
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } 
/*     */       
/* 128 */       jSONObject.put("product", jSONArray1);
/* 129 */       jSONObject.put("publish", jSONArray2);
/*     */     } 
/* 131 */     return MessageCode.SUCCESS.getMessage().setData(jSONObject);
/*     */   }
/*     */   
/*     */   private JSONObject getResultByFile(File[] paramArrayOfFile, Map<String, String> paramMap) {
/* 135 */     JSONObject jSONObject1 = new JSONObject();
/* 136 */     JSONObject jSONObject2 = new JSONObject();
/* 137 */     for (File file : paramArrayOfFile) {
/* 138 */       if (!file.isDirectory()) {
/* 139 */         String str1 = file.getName();
/* 140 */         String str2 = file.getAbsolutePath();
/* 141 */         String str3 = file.getParent();
/*     */         
/* 143 */         ArrayList<String> arrayList = new ArrayList();
/* 144 */         arrayList.add("data");
/* 145 */         arrayList.add("main");
/* 146 */         if (jSONObject2 == null || jSONObject2.size() == 0) {
/* 147 */           jSONObject2 = EsbDataImportUtil.getFileDatasByProduct(str3, arrayList);
/*     */         }
/*     */         
/* 150 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 151 */         Iterator<String> iterator = paramMap.keySet().iterator();
/* 152 */         while (iterator.hasNext()) {
/*     */           
/* 154 */           String str5 = iterator.next();
/* 155 */           String str6 = paramMap.get(str5);
/* 156 */           EsbProductSettingBean esbProductSettingBean = EsbUtil.getProductBaseInfo(str6);
/* 157 */           String str7 = esbProductSettingBean.getProductName();
/* 158 */           hashMap.put(str6, str7);
/*     */         } 
/*     */         
/* 161 */         JSONArray jSONArray1 = EsbDataImportUtil.getProductTypes(hashMap, jSONObject2, "init");
/*     */         
/* 163 */         String str4 = Util.null2String(this.params.get("conflicts"));
/* 164 */         JSONArray jSONArray2 = new JSONArray();
/* 165 */         if (StringUtils.isNotEmpty(str4)) {
/* 166 */           jSONArray2 = JSONArray.parseArray(str4);
/*     */         }
/* 168 */         judgeHandle(jSONArray1, jSONArray2);
/*     */         
/* 170 */         jSONObject1.put("product", jSONArray1);
/* 171 */         if (str1.equals("publish.xml")) {
/* 172 */           JSONArray jSONArray = EsbDataImportUtil.getFileDatasByPublish(jSONObject2, str2, str3, arrayList);
/* 173 */           judgeHandle(jSONArray, jSONArray2);
/* 174 */           jSONObject1.put("publish", jSONArray);
/*     */         } 
/*     */       } 
/*     */     } 
/* 178 */     return jSONObject1;
/*     */   }
/*     */   
/*     */   private void judgeHandle(JSONArray paramJSONArray1, JSONArray paramJSONArray2) {
/* 182 */     if (paramJSONArray1 != null && paramJSONArray1.size() > 0) {
/* 183 */       for (byte b = 0; b < paramJSONArray1.size(); b++) {
/* 184 */         JSONObject jSONObject = paramJSONArray1.getJSONObject(b);
/* 185 */         Iterator<String> iterator = jSONObject.keySet().iterator();
/* 186 */         while (iterator.hasNext()) {
/* 187 */           String str = iterator.next();
/* 188 */           JSONObject jSONObject1 = jSONObject.getJSONObject(str);
/* 189 */           JSONArray jSONArray1 = jSONObject1.getJSONArray("transformArr");
/* 190 */           JSONArray jSONArray2 = jSONObject1.getJSONArray("eventArr");
/* 191 */           JSONArray jSONArray3 = jSONObject1.getJSONArray("resourceArr");
/* 192 */           JSONArray jSONArray4 = jSONObject1.getJSONArray("constantArr");
/* 193 */           JSONArray jSONArray5 = jSONObject1.getJSONArray("serviceArr");
/* 194 */           if (paramJSONArray2.size() > 0) {
/* 195 */             for (byte b1 = 0; b1 < paramJSONArray2.size(); b1++) {
/* 196 */               JSONObject jSONObject2 = paramJSONArray2.getJSONObject(b1);
/* 197 */               if ("service".equals(jSONObject2.getString("type"))) {
/* 198 */                 replaceConflict(jSONObject2, jSONArray5);
/* 199 */               } else if ("event".equals(jSONObject2.getString("type"))) {
/* 200 */                 replaceConflict(jSONObject2, jSONArray2);
/* 201 */               } else if ("resource".equals(jSONObject2.getString("type"))) {
/* 202 */                 replaceConflict(jSONObject2, jSONArray3);
/* 203 */               } else if ("transform".equals(jSONObject2.getString("type"))) {
/* 204 */                 replaceConflict(jSONObject2, jSONArray1);
/* 205 */               } else if ("constant".equals(jSONObject2.getString("type"))) {
/* 206 */                 replaceConflict(jSONObject2, jSONArray4);
/* 207 */               } else if ("app".equals(jSONObject2.getString("type"))) {
/* 208 */                 replaceConflict(jSONObject2, paramJSONArray1, paramJSONArray2, "app");
/*     */               } 
/*     */             } 
/*     */           }
/*     */         } 
/*     */       } 
/*     */     }
/*     */   }
/*     */   
/*     */   private void replaceConflict(JSONObject paramJSONObject, JSONArray paramJSONArray1, JSONArray paramJSONArray2, String paramString) {
/* 218 */     if (StringUtils.isEmpty(paramString)) {
/* 219 */       replaceConflict(paramJSONObject, paramJSONArray1);
/* 220 */     } else if ("app".equals(paramString)) {
/* 221 */       String str1 = paramJSONObject.getString("nameCode");
/* 222 */       String str2 = paramJSONObject.getString("handleMethod");
/* 223 */       JSONArray jSONArray = paramJSONObject.getJSONArray("assign");
/* 224 */       for (byte b = 0; b < paramJSONArray1.size(); b++) {
/* 225 */         JSONObject jSONObject = paramJSONArray1.getJSONObject(b);
/* 226 */         Iterator<String> iterator = jSONObject.keySet().iterator();
/* 227 */         while (iterator.hasNext()) {
/* 228 */           String str = iterator.next();
/* 229 */           if (str.equals(str1)) {
/* 230 */             if ("2".equals(str2)) {
/* 231 */               JSONObject jSONObject1 = jSONObject.getJSONObject(str);
/* 232 */               JSONArray jSONArray1 = jSONObject1.getJSONArray("transformArr");
/* 233 */               JSONArray jSONArray2 = jSONObject1.getJSONArray("eventArr");
/* 234 */               JSONArray jSONArray3 = jSONObject1.getJSONArray("resourceArr");
/* 235 */               JSONArray jSONArray4 = jSONObject1.getJSONArray("constantArr");
/* 236 */               JSONArray jSONArray5 = jSONObject1.getJSONArray("serviceArr");
/* 237 */               if (paramJSONArray2.size() > 0) {
/* 238 */                 for (byte b1 = 0; b1 < paramJSONArray2.size(); b1++) {
/* 239 */                   JSONObject jSONObject3 = paramJSONArray2.getJSONObject(b1);
/* 240 */                   if ("service".equals(jSONObject3.getString("type"))) {
/* 241 */                     replaceConflict(jSONObject3, jSONArray5);
/* 242 */                   } else if ("event".equals(jSONObject3.getString("type"))) {
/* 243 */                     replaceConflict(jSONObject3, jSONArray2);
/* 244 */                   } else if ("resource".equals(jSONObject3.getString("type"))) {
/* 245 */                     replaceConflict(jSONObject3, jSONArray3);
/* 246 */                   } else if ("transform".equals(jSONObject3.getString("type"))) {
/* 247 */                     replaceConflict(jSONObject3, jSONArray1);
/* 248 */                   } else if ("constant".equals(jSONObject3.getString("type"))) {
/* 249 */                     replaceConflict(jSONObject3, jSONArray4);
/*     */                   } 
/*     */                 } 
/*     */               }
/* 253 */               JSONObject jSONObject2 = new JSONObject();
/* 254 */               jSONObject2.put(jSONArray.getString(1), jSONObject1);
/* 255 */               paramJSONArray1.add(jSONObject2);
/* 256 */               paramJSONArray1.remove(jSONObject); continue;
/* 257 */             }  if ("3".equals(str2)) {
/* 258 */               paramJSONArray1.remove(jSONObject);
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private void replaceConflict(JSONObject paramJSONObject, JSONArray paramJSONArray) {
/* 267 */     String str1 = paramJSONObject.getString("nameCode");
/* 268 */     String str2 = paramJSONObject.getString("handleMethod");
/* 269 */     if ("2".equals(str2)) {
/* 270 */       JSONArray jSONArray = paramJSONObject.getJSONArray("assign");
/* 271 */       if (paramJSONArray != null && paramJSONArray.size() > 0) {
/* 272 */         for (byte b = 0; b < paramJSONArray.size(); b++) {
/* 273 */           JSONObject jSONObject = paramJSONArray.getJSONObject(b);
/* 274 */           if (str1.equals(jSONObject.getString("id"))) {
/* 275 */             jSONObject.put("id", jSONArray.getString(1));
/*     */           }
/*     */         } 
/*     */       }
/* 279 */     } else if ("3".equals(str2) && 
/* 280 */       paramJSONArray != null && paramJSONArray.size() > 0) {
/* 281 */       for (byte b = 0; b < paramJSONArray.size(); b++) {
/* 282 */         JSONObject jSONObject = paramJSONArray.getJSONObject(b);
/* 283 */         if (str1.equals(jSONObject.getString("id")))
/* 284 */           paramJSONArray.remove(jSONObject); 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/esb/cmd/DataInit/GetDataInitTypesCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */