/*     */ package com.engine.portalTs.web;
/*     */ 
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import com.engine.portalTs.entity.ResultData;
/*     */ import com.engine.portalTs.entity.news.Parent;
/*     */ import com.engine.portalTs.service.impl.CustomNewsServiceImpl;
/*     */ import java.io.File;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.POST;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ public class CustomNewsAction
/*     */ {
/*     */   public CustomNewsServiceImpl getService(User paramUser) {
/*  24 */     return (CustomNewsServiceImpl)ServiceUtil.getService(CustomNewsServiceImpl.class, paramUser);
/*     */   }
/*     */ 
/*     */   
/*     */   @Path("/saveOrUpdateItem")
/*     */   @POST
/*     */   @Produces({"text/plain"})
/*     */   public String saveOrUpdateCustomPageItem(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  32 */     ResultData resultData = null;
/*     */     try {
/*  34 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  35 */       getService(user).saveOrUpdateItem(ParamUtil.request2Map(paramHttpServletRequest), user);
/*  36 */       resultData = ResultData.ok();
/*  37 */     } catch (Exception exception) {
/*  38 */       exception.printStackTrace();
/*  39 */       resultData = ResultData.error(exception.getMessage());
/*     */     } 
/*  41 */     return JSONObject.fromObject(resultData).toString();
/*     */   }
/*     */   
/*     */   @Path("/save")
/*     */   @POST
/*     */   @Produces({"text/plain"})
/*     */   public String findCustomPage(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  48 */     ResultData resultData = null;
/*     */     try {
/*  50 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  51 */       Map<String, String> map = ParamUtil.request2Map(paramHttpServletRequest);
/*  52 */       String str = paramHttpServletRequest.getRealPath("/");
/*  53 */       if (!str.endsWith(File.separator)) {
/*  54 */         str = str + File.separator;
/*     */       }
/*  56 */       map.put("realPath", str);
/*  57 */       Parent parent = getService(user).save(map, user);
/*  58 */       resultData = ResultData.ok(parent);
/*  59 */     } catch (Exception exception) {
/*  60 */       exception.printStackTrace();
/*  61 */       resultData = ResultData.error(exception.getMessage());
/*     */     } 
/*  63 */     return JSONObject.fromObject(resultData).toString();
/*     */   }
/*     */   
/*     */   @Path("/update")
/*     */   @POST
/*     */   @Produces({"text/plain"})
/*     */   public String updateCompanyNews(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  70 */     ResultData resultData = null;
/*     */     try {
/*  72 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  73 */       getService(user).update(ParamUtil.request2Map(paramHttpServletRequest), user);
/*  74 */       resultData = ResultData.ok();
/*  75 */     } catch (Exception exception) {
/*  76 */       exception.printStackTrace();
/*  77 */       resultData = ResultData.error(exception.getMessage());
/*     */     } 
/*  79 */     return JSONObject.fromObject(resultData).toString();
/*     */   }
/*     */   
/*     */   @Path("/deleteItem")
/*     */   @POST
/*     */   @Produces({"text/plain"})
/*     */   public String deleteCompanyNewsItem(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  86 */     ResultData resultData = null;
/*     */     try {
/*  88 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  89 */       getService(user).deleteItem(ParamUtil.request2Map(paramHttpServletRequest), user);
/*  90 */       resultData = ResultData.ok();
/*  91 */     } catch (Exception exception) {
/*  92 */       exception.printStackTrace();
/*  93 */       resultData = ResultData.error(exception.getMessage());
/*     */     } 
/*  95 */     return JSONObject.fromObject(resultData).toString();
/*     */   }
/*     */   
/*     */   @Path("/orderItem")
/*     */   @POST
/*     */   @Produces({"text/plain"})
/*     */   public String orderCompanyNewsItemCmd(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 102 */     ResultData resultData = null;
/*     */     try {
/* 104 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 105 */       getService(user).orderItem(ParamUtil.request2Map(paramHttpServletRequest), user);
/* 106 */       resultData = ResultData.ok();
/* 107 */     } catch (Exception exception) {
/* 108 */       exception.printStackTrace();
/* 109 */       resultData = ResultData.error(exception.getMessage());
/*     */     } 
/* 111 */     return JSONObject.fromObject(resultData).toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/portalTs/web/CustomNewsAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */