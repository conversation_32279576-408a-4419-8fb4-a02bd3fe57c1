/*     */ package com.engine.portalTs.web;
/*     */ 
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import com.engine.portalTs.entity.ResultData;
/*     */ import com.engine.portalTs.entity.commonAbstract.Parent;
/*     */ import com.engine.portalTs.service.impl.CustomCommonAbstractServiceImpl;
/*     */ import java.io.File;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.POST;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ public class CustomCommonAbstractAction
/*     */ {
/*     */   public CustomCommonAbstractServiceImpl getService(User paramUser) {
/*  23 */     return (CustomCommonAbstractServiceImpl)ServiceUtil.getService(CustomCommonAbstractServiceImpl.class, paramUser);
/*     */   }
/*     */ 
/*     */   
/*     */   @Path("/saveOrUpdateItem")
/*     */   @POST
/*     */   @Produces({"text/plain"})
/*     */   public String saveOrUpdateCustomPageItem(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  31 */     ResultData resultData = null;
/*     */     try {
/*  33 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  34 */       getService(user).saveOrUpdateItem(ParamUtil.request2Map(paramHttpServletRequest), user);
/*  35 */       resultData = ResultData.ok();
/*  36 */     } catch (Exception exception) {
/*  37 */       exception.printStackTrace();
/*  38 */       resultData = ResultData.error(exception.getMessage());
/*     */     } 
/*  40 */     return JSONObject.fromObject(resultData).toString();
/*     */   }
/*     */   
/*     */   @Path("/save")
/*     */   @POST
/*     */   @Produces({"text/plain"})
/*     */   public String findCustomPage(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  47 */     ResultData resultData = null;
/*     */     try {
/*  49 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  50 */       Map<String, String> map = ParamUtil.request2Map(paramHttpServletRequest);
/*  51 */       String str = paramHttpServletRequest.getRealPath("/");
/*  52 */       if (!str.endsWith(File.separator)) {
/*  53 */         str = str + File.separator;
/*     */       }
/*  55 */       map.put("realPath", str);
/*  56 */       Parent parent = getService(user).save(map, user);
/*  57 */       resultData = ResultData.ok(parent);
/*  58 */     } catch (Exception exception) {
/*  59 */       exception.printStackTrace();
/*  60 */       resultData = ResultData.error(exception.getMessage());
/*     */     } 
/*  62 */     return JSONObject.fromObject(resultData).toString();
/*     */   }
/*     */   
/*     */   @Path("/update")
/*     */   @POST
/*     */   @Produces({"text/plain"})
/*     */   public String updateCompanyNews(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  69 */     ResultData resultData = null;
/*     */     try {
/*  71 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  72 */       getService(user).update(ParamUtil.request2Map(paramHttpServletRequest), user);
/*  73 */       resultData = ResultData.ok();
/*  74 */     } catch (Exception exception) {
/*  75 */       exception.printStackTrace();
/*  76 */       resultData = ResultData.error(exception.getMessage());
/*     */     } 
/*  78 */     return JSONObject.fromObject(resultData).toString();
/*     */   }
/*     */   
/*     */   @Path("/deleteItem")
/*     */   @POST
/*     */   @Produces({"text/plain"})
/*     */   public String deleteCompanyNewsItem(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  85 */     ResultData resultData = null;
/*     */     try {
/*  87 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  88 */       getService(user).deleteItem(ParamUtil.request2Map(paramHttpServletRequest), user);
/*  89 */       resultData = ResultData.ok();
/*  90 */     } catch (Exception exception) {
/*  91 */       exception.printStackTrace();
/*  92 */       resultData = ResultData.error(exception.getMessage());
/*     */     } 
/*  94 */     return JSONObject.fromObject(resultData).toString();
/*     */   }
/*     */   
/*     */   @Path("/orderItem")
/*     */   @POST
/*     */   @Produces({"text/plain"})
/*     */   public String orderCompanyNewsItemCmd(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 101 */     ResultData resultData = null;
/*     */     try {
/* 103 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 104 */       getService(user).orderItem(ParamUtil.request2Map(paramHttpServletRequest), user);
/* 105 */       resultData = ResultData.ok();
/* 106 */     } catch (Exception exception) {
/* 107 */       exception.printStackTrace();
/* 108 */       resultData = ResultData.error(exception.getMessage());
/*     */     } 
/* 110 */     return JSONObject.fromObject(resultData).toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/portalTs/web/CustomCommonAbstractAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */