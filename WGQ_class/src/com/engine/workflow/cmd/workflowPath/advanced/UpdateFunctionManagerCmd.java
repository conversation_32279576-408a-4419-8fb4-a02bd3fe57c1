/*     */ package com.engine.workflow.cmd.workflowPath.advanced;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WfRightManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UpdateFunctionManagerCmd
/*     */   extends AbstractCommonCommand<String>
/*     */ {
/*     */   private int workflowid;
/*     */   private Map<String, Object> logMap;
/*     */   
/*     */   public UpdateFunctionManagerCmd(Map<String, Object> paramMap, int paramInt, User paramUser) {
/*  38 */     this.params = paramMap;
/*  39 */     this.workflowid = paramInt;
/*  40 */     this.user = paramUser;
/*  41 */     this.logMap = new HashMap<>();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public UpdateFunctionManagerCmd() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  57 */     BizLogContext bizLogContext = new BizLogContext();
/*  58 */     bizLogContext.setDateObject(new Date());
/*  59 */     bizLogContext.setUserid(this.user.getUID());
/*  60 */     bizLogContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/*  61 */     bizLogContext.setTargetId(this.workflowid + "");
/*  62 */     bizLogContext.setLogType(BizLogType.WORKFLOW_ENGINE);
/*  63 */     bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_PATH_PATHSET_FUNCTIONMANAGER);
/*  64 */     bizLogContext.setOperateType(BizLogOperateType.UPDATE);
/*  65 */     bizLogContext.setParams(this.params);
/*  66 */     bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/*  67 */     bizLogContext.setDesc(String.format(this.user.getLastname() + "" + SystemEnv.getHtmlLabelName(10005650, ThreadVarLanguage.getLang()) + " " + SystemEnv.getHtmlLabelName(18499, ThreadVarLanguage.getLang()) + "id" + SystemEnv.getHtmlLabelName(10004839, ThreadVarLanguage.getLang()) + "{" + this.workflowid + "}  ", new Object[0]));
/*  68 */     bizLogContext.setOldValues(this.logMap);
/*  69 */     bizLogContext.setNewValues(this.params);
/*  70 */     return bizLogContext;
/*     */   }
/*     */ 
/*     */   
/*     */   public String execute(CommandContext paramCommandContext) {
/*  75 */     boolean bool = (new WfRightManager()).hasPermission3(this.workflowid, 0, this.user, 1);
/*  76 */     if (!HrmUserVarify.checkUserRight("WorkflowManage:All", this.user) && !bool) {
/*  77 */       return "0";
/*     */     }
/*     */     
/*  80 */     writeLog();
/*     */     
/*  82 */     return update();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> writeLog() {
/*  92 */     String str1 = "select  nodeid,nodetype,nodename,pigeonhole,retract from  workflow_nodebase a , workflow_flownode  b   LEFT JOIN  workflow_function_manage c  on c.operatortype=b.nodeid  where (a.IsFreeNode is null or a.IsFreeNode!='1') and a.id=b.nodeid and b.workflowid=? order by b.nodeorder, b.nodetype,a.id";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  97 */     String str2 = "select isoverrb,isoveriv from workflow_base where id=?";
/*     */     
/*  99 */     RecordSet recordSet = new RecordSet();
/* 100 */     recordSet.executeQuery(str2, new Object[] { Integer.valueOf(this.workflowid) });
/* 101 */     if (recordSet.next()) {
/* 102 */       this.logMap.put("isoverrb", recordSet.getString("isoverrb"));
/* 103 */       this.logMap.put("isoveriv", recordSet.getString("isoveriv"));
/*     */     } 
/*     */     
/* 106 */     recordSet.executeQuery(str1, new Object[] { Integer.valueOf(this.workflowid) });
/*     */     
/* 108 */     while (recordSet.next()) {
/* 109 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 110 */       hashMap.put("nodetype", recordSet.getString("nodetype"));
/* 111 */       hashMap.put("nodename", recordSet.getString("nodename"));
/* 112 */       hashMap.put("pigeonhole", recordSet.getString("pigeonhole"));
/* 113 */       hashMap.put("retract", recordSet.getString("retract"));
/* 114 */       this.logMap.put(recordSet.getString("nodeid"), hashMap);
/*     */     } 
/*     */     
/* 117 */     return this.logMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String update() {
/* 129 */     String str1 = "select  nodeid from  workflow_nodebase a , workflow_flownode  b where (a.IsFreeNode is null or a.IsFreeNode!='1') and a.id=b.nodeid and b.workflowid= ?";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 135 */     String str2 = "delete from WORKFLOW_FUNCTION_MANAGE where workflowid= ?";
/*     */     
/* 137 */     RecordSet recordSet1 = new RecordSet();
/* 138 */     RecordSet recordSet2 = new RecordSet();
/* 139 */     RecordSet recordSet3 = new RecordSet();
/*     */     
/* 141 */     recordSet1.executeUpdate("update workflow_base set ISOVERRB=?,ISOVERIV=? where id=?", new Object[] {
/* 142 */           Util.null2String(this.params.get("forcedRecovery")), Util.null2String(this.params.get("forcedIntervention")), Integer.valueOf(this.workflowid) });
/* 143 */     recordSet1.executeQuery(str1, new Object[] { Integer.valueOf(this.workflowid) });
/* 144 */     if (recordSet1.getCounts() > 0) {
/*     */       
/* 146 */       String str = "delete from workflow_function_manage where workflowid = ? and (operatortype IN (SELECT id FROM workflow_nodebase WHERE (IsFreeNode is null or IsFreeNode!='1') AND workflowid= ? ) or operatortype=-1 or  operatortype=-9)";
/*     */ 
/*     */       
/* 149 */       recordSet3.executeUpdate(str, new Object[] { Integer.valueOf(this.workflowid), Integer.valueOf(this.workflowid) });
/*     */     } 
/* 151 */     while (recordSet1.next()) {
/* 152 */       int i4 = Util.getIntValue(recordSet1.getString(1));
/* 153 */       int i5 = Util.getIntValue(Util.null2String(this.params.get("node" + i4 + "_lb")), 0);
/* 154 */       int i6 = Util.getIntValue(Util.null2String(this.params.get("node" + i4 + "_cb")), 0);
/* 155 */       int i7 = i5 + i6;
/*     */       
/* 157 */       if (i7 > 2) {
/* 158 */         i7 = 2;
/* 159 */       } else if (i7 < 0) {
/* 160 */         i7 = 0;
/*     */       } 
/* 162 */       String str8 = Util.null2String(this.params.get("node" + i4 + "_ov"));
/* 163 */       String str9 = "insert into WORKFLOW_FUNCTION_MANAGE(workflowid,retract,pigeonhole,operatortype) VALUES(?,?,?,?)";
/*     */       
/* 165 */       recordSet2.executeUpdate(str9, new Object[] { Integer.valueOf(this.workflowid), i7 + "", str8, Integer.valueOf(i4) });
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 171 */     int i = Util.getIntValue(Util.null2String(this.params.get("node-9_lb")), 0);
/* 172 */     int j = Util.getIntValue(Util.null2String(this.params.get("node-9_cb")), 0);
/* 173 */     int k = i + j;
/*     */     
/* 175 */     if (k > 2) {
/* 176 */       k = 2;
/* 177 */     } else if (k < 0) {
/* 178 */       k = 0;
/*     */     } 
/* 180 */     String str3 = Util.null2String(this.params.get("node-9_ov"));
/* 181 */     String str4 = "insert into WORKFLOW_FUNCTION_MANAGE(workflowid,retract,pigeonhole,operatortype) VALUES(?,?,?,?)";
/* 182 */     recordSet2.executeUpdate(str4, new Object[] { Integer.valueOf(this.workflowid), k + "", str3, Integer.valueOf(-9) });
/* 183 */     recordSet2.executeUpdate("insert into WORKFLOW_FUNCTION_MANAGE(workflowid,operatortype) VALUES(?,-1)", new Object[] { Integer.valueOf(this.workflowid) });
/*     */ 
/*     */     
/* 186 */     int m = Util.getIntValue(Util.null2String(this.params.get("allowWithdrawalType")), 1);
/* 187 */     String str5 = (m == 1) ? Util.null2String(this.params.get("allowWithdrawalNode1")) : Util.null2String(this.params.get("allowWithdrawalNode2"));
/* 188 */     int n = Util.getIntValue(Util.null2String(this.params.get("beAllowWithdrawalType")), 0);
/* 189 */     String str6 = "";
/* 190 */     if (n == 1) {
/* 191 */       str6 = Util.null2String(this.params.get("beAllowWithdrawalNode1"));
/* 192 */     } else if (n == 2) {
/* 193 */       str6 = Util.null2String(this.params.get("beAllowWithdrawalNode2"));
/*     */     } 
/* 195 */     int i1 = Util.getIntValue(Util.null2String(this.params.get("allowRemind")), 0);
/* 196 */     int i2 = Util.getIntValue(Util.null2String(this.params.get("doPreOperate")), 0);
/* 197 */     int i3 = Util.getIntValue(Util.null2String(this.params.get("remarkRequired")), 1);
/*     */     
/* 199 */     String str7 = "";
/* 200 */     recordSet1.executeQuery("select * from workflow_requestWithdraw where workflowid = ?", new Object[] { Integer.valueOf(this.workflowid) });
/* 201 */     if (recordSet1.next()) {
/* 202 */       recordSet2.executeUpdate("update workflow_requestWithdraw set allowWithdrawalType=?,allowWithdrawalNodeids=?,beAllowWithdrawalType=?,beAllowWithdrawalNodeids=?,allowRemind=?,doPreOperate=?,remarkRequired=? where workflowid = ?", new Object[] {
/* 203 */             Integer.valueOf(m), str5, Integer.valueOf(n), str6, Integer.valueOf(i1), Integer.valueOf(i2), Integer.valueOf(i3), Integer.valueOf(this.workflowid) });
/*     */     } else {
/* 205 */       recordSet2.executeUpdate("insert into workflow_requestWithdraw(workflowid,allowWithdrawalType,allowWithdrawalNodeids,beAllowWithdrawalType,beAllowWithdrawalNodeids,allowRemind,doPreOperate,remarkRequired) values(?,?,?,?,?,?,?,?)", new Object[] {
/* 206 */             Integer.valueOf(this.workflowid), Integer.valueOf(m), str5, Integer.valueOf(n), str6, Integer.valueOf(i1), Integer.valueOf(i2), Integer.valueOf(i3)
/*     */           });
/*     */     } 
/*     */     
/* 210 */     return "1";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/workflowPath/advanced/UpdateFunctionManagerCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */