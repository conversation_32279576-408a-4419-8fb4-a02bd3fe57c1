package com.engine.workflow.publicApi;

import java.util.Map;
import weaver.hrm.User;

public interface WorkflowRequestPA {
  Object getWorkflowRequest(User paramUser, int paramInt, Map<String, Object> paramMap);
  
  Object getRequestResources(User paramUser, int paramInt, Map<String, Object> paramMap);
  
  Object getRequestStatus(User paramUser, int paramInt, Map<String, Object> paramMap);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/publicApi/WorkflowRequestPA.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */