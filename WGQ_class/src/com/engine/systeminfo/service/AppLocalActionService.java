package com.engine.systeminfo.service;

import java.util.Map;
import weaver.hrm.User;

public interface AppLocalActionService {
  Map<String, Object> addAppLocal(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> deleteAppLocal(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> updateAppLocal(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> queryAppLocal(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> openAppLocal(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> closeAppLocal(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> appLocalCondition(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> batchDisableAppLocal(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> queryAppLocalById(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getEditAppLocalForm(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> batchEnableAppLocal(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> operate(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> appListCondition(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> queryAppList(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getUrlAndAddress(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getEcodeList(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> updateAddress(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getAddAppLocalForm(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getEmSettingForm(Map<String, Object> paramMap, User paramUser);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/systeminfo/service/AppLocalActionService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */