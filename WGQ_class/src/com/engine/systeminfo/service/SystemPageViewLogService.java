package com.engine.systeminfo.service;

import java.util.Map;

public interface SystemPageViewLogService {
  Map<String, Object> insertSystemPageView(Map<String, Object> paramMap);
  
  Map<String, Object> getSystemConfigSwitch(Map<String, Object> paramMap);
  
  Map<String, Object> getSystemPageViewRecord(Map<String, Object> paramMap);
  
  Map<String, Object> getSystemPageViewType(Map<String, Object> paramMap);
  
  Map<String, Object> updateSystemConfigSwitch(Map<String, Object> paramMap);
  
  Map<String, Object> deleteSystemPageViewRecord(Map<String, Object> paramMap);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/systeminfo/service/SystemPageViewLogService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */