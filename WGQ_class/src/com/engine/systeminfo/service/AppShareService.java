package com.engine.systeminfo.service;

import java.util.Map;
import weaver.hrm.User;

public interface AppShareService {
  Map<String, Object> appShare(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getAppShareList(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> changeStatus(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> searchCondition(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> batchChangeStatus(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getEditForm(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> shareDetail(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> detailForm(Map<String, Object> paramMap, User paramUser);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/systeminfo/service/AppShareService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */