/*    */ package com.engine.systeminfo.cmd.appmanage;
/*    */ 
/*    */ import com.cloudstore.eccom.constant.WeaMessageCode;
/*    */ import com.cloudstore.eccom.result.WeaResultMsg;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.systeminfo.dao.AppManageDao;
/*    */ import java.util.Map;
/*    */ import org.apache.commons.logging.Log;
/*    */ import org.apache.commons.logging.LogFactory;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetAppById
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/* 24 */   private static final Log log = LogFactory.getLog(GetAppById.class);
/*    */   
/* 26 */   private BizLogContext bizLogContext = new BizLogContext();
/*    */   
/*    */   public GetAppById(Map<String, Object> paramMap, User paramUser) {
/* 29 */     this.params = paramMap;
/* 30 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 35 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 40 */     WeaResultMsg weaResultMsg = new WeaResultMsg(true);
/* 41 */     if (!HrmUserVarify.checkUserRight("E-MobileApp:Management", this.user)) {
/* 42 */       weaResultMsg.put("noright", Boolean.valueOf(true));
/* 43 */       return weaResultMsg.getResultMapAll();
/*    */     } 
/* 45 */     RecordSet recordSet1 = new RecordSet();
/* 46 */     RecordSet recordSet2 = new RecordSet();
/* 47 */     String str1 = Util.null2String(this.params.get("id"));
/* 48 */     String str2 = AppManageDao.getOne();
/* 49 */     String str3 = "";
/* 50 */     recordSet1.executeQuery(str2, new Object[] { str1 });
/* 51 */     if (recordSet1.next()) {
/* 52 */       weaResultMsg.put("id", recordSet1.getString("id"));
/* 53 */       weaResultMsg.put("appname", recordSet1.getString("appname"));
/* 54 */       String str4 = recordSet1.getString("typename");
/* 55 */       String str5 = SystemEnv.getHtmlLabelName(Integer.parseInt(str4), this.user.getLanguage());
/* 56 */       weaResultMsg.put("apptype", str5);
/* 57 */       weaResultMsg.put("appconfig", recordSet1.getString("appconfig"));
/* 58 */       weaResultMsg.put("description", recordSet1.getString("description"));
/* 59 */       weaResultMsg.put("img_url", recordSet1.getString("img_url"));
/* 60 */       weaResultMsg.success();
/* 61 */       return weaResultMsg.getResultMapAll();
/*    */     } 
/* 63 */     return weaResultMsg.fail("ID does not exist", WeaMessageCode.BS_NOT_FOUND.getCode()).getResultMapAll();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/systeminfo/cmd/appmanage/GetAppById.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */