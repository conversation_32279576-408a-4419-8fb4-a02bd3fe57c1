/*     */ package com.engine.systeminfo.cmd.osinfo;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4SysEngine;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.common.util.LogUtil;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.msgcenter.util.MsgPushLogUtil;
/*     */ import com.engine.systeminfo.constant.BrowserConfigConst;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.UUID;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class SavePageConfigCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>> {
/*     */   public SavePageConfigCmd(Map<String, Object> paramMap, User paramUser) {
/*  30 */     this.params = paramMap;
/*  31 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  36 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  41 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  42 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  43 */     hashMap.put("api_status", Boolean.valueOf(true));
/*  44 */     if (!HrmUserVarify.checkUserRight("BrowserDisplay:Config", this.user)) {
/*  45 */       hashMap.put("hasRight", Boolean.valueOf(false));
/*  46 */       return (Map)hashMap;
/*     */     } 
/*  48 */     hashMap.put("hasRight", Boolean.valueOf(true));
/*  49 */     String str1 = Util.null2String(this.params.get("table"));
/*  50 */     String str2 = Util.null2String(this.params.get("type"));
/*  51 */     String str3 = Util.null2String(this.params.get("pageSize"));
/*  52 */     String str4 = MsgPushLogUtil.getNowDate();
/*  53 */     String str5 = MsgPushLogUtil.getNowTime();
/*     */     try {
/*  55 */       RecordSet recordSet = new RecordSet();
/*  56 */       JSONArray jSONArray = JSONArray.parseArray(str1);
/*  57 */       for (byte b = 0; b < jSONArray.size(); b++) {
/*  58 */         JSONObject jSONObject = jSONArray.getJSONObject(b);
/*  59 */         String str6 = UUID.randomUUID().toString().replaceAll("-", "");
/*  60 */         String str7 = jSONObject.getString("type");
/*  61 */         String str8 = jSONObject.getString("name");
/*  62 */         if ("sysBrowser".equals(str2)) {
/*  63 */           str7 = jSONObject.getString("type");
/*  64 */           str8 = jSONObject.getString("name");
/*  65 */         } else if ("cusBrowser".equals(str2)) {
/*  66 */           str7 = jSONObject.getString("showname");
/*  67 */           if (!str7.startsWith("browser."))
/*  68 */             str7 = "browser." + str7; 
/*  69 */           str8 = jSONObject.getString("name");
/*     */         } 
/*     */         
/*  72 */         String str9 = "select id from ecology_browser_display_config where browserType=? and status=?";
/*  73 */         recordSet.executeQuery(str9, new Object[] { str7, BrowserConfigConst.PAGE_CONFIG_STATUS });
/*  74 */         if (recordSet.next()) {
/*  75 */           String str = recordSet.getString("id");
/*  76 */           str9 = "update ecology_browser_display_config set pageSize=? where id=?";
/*  77 */           recordSet.executeUpdate(str9, new Object[] { str3, str });
/*     */         } else {
/*     */           
/*  80 */           str9 = "insert into ecology_browser_display_config (id,browserType,browserLabel,pageSize,status,createdate,createtime) values(?,?,?,?,?, ?,?)";
/*  81 */           recordSet.executeUpdate(str9, new Object[] { str6, str7, str8, str3, BrowserConfigConst.PAGE_CONFIG_STATUS, str4, str5 });
/*     */           
/*  83 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  84 */           hashMap1.put(SystemEnv.getHtmlLabelName(505342, this.user.getLanguage()), str8);
/*  85 */           arrayList.add(hashMap1);
/*     */         } 
/*  87 */       }  writeLog((List)arrayList);
/*  88 */     } catch (Exception exception) {
/*  89 */       exception.printStackTrace();
/*  90 */       hashMap.put("exception", exception.getMessage());
/*     */     } 
/*  92 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private void writeLog(List<Map<String, Object>> paramList) throws Exception {
/*  96 */     String str1 = SystemEnv.getHtmlLabelName(1421, this.user.getLanguage());
/*  97 */     BizLogContext bizLogContext = new BizLogContext();
/*     */     
/*  99 */     bizLogContext.setDateObject(new Date());
/* 100 */     bizLogContext.setUserid(this.user.getUID());
/* 101 */     bizLogContext.setUsertype(this.user.getType());
/* 102 */     bizLogContext.setTargetId("");
/* 103 */     bizLogContext.setTargetName("");
/* 104 */     bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4SysEngine.SYSTEM_ENGINE_BROWSER_CONFIG);
/* 105 */     bizLogContext.setBelongTypeTargetId("");
/* 106 */     bizLogContext.setBelongTypeTargetName(String.valueOf(BizLogSmallType4SysEngine.SYSTEM_ENGINE_BROWSER_CONFIG));
/* 107 */     bizLogContext.setLogType(BizLogType.SYSTEM_ENGINE);
/* 108 */     bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4SysEngine.SYSTEM_ENGINE_BROWSER_CONFIG);
/* 109 */     bizLogContext.setOperateType(BizLogOperateType.ADD);
/* 110 */     bizLogContext.setParams(this.params);
/* 111 */     bizLogContext.setOldValues(new HashMap<>());
/* 112 */     bizLogContext.setNewValues(new HashMap<>());
/* 113 */     bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/* 114 */     bizLogContext.setDetail(false);
/* 115 */     String str2 = bizLogContext.createMainid();
/* 116 */     bizLogContext.setMainId(str2);
/* 117 */     bizLogContext.setGroupNameLabel(0);
/* 118 */     bizLogContext.setDesc(String.format(this.user.getLastname() + "-" + str1 + "-" + SystemEnv.getHtmlLabelName(505340, this.user.getLanguage()), new Object[0]));
/* 119 */     LogUtil.writeBizLog(bizLogContext);
/*     */     
/* 121 */     bizLogContext.setOldValues(null);
/* 122 */     bizLogContext.setDetail(true);
/* 123 */     bizLogContext.setMainId("");
/* 124 */     bizLogContext.setBelongMainId(str2);
/* 125 */     bizLogContext.setBelongTypeTargetName("");
/* 126 */     bizLogContext.setGroupId("1");
/* 127 */     bizLogContext.setGroupNameLabel(506623);
/* 128 */     bizLogContext.setDesc("");
/* 129 */     for (byte b = 0; b < paramList.size(); b++) {
/* 130 */       bizLogContext.setNewValues(paramList.get(b));
/* 131 */       LogUtil.writeBizLog(bizLogContext);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/systeminfo/cmd/osinfo/SavePageConfigCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */