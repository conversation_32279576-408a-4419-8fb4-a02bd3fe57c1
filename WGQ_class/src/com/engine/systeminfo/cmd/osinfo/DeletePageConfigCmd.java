/*     */ package com.engine.systeminfo.cmd.osinfo;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4SysEngine;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.common.util.LogUtil;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.systeminfo.constant.BrowserConfigConst;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.DBUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class DeletePageConfigCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public DeletePageConfigCmd(Map<String, Object> paramMap, User paramUser) {
/*  29 */     this.params = paramMap;
/*  30 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  35 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  40 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  41 */     hashMap.put("api_status", Boolean.valueOf(true));
/*  42 */     hashMap.put("hasRight", Boolean.valueOf(true));
/*  43 */     if (!HrmUserVarify.checkUserRight("BrowserDisplay:Config", this.user)) {
/*  44 */       hashMap.put("hasRight", Boolean.valueOf(false));
/*  45 */       return (Map)hashMap;
/*     */     } 
/*  47 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  48 */     String str1 = Util.null2String(this.params.get("ids"));
/*  49 */     if (StringUtils.isBlank(str1))
/*  50 */       return (Map)hashMap; 
/*  51 */     ArrayList arrayList1 = new ArrayList();
/*  52 */     Object[] arrayOfObject = DBUtil.transListIn(str1, arrayList1);
/*  53 */     RecordSet recordSet = new RecordSet();
/*  54 */     String str2 = "select * from ecology_browser_display_config where status=? and id in(" + arrayOfObject[0] + ")";
/*     */     try {
/*  56 */       recordSet.executeQuery(str2, new Object[] { BrowserConfigConst.PAGE_CONFIG_STATUS, arrayList1 });
/*  57 */       while (recordSet.next()) {
/*  58 */         String str3 = recordSet.getString("id");
/*  59 */         String str4 = recordSet.getString("browserType");
/*  60 */         String str5 = recordSet.getString("browserLabel");
/*  61 */         String str6 = recordSet.getString("pageSize");
/*     */         
/*  63 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  64 */         hashMap1.put(SystemEnv.getHtmlLabelName(505342, this.user.getLanguage()), str5);
/*  65 */         arrayList.add(hashMap1);
/*     */       } 
/*  67 */       str2 = "delete from ecology_browser_display_config where id in(" + arrayOfObject[0] + ")";
/*  68 */       recordSet.executeUpdate(str2, new Object[] { arrayList1 });
/*  69 */       writeLog((List)arrayList);
/*  70 */     } catch (Exception exception) {
/*  71 */       exception.printStackTrace();
/*  72 */       hashMap.put("exception", exception.getMessage());
/*     */     } 
/*  74 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private void writeLog(List<Map<String, Object>> paramList) throws Exception {
/*  78 */     BizLogContext bizLogContext = new BizLogContext();
/*     */     
/*  80 */     String str1 = SystemEnv.getHtmlLabelName(91, this.user.getLanguage());
/*  81 */     bizLogContext.setDateObject(new Date());
/*  82 */     bizLogContext.setUserid(this.user.getUID());
/*  83 */     bizLogContext.setUsertype(this.user.getType());
/*  84 */     bizLogContext.setTargetId("");
/*  85 */     bizLogContext.setTargetName("");
/*  86 */     bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4SysEngine.SYSTEM_ENGINE_BROWSER_CONFIG);
/*  87 */     bizLogContext.setBelongTypeTargetId("");
/*  88 */     bizLogContext.setBelongTypeTargetName(String.valueOf(BizLogSmallType4SysEngine.SYSTEM_ENGINE_BROWSER_CONFIG));
/*  89 */     bizLogContext.setLogType(BizLogType.SYSTEM_ENGINE);
/*  90 */     bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4SysEngine.SYSTEM_ENGINE_BROWSER_CONFIG);
/*  91 */     bizLogContext.setOperateType(BizLogOperateType.DELETE);
/*  92 */     bizLogContext.setParams(this.params);
/*  93 */     bizLogContext.setOldValues(new HashMap<>());
/*  94 */     bizLogContext.setNewValues(new HashMap<>());
/*  95 */     bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/*  96 */     bizLogContext.setDetail(false);
/*  97 */     String str2 = bizLogContext.createMainid();
/*  98 */     bizLogContext.setMainId(str2);
/*  99 */     bizLogContext.setGroupNameLabel(0);
/* 100 */     bizLogContext.setDesc(String.format(this.user.getLastname() + "-" + str1 + "-" + SystemEnv.getHtmlLabelName(505340, this.user.getLanguage()), new Object[0]));
/* 101 */     LogUtil.writeBizLog(bizLogContext);
/*     */     
/* 103 */     bizLogContext.setNewValues(null);
/* 104 */     bizLogContext.setDetail(true);
/* 105 */     bizLogContext.setMainId("");
/* 106 */     bizLogContext.setBelongMainId(str2);
/* 107 */     bizLogContext.setBelongTypeTargetName("");
/* 108 */     bizLogContext.setGroupId("1");
/* 109 */     bizLogContext.setGroupNameLabel(506623);
/* 110 */     bizLogContext.setDesc("");
/* 111 */     for (byte b = 0; b < paramList.size(); b++) {
/* 112 */       bizLogContext.setOldValues(paramList.get(b));
/* 113 */       LogUtil.writeBizLog(bizLogContext);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/systeminfo/cmd/osinfo/DeletePageConfigCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */