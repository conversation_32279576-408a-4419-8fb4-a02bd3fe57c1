/*    */ package com.engine.odoc.cmd.rightMenu;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.odoc.cmd.rightMenu.constant.RightMenuConstant;
/*    */ import com.engine.odoc.cmd.rightMenu.constant.RightMenuEnum;
/*    */ import com.engine.odoc.cmd.rightMenu.functionFactory.FunctionSetFactory;
/*    */ import com.engine.odoc.cmd.rightMenu.functionFactory.annotation.FunctionSettingAnno;
/*    */ import java.lang.reflect.Constructor;
/*    */ import java.util.HashMap;
/*    */ import java.util.LinkedHashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class LoadMenuFunctionCfgCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>> {
/*    */   public LoadMenuFunctionCfgCmd(User paramUser, Map<String, Object> paramMap) {
/* 21 */     this.user = paramUser;
/* 22 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 27 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/*    */     try {
/* 35 */       int i = this.user.getLanguage();
/* 36 */       int j = Util.getIntValue(Util.null2String(this.params.get("workflowid")));
/* 37 */       int k = Util.getIntValue(Util.null2String(this.params.get("nodeid")));
/* 38 */       String str = Util.null2String(this.params.get("type"));
/*    */       
/* 40 */       hashMap.put("dialogTitle", getDialogTitle());
/* 41 */       hashMap.putAll(getDialogCondition());
/* 42 */       hashMap.put("api_status", RightMenuConstant.SUCCESS);
/* 43 */     } catch (Exception exception) {
/* 44 */       hashMap.put("api_status", RightMenuConstant.ERROR);
/* 45 */       hashMap.put("api_msg", SystemEnv.getHtmlLabelName(10005506, this.user.getLanguage()));
/*    */     } 
/* 47 */     return (Map)hashMap;
/*    */   }
/*    */   private Map<String, Object> getDialogCondition() {
/* 50 */     Map<String, Object> map = (Map)new LinkedHashMap<>();
/* 51 */     String str = Util.null2String(this.params.get("type"));
/* 52 */     RightMenuEnum rightMenuEnum = RightMenuEnum.getRightMenuEnumByShowName(str);
/*    */     try {
/* 54 */       FunctionSettingAnno functionSettingAnno = rightMenuEnum.getClass().getField(rightMenuEnum.name()).<FunctionSettingAnno>getAnnotation(FunctionSettingAnno.class);
/* 55 */       if (functionSettingAnno != null) {
/* 56 */         Constructor<FunctionSetFactory> constructor = functionSettingAnno.target().getConstructor(new Class[] { Map.class, User.class });
/* 57 */         FunctionSetFactory functionSetFactory = constructor.newInstance(new Object[] { this.params, this.user });
/* 58 */         map = functionSetFactory.getConditions();
/*    */       } 
/* 60 */     } catch (Exception exception) {
/* 61 */       exception.printStackTrace();
/*    */     } 
/* 63 */     return map;
/*    */   }
/*    */   
/*    */   private String getDialogTitle() {
/* 67 */     int i = this.user.getLanguage();
/* 68 */     String str1 = Util.null2String(this.params.get("type"));
/*    */     
/* 70 */     RightMenuEnum rightMenuEnum = RightMenuEnum.getRightMenuEnumByShowName(str1);
/* 71 */     String str2 = "";
/* 72 */     if (rightMenuEnum != null) {
/* 73 */       str2 = SystemEnv.getHtmlLabelName(rightMenuEnum.getMenuName(), i);
/*    */     }
/* 75 */     return str2;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/odoc/cmd/rightMenu/LoadMenuFunctionCfgCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */