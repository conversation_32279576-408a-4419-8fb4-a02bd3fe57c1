/*    */ package com.engine.odoc.cmd.rightMenu;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.odoc.cmd.rightMenu.constant.RightMenuConstant;
/*    */ import com.engine.odoc.cmd.rightMenu.constant.RightMenuEnum;
/*    */ import com.engine.odoc.cmd.rightMenu.functionFactory.FunctionSetFactory;
/*    */ import com.engine.odoc.cmd.rightMenu.functionFactory.annotation.FunctionSettingAnno;
/*    */ import java.lang.reflect.Constructor;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class SaveMenuFunctionCfgCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>> {
/*    */   public SaveMenuFunctionCfgCmd(User paramUser, Map<String, Object> paramMap) {
/* 20 */     this.user = paramUser;
/* 21 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 26 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 31 */     Map<String, Boolean> map = (Map)new HashMap<>();
/* 32 */     int i = Util.getIntValue(Util.null2String(this.params.get("workflowid")));
/* 33 */     int j = Util.getIntValue(Util.null2String(this.params.get("nodeid")));
/* 34 */     int k = Util.getIntValue(Util.null2String(this.params.get("rejectType")));
/* 35 */     String str1 = Util.null2String(this.params.get("rejectNodes"));
/* 36 */     String str2 = Util.null2String(this.params.get("type"));
/* 37 */     RightMenuEnum rightMenuEnum = RightMenuEnum.getRightMenuEnumByShowName(str2);
/*    */     try {
/* 39 */       FunctionSettingAnno functionSettingAnno = rightMenuEnum.getClass().getField(rightMenuEnum.name()).<FunctionSettingAnno>getAnnotation(FunctionSettingAnno.class);
/* 40 */       if (functionSettingAnno != null) {
/* 41 */         Constructor<FunctionSetFactory> constructor = functionSettingAnno.target().getConstructor(new Class[] { Map.class, User.class });
/* 42 */         FunctionSetFactory functionSetFactory = constructor.newInstance(new Object[] { this.params, this.user });
/* 43 */         map = functionSetFactory.saveFunctionSetting();
/*    */       } else {
/* 45 */         new Exception("type is null");
/*    */       } 
/* 47 */     } catch (Exception exception) {
/* 48 */       exception.printStackTrace();
/* 49 */       map.put("api_status", RightMenuConstant.ERROR);
/* 50 */       map.put("api_msg", SystemEnv.getHtmlLabelName(10005506, this.user.getLanguage()));
/*    */     } 
/* 52 */     return (Map)map;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/odoc/cmd/rightMenu/SaveMenuFunctionCfgCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */