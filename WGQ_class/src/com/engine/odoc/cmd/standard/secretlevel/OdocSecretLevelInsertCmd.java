/*    */ package com.engine.odoc.cmd.standard.secretlevel;
/*    */ 
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.odoc.cmd.OdocAbstractCommonCommand;
/*    */ import com.engine.odoc.entity.standard.SecretLevel;
/*    */ import com.google.common.collect.Maps;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*    */ import weaver.orm.util.OrmUtil;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OdocSecretLevelInsertCmd
/*    */   extends OdocAbstractCommonCommand
/*    */ {
/*    */   private SecretLevel sl;
/*    */   
/*    */   public OdocSecretLevelInsertCmd(SecretLevel paramSecretLevel) {
/* 23 */     this.sl = paramSecretLevel;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> exe(CommandContext paramCommandContext) {
/* 28 */     HashMap<String, Boolean> hashMap = Maps.newHashMap(); try {
/*    */       String str;
/*    */       SecretLevel secretLevel;
/* 31 */       boolean bool = (new ManageDetachComInfo()).isUseOdocManageDetach();
/* 32 */       bool = false;
/*    */ 
/*    */       
/* 35 */       if (bool) {
/* 36 */         str = "select * from DocSecretLevel where name = ? and subCompanyId=?";
/* 37 */         secretLevel = (SecretLevel)OrmUtil.selectObjBySql(SecretLevel.class, str, new Object[] { this.sl.getName(), this.sl.getSubCompanyId() });
/*    */       } else {
/*    */         
/* 40 */         str = "select * from DocSecretLevel where name = ? ";
/* 41 */         secretLevel = (SecretLevel)OrmUtil.selectObjBySql(SecretLevel.class, str, new Object[] { this.sl.getName() });
/* 42 */       }  if (secretLevel != null) {
/* 43 */         hashMap.put("api_status", Boolean.valueOf(false));
/* 44 */         hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(385431, this.user.getLanguage()) + ":" + this.sl.getName() + "," + SystemEnv.getHtmlLabelName(385434, this.user.getLanguage()));
/*    */       } else {
/*    */         
/* 47 */         boolean bool1 = OrmUtil.insertObj(this.sl);
/* 48 */         hashMap.put("api_status", Boolean.valueOf(bool1));
/*    */         
/* 50 */         if (bool) {
/* 51 */           this.sl = (SecretLevel)OrmUtil.selectObjBySql(SecretLevel.class, str, new Object[] { this.sl.getName(), this.sl.getSubCompanyId() });
/*    */         } else {
/*    */           
/* 54 */           this.sl = (SecretLevel)OrmUtil.selectObjBySql(SecretLevel.class, str, new Object[] { this.sl.getName() });
/*    */         } 
/* 56 */         addBizLog(this.sl.getId() + "", this.sl.getName(), null, this.sl);
/*    */       } 
/* 58 */     } catch (Exception exception) {
/* 59 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 60 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*    */     } 
/*    */ 
/*    */     
/* 64 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/odoc/cmd/standard/secretlevel/OdocSecretLevelInsertCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */