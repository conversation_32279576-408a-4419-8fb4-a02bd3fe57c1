/*    */ package com.engine.odoc.cmd.standard.secretlevel;
/*    */ 
/*    */ import com.cloudstore.dev.api.util.Util_TableMap;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.odoc.cmd.OdocAbstractCommonCommand;
/*    */ import com.engine.odoc.util.OdocStandardUtil;
/*    */ import com.google.common.collect.Maps;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.front.tablestring.CheckBox;
/*    */ import weaver.front.tablestring.Col;
/*    */ import weaver.front.tablestring.Operate;
/*    */ import weaver.front.tablestring.OperateItem;
/*    */ import weaver.front.tablestring.Sql;
/*    */ import weaver.front.tablestring.Table;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OdocSecretLevelGetListCmd
/*    */   extends OdocAbstractCommonCommand
/*    */ {
/*    */   private String term;
/*    */   
/*    */   public OdocSecretLevelGetListCmd(String paramString) {
/* 32 */     this.term = paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> exe(CommandContext paramCommandContext) {
/* 37 */     HashMap<String, String> hashMap = Maps.newHashMap();
/* 38 */     boolean bool = (new ManageDetachComInfo()).isUseOdocManageDetach();
/* 39 */     bool = false;
/* 40 */     String str1 = (String)getParams().get("subCompanyId");
/* 41 */     Operate operate = new Operate("20%", "com.engine.odoc.util.OdocStandardFrontMethodUtil.getOperate2", "column:subCompanyId+" + this.user.getUID());
/* 42 */     OperateItem operateItem1 = new OperateItem("javascript:doEdit();", SystemEnv.getHtmlLabelName(26473, this.user.getLanguage()), 0);
/* 43 */     OperateItem operateItem2 = new OperateItem("javascript:doDelete();", SystemEnv.getHtmlLabelName(91, this.user.getLanguage()), 1);
/* 44 */     operate.addOperateItem(operateItem1);
/* 45 */     operate.addOperateItem(operateItem2);
/*    */     
/* 47 */     CheckBox checkBox = new CheckBox("checkbox", "com.engine.odoc.util.OdocStandardFrontMethodUtil.checkable2", "column:id+column:subCompanyId+" + this.user.getUID());
/* 48 */     Sql sql = new Sql("*", "1=1" + OdocStandardUtil.getSqlWhere(bool, Util.getIntValue(str1), this.user), "DocSecretLevel", "showorder", "id", "asc", "true");
/* 49 */     if (this.term != null && !"".equals(this.term)) {
/* 50 */       sql.setSqlwhere(sql.getSqlwhere() + " and  ( name like '%" + this.term + "%' or desc_n like '%" + this.term + "%' ) ");
/*    */     }
/*    */     
/* 53 */     String str2 = "com.engine.odoc.util.OdocStandardFrontMethodUtil.formatMultiLang";
/*    */     
/* 55 */     Col col1 = new Col("10%", false, "", "id", "column:id", "id", "id");
/* 56 */     Col col2 = new Col("30%", true, str2, SystemEnv.getHtmlLabelName(33439, this.user.getLanguage()), this.user.getLanguage() + "", "name", "name");
/* 57 */     Col col3 = new Col("30%", true, str2, SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), this.user.getLanguage() + "", "desc_n", "desc_n");
/* 58 */     Col col4 = new Col("30%", true, "", SystemEnv.getHtmlLabelName(15513, this.user.getLanguage()), "column:showorder", "showorder", "showorder");
/*    */     
/* 60 */     Table table = new Table("DocSecretLevelList", "checkbox");
/* 61 */     table.setCb(checkBox);
/* 62 */     table.setSql(sql);
/* 63 */     table.setOperate(operate);
/* 64 */     table.addCol(col1);
/* 65 */     table.addCol(col2);
/* 66 */     if (bool) {
/* 67 */       Col col = new Col("25%", true, "com.engine.odoc.util.OdocStandardFrontMethodUtil.GetSubcompanyName", SystemEnv.getHtmlLabelName(1878, this.user.getLanguage()), "", "subCompanyId", "subCompanyId");
/* 68 */       table.addCol(col);
/*    */     } 
/* 70 */     table.addCol(col3);
/* 71 */     table.addCol(col4);
/*    */     
/* 73 */     String str3 = Util.getEncrypt(Util.getRandom());
/* 74 */     Util_TableMap.setVal(str3, table.toString());
/* 75 */     hashMap.put("sessionkey", str3);
/*    */ 
/*    */ 
/*    */     
/* 79 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/odoc/cmd/standard/secretlevel/OdocSecretLevelGetListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */