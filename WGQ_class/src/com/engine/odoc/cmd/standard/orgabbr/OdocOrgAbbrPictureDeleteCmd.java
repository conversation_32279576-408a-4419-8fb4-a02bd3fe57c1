/*    */ package com.engine.odoc.cmd.standard.orgabbr;
/*    */ 
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OdocOrgAbbrPictureDeleteCmd
/*    */   implements Command<Map<String, Object>>
/*    */ {
/*    */   private User user;
/*    */   private Map<String, Object> params;
/*    */   
/*    */   public OdocOrgAbbrPictureDeleteCmd(User paramUser, Map<String, Object> paramMap) {
/* 23 */     this.user = paramUser;
/* 24 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 30 */     int i = Util.getIntValue("" + this.params.get("orgId"), -1);
/* 31 */     String str = Util.null2String(this.params.get("orgType"));
/* 32 */     int j = -1;
/* 33 */     boolean bool = false;
/* 34 */     RecordSet recordSet = new RecordSet();
/* 35 */     if ("0".equals(str)) {
/* 36 */       String str1 = "select id from workflow_subComAbbrDef where subcompanyid = " + i;
/* 37 */       recordSet.executeQuery(str1, new Object[0]);
/* 38 */       if (recordSet.next()) {
/* 39 */         j = recordSet.getInt(1);
/*    */       }
/* 41 */       if (j > 0) {
/* 42 */         String str2 = "update workflow_subComAbbrDef set abbrPicture = null where id = " + j;
/* 43 */         bool = recordSet.executeUpdate(str2, new Object[0]);
/*    */       } 
/* 45 */     } else if ("1".equals(str)) {
/* 46 */       String str1 = "select id from workflow_deptabbrdef where departmentid = " + i;
/* 47 */       recordSet.executeQuery(str1, new Object[0]);
/* 48 */       if (recordSet.next()) {
/* 49 */         j = recordSet.getInt(1);
/*    */       }
/* 51 */       if (j > 0) {
/* 52 */         String str2 = "update workflow_deptabbrdef set abbrPicture = null where id = " + j;
/* 53 */         bool = recordSet.executeUpdate(str2, new Object[0]);
/*    */       } 
/*    */     } 
/* 56 */     hashMap.put("api_status", Boolean.valueOf(bool));
/* 57 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/odoc/cmd/standard/orgabbr/OdocOrgAbbrPictureDeleteCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */