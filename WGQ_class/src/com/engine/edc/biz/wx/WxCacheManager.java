/*     */ package com.engine.edc.biz.wx;
/*     */ 
/*     */ import com.engine.core.exception.ECException;
/*     */ import com.weaver.formmodel.util.DateHelper;
/*     */ import java.util.Set;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import java.util.function.Supplier;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WxCacheManager
/*     */ {
/*  19 */   private static Set<String> dates = ConcurrentHashMap.newKeySet();
/*     */   
/*     */   public static synchronized WxCacheObject get(String paramString, Supplier<WxCacheObject> paramSupplier) {
/*  22 */     String str = DateHelper.getCurrentDate();
/*  23 */     if (!dates.contains(str)) {
/*  24 */       dates.add(str);
/*  25 */       clearExpiredCacheObj();
/*     */     } 
/*     */     
/*  28 */     WxCacheObject wxCacheObject = getCacheObjFromDBByKey(paramString);
/*  29 */     if (wxCacheObject != null) {
/*  30 */       if (wxCacheObject.isExpired()) {
/*  31 */         wxCacheObject = paramSupplier.get();
/*  32 */         updateCacheObjWithDB(paramString, wxCacheObject);
/*     */       } 
/*     */     } else {
/*  35 */       wxCacheObject = paramSupplier.get();
/*  36 */       saveCacheObjToDB(paramString, wxCacheObject);
/*     */     } 
/*     */     
/*  39 */     return wxCacheObject;
/*     */   }
/*     */   
/*     */   public static WxCacheObject getCacheObjFromDBByKey(String paramString) {
/*  43 */     String str = "select cache_value, create_timemillis, expires_in from edc_wx_cache_obj where cache_key = ?";
/*  44 */     RecordSet recordSet = new RecordSet();
/*  45 */     boolean bool = recordSet.executeQuery(str, new Object[] { paramString });
/*  46 */     if (!bool) {
/*  47 */       throw new ECException("获取缓存数据时出现异常");
/*     */     }
/*  49 */     if (recordSet.next()) {
/*  50 */       String str1 = recordSet.getString("cache_value");
/*  51 */       long l = Long.parseLong(recordSet.getString("create_timemillis"));
/*  52 */       int i = recordSet.getInt("expires_in");
/*  53 */       return new WxCacheObject(str1, i, l);
/*     */     } 
/*  55 */     return null;
/*     */   }
/*     */   
/*     */   public static void updateCacheObjWithDB(String paramString, WxCacheObject paramWxCacheObject) {
/*  59 */     String str = "update edc_wx_cache_obj set cache_value = ?, create_timemillis = ?, expires_in = ? where cache_key = ?";
/*  60 */     RecordSet recordSet = new RecordSet();
/*  61 */     boolean bool = recordSet.executeUpdate(str, new Object[] { paramWxCacheObject.get(), String.valueOf(paramWxCacheObject.getCreateTimeMillis()), Integer.valueOf(paramWxCacheObject.getExpiresIn()), paramString });
/*  62 */     if (!bool) {
/*  63 */       throw new ECException("更新缓存数据时出现异常");
/*     */     }
/*     */   }
/*     */   
/*     */   private static void saveCacheObjToDB(String paramString, WxCacheObject paramWxCacheObject) {
/*  68 */     String str = "insert into edc_wx_cache_obj(cache_key, cache_value, create_timemillis, expires_in) values(?, ?, ?, ?)";
/*  69 */     RecordSet recordSet = new RecordSet();
/*  70 */     boolean bool = recordSet.executeUpdate(str, new Object[] { paramString, paramWxCacheObject.get(), String.valueOf(paramWxCacheObject.getCreateTimeMillis()), Integer.valueOf(paramWxCacheObject.getExpiresIn()) });
/*  71 */     if (!bool)
/*  72 */       throw new ECException("插入缓存数据时出现异常"); 
/*     */   }
/*     */   
/*     */   private static void clearExpiredCacheObj() {
/*     */     String str2;
/*  77 */     RecordSet recordSet = new RecordSet();
/*  78 */     String str1 = recordSet.getDBType();
/*     */     
/*  80 */     if ("sqlserver".equals(str1)) {
/*  81 */       str2 = "delete from edc_wx_cache_obj where (convert(bigint, create_timemillis) + (expires_in * 1000)) < ?";
/*  82 */     } else if ("mysql".equals(str1)) {
/*  83 */       str2 = "delete from edc_wx_cache_obj where (CAST(creator AS decimal(20,0))  + (expires_in * 1000)) < ?";
/*     */     } else {
/*  85 */       str2 = "delete from edc_wx_cache_obj where (to_number(create_timemillis) + (expires_in * 1000)) < ?";
/*     */     } 
/*  87 */     boolean bool = recordSet.executeUpdate(str2, new Object[] { Long.valueOf(System.currentTimeMillis()) });
/*  88 */     if (!bool) {
/*  89 */       (new BaseBean()).writeLog(WxCacheManager.class.getName(), "清理过期缓存数据时出现异常");
/*     */     }
/*     */   }
/*     */   
/*     */   public static Object getWithSession(String paramString, HttpSession paramHttpSession, Supplier<Object> paramSupplier) {
/*  94 */     Object object = paramHttpSession.getAttribute(paramString);
/*  95 */     if (object == null || (object instanceof WxCacheObject && ((WxCacheObject)object).isExpired())) {
/*  96 */       object = paramSupplier.get();
/*  97 */       paramHttpSession.setAttribute(paramString, object);
/*     */     } 
/*  99 */     return object;
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 103 */     WxCacheObject wxCacheObject1 = null;
/*     */     try {
/* 105 */       wxCacheObject1 = get("123", () -> new WxCacheObject("a", 80));
/*     */     
/*     */     }
/* 108 */     catch (Exception exception) {}
/*     */ 
/*     */     
/* 111 */     WxCacheObject wxCacheObject2 = get("123", () -> new WxCacheObject("b", 80));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 117 */     System.out.println((wxCacheObject1 == wxCacheObject2));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/edc/biz/wx/WxCacheManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */