/*      */ package com.engine.edc.biz;
/*      */ 
/*      */ import com.alibaba.fastjson.JSONArray;
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import com.cloudstore.dev.api.bean.MessageBean;
/*      */ import com.cloudstore.dev.api.bean.MessageType;
/*      */ import com.cloudstore.dev.api.util.Util_Message;
/*      */ import com.engine.cube.biz.ParamUtil;
/*      */ import com.engine.edc.dao.CubePathNodeeDao;
/*      */ import com.engine.edc.entity.EdcVersionNode;
/*      */ import com.engine.edc.util.DBUtil;
/*      */ import com.engine.edc.util.EDCUtil;
/*      */ import com.engine.msgcenter.biz.ConfigManager;
/*      */ import com.engine.msgcenter.biz.WeaMessageTypeConfig;
/*      */ import com.google.common.collect.Sets;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.UUID;
/*      */ import java.util.stream.Collectors;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class CubePathTaskBiz
/*      */ {
/*   41 */   private String arrivaldate = TimeUtil.getCurrentDateString();
/*   42 */   private String arrivaltime = TimeUtil.getOnlyCurrentTimeString();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getTaskInfo(String paramString1, String paramString2, User paramUser) {
/*   51 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*   53 */     boolean bool = checkUserPermission(paramString1, paramString2, paramUser);
/*   54 */     hashMap.put("isRight", Boolean.valueOf(false));
/*      */ 
/*      */     
/*   57 */     RecordSet recordSet = new RecordSet();
/*   58 */     recordSet.executeQuery("select * from edc_task where id=?", new Object[] { paramString1 });
/*   59 */     int i = 0;
/*   60 */     if (recordSet.next()) {
/*   61 */       i = Util.getIntValue(recordSet.getString("isfinished"), 0);
/*      */     }
/*   63 */     hashMap.put("isfinished", Integer.valueOf(i));
/*      */ 
/*      */ 
/*      */     
/*   67 */     recordSet.executeQuery("select * from edc_tasknode where id=?", new Object[] { paramString2 });
/*      */ 
/*      */     
/*   70 */     int j = 0;
/*   71 */     int k = 0;
/*   72 */     int m = 0;
/*   73 */     int n = 0;
/*      */     
/*   75 */     int i1 = 0;
/*   76 */     int i2 = 0;
/*      */     
/*   78 */     boolean bool1 = false;
/*   79 */     if (recordSet.next()) {
/*   80 */       j = Util.getIntValue(recordSet.getString("status"), 0);
/*   81 */       k = Util.getIntValue(recordSet.getString("iscurrentnode"), 0);
/*   82 */       m = Util.getIntValue(recordSet.getString("supstatus"), 0);
/*   83 */       n = Util.getIntValue(recordSet.getString("undotype"), 0);
/*   84 */       i1 = Util.getIntValue(recordSet.getString("reporttype"), 0);
/*   85 */       i2 = Util.getIntValue(recordSet.getString("isforcedcollect"), 0);
/*      */     } 
/*   87 */     hashMap.put("status", Integer.valueOf(j));
/*   88 */     hashMap.put("iscurrentnode", Integer.valueOf(k));
/*   89 */     hashMap.put("supstatus", Integer.valueOf(m));
/*   90 */     hashMap.put("canSubmit", Boolean.valueOf((k == 1)));
/*   91 */     hashMap.put("undoType", Integer.valueOf(n));
/*   92 */     hashMap.put("reportType", Integer.valueOf(i1));
/*   93 */     hashMap.put("isforcedcollect", Integer.valueOf(i2));
/*      */     
/*   95 */     recordSet.executeQuery("select 1 from edc_tasknode where parentid=?", new Object[] { paramString2 });
/*   96 */     if (recordSet.next()) {
/*   97 */       bool1 = true;
/*      */     }
/*   99 */     hashMap.put("hasChild", Integer.valueOf(bool1));
/*      */ 
/*      */     
/*  102 */     recordSet.executeQuery("select * from edc_taskoperator where nodeid=? and resourceid=?", new Object[] { paramString2, Integer.valueOf(paramUser.getUID()) });
/*  103 */     if (recordSet.next()) {
/*  104 */       String str = Util.null2String(recordSet.getString("stepid"));
/*  105 */       recordSet.executeQuery("select * from edc_tasklog where logtype=1 and nodeid=? and operator=? and stepid=?", new Object[] { paramString2, Integer.valueOf(paramUser.getUID()), str });
/*  106 */       if (!recordSet.next()) {
/*  107 */         savePathlog("1", paramString1, paramString2, paramUser.getUID() + "", "", "", "", str);
/*  108 */         recordSet.executeUpdate("update edc_operatorrule set isreceived=?,receivedate=?,receivetime=? where nodeid=? and taskid=? and resourceid=?", new Object[] {
/*  109 */               Integer.valueOf(1), this.arrivaldate, this.arrivaltime, paramString2, paramString1, Integer.valueOf(paramUser.getUID()) });
/*      */       } 
/*  111 */       recordSet.executeQuery("select * from edc_taskstep where id=?", new Object[] { str });
/*  112 */       if (recordSet.next()) {
/*  113 */         int i3 = Util.getIntValue(Util.null2String(recordSet.getString("receiver")), 0);
/*  114 */         if (i3 == 0) {
/*  115 */           recordSet.executeUpdate("update edc_taskstep set receiver=?,receivedate=?,receivetime=?", new Object[] { Integer.valueOf(paramUser.getUID()), this.arrivaldate, this.arrivaltime });
/*      */         }
/*      */       } 
/*      */     } 
/*  119 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkUserPermission(String paramString1, String paramString2, User paramUser) {
/*  130 */     String str = (String)DBUtil.queryForObject("select id from edc_operatorrule  where taskid = ? and nodeid = ? and resourceid = ? and isarrival = '1' ", String.class, new Object[] { paramString1, paramString2, 
/*      */           
/*  132 */           Integer.valueOf(paramUser.getUID()) });
/*  133 */     if (EDCUtil.isNotEmpty(str)) {
/*  134 */       return true;
/*      */     }
/*  136 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> cubePathFlow(Map<String, Object> paramMap, User paramUser) {
/*  148 */     String str = Util.null2String(paramMap.get("optlevel"));
/*      */     
/*      */     try {
/*  151 */       if ("0".equals(str)) {
/*  152 */         paramMap = createTask(paramMap, paramUser);
/*  153 */       } else if ("1".equals(str)) {
/*  154 */         paramMap = modifyTaskSave(paramMap, paramUser);
/*  155 */       } else if ("2".equals(str)) {
/*  156 */         paramMap = modifyTaskSubmit(paramMap, paramUser);
/*  157 */       } else if ("3".equals(str)) {
/*  158 */         paramMap = modifyTaskReject(paramMap, paramUser);
/*  159 */       } else if ("4".equals(str)) {
/*  160 */         paramMap = modifyTaskRedirect(paramMap, paramUser);
/*  161 */       } else if ("5".equals(str)) {
/*  162 */         paramMap = modifyTaskUndo(paramMap, paramUser);
/*  163 */       } else if (!"6".equals(str)) {
/*      */         
/*  165 */         if ("7".equals(str))
/*  166 */         { paramMap = modifyTaskTransfer(paramMap, paramUser); }
/*  167 */         else if (!"8".equals(str))
/*      */         
/*  169 */         { if ("9".equals(str))
/*  170 */           { paramMap = forcedCollectTask(paramMap, paramUser); }
/*  171 */           else if ("11".equals(str))
/*  172 */           { paramMap = resetCollectTask(paramMap, paramUser); }
/*  173 */           else if ("10".equals(str))
/*  174 */           { paramMap = modifyBatchReject(paramMap, paramUser); }  } 
/*      */       } 
/*  176 */     } catch (Exception exception) {
/*  177 */       exception.printStackTrace();
/*      */     } 
/*      */     
/*  180 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean savePathlog(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8) {
/*  194 */     if ("".equals(paramString1) || "".equals(paramString2) || "".equals(paramString4)) {
/*  195 */       return false;
/*      */     }
/*  197 */     RecordSet recordSet = new RecordSet();
/*  198 */     String str1 = UUID.randomUUID().toString().replace("-", "");
/*      */     
/*  200 */     String str2 = "uuid,logtype,taskid,operator,operatedate,operatetime";
/*  201 */     String str3 = "?,?,?,?,?,?";
/*  202 */     ArrayList<String> arrayList = new ArrayList();
/*  203 */     arrayList.add(str1);
/*  204 */     arrayList.add(paramString1);
/*  205 */     arrayList.add(paramString2);
/*  206 */     arrayList.add(paramString4);
/*  207 */     arrayList.add(this.arrivaldate);
/*  208 */     arrayList.add(this.arrivaltime);
/*  209 */     if (!"".equals(paramString3)) {
/*  210 */       str2 = str2 + ",nodeid";
/*  211 */       str3 = str3 + ",?";
/*  212 */       arrayList.add(paramString3);
/*      */     } 
/*  214 */     if (!"".equals(paramString5)) {
/*  215 */       str2 = str2 + ",receiver";
/*  216 */       str3 = str3 + ",?";
/*  217 */       arrayList.add(paramString5);
/*      */     } 
/*  219 */     if (!"".equals(paramString6)) {
/*  220 */       str2 = str2 + ",targetnodeid";
/*  221 */       str3 = str3 + ",?";
/*  222 */       arrayList.add(paramString6);
/*      */     } 
/*  224 */     if (!"".equals(paramString7)) {
/*  225 */       str2 = str2 + ",isflow";
/*  226 */       str3 = str3 + ",?";
/*  227 */       arrayList.add(paramString7);
/*      */     } 
/*  229 */     if (!"".equals(paramString8)) {
/*  230 */       str2 = str2 + ",stepid";
/*  231 */       str3 = str3 + ",?";
/*  232 */       arrayList.add(paramString8);
/*      */     } 
/*  234 */     return recordSet.executeUpdate("insert into edc_taskLog (" + str2 + ") values(" + str3 + ")", new Object[] { arrayList });
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> createTask(Map<String, Object> paramMap, User paramUser) {
/*  244 */     RecordSet recordSet = new RecordSet();
/*  245 */     int i = 0;
/*  246 */     String str1 = ParamUtil.get(paramMap, "name");
/*  247 */     String str2 = ParamUtil.get(paramMap, "description");
/*  248 */     String str3 = ParamUtil.get(paramMap, "startDate");
/*  249 */     String str4 = ParamUtil.get(paramMap, "endDate");
/*  250 */     String str5 = ParamUtil.get(paramMap, "pathid");
/*  251 */     String str6 = ParamUtil.get(paramMap, "appid");
/*  252 */     String str7 = ParamUtil.get(paramMap, "cycle");
/*  253 */     String str8 = ParamUtil.get(paramMap, "taskDate");
/*  254 */     String str9 = ParamUtil.get(paramMap, "taskTime");
/*  255 */     String str10 = ParamUtil.get(paramMap, "rootOperator");
/*  256 */     String str11 = UUID.randomUUID().toString().replace("-", "");
/*      */     
/*  258 */     recordSet.executeQuery("select * from edc_pathversions where pathid=? and isused=1 order by score desc", new Object[] { str5 });
/*  259 */     String str12 = "";
/*  260 */     if (recordSet.next()) {
/*  261 */       str12 = Util.null2String(recordSet.getString("id"));
/*      */     }
/*      */ 
/*      */     
/*  265 */     String str13 = "uuid, name, description, startdate, enddate, pathid, creater, isdelete, isfinished, createdate, createtime,appid, cycle, taskDate, taskTime";
/*      */     
/*  267 */     String str14 = "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?";
/*  268 */     ArrayList<String> arrayList = new ArrayList();
/*  269 */     arrayList.add(str11);
/*  270 */     arrayList.add(str1);
/*  271 */     arrayList.add(str2);
/*  272 */     arrayList.add(str3);
/*  273 */     arrayList.add(str4);
/*  274 */     arrayList.add(str5);
/*  275 */     arrayList.add(Integer.valueOf(paramUser.getUID()));
/*  276 */     arrayList.add(Integer.valueOf(0));
/*  277 */     arrayList.add(Integer.valueOf(0));
/*  278 */     arrayList.add(this.arrivaldate);
/*  279 */     arrayList.add(this.arrivaltime);
/*  280 */     arrayList.add(str6);
/*  281 */     arrayList.add(str7);
/*  282 */     arrayList.add(str8);
/*  283 */     arrayList.add(str9);
/*  284 */     if (!"".equals(str12)) {
/*  285 */       str13 = str13 + ",versionid";
/*  286 */       str14 = str14 + ",?";
/*  287 */       arrayList.add(str12);
/*      */     } 
/*      */ 
/*      */     
/*  291 */     recordSet.executeUpdate("insert into edc_task (" + str13 + ") values (" + str14 + ")", new Object[] { arrayList });
/*  292 */     recordSet.executeQuery("select id from edc_task where uuid = ?", new Object[] { str11 });
/*  293 */     if (recordSet.next()) {
/*  294 */       i = Util.getIntValue(recordSet.getString("id"));
/*      */       
/*  296 */       savePathlog("0", i + "", "", paramUser.getUID() + "", "", "", "", "");
/*      */       
/*  298 */       if (!"".equals(str12)) {
/*      */         
/*  300 */         (new CubePathNodeBiz()).createNodesByVersion(i + "", str5, str12, str10, paramUser);
/*      */       } else {
/*      */         
/*  303 */         (new CubePathNodeBiz(i)).checkPathVersion(str5);
/*      */       } 
/*      */       
/*  306 */       createDefaultOperator(i, paramUser.getUID());
/*      */     } 
/*      */     
/*  309 */     paramMap.put("taskid", Integer.valueOf(i));
/*  310 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void createDefaultOperator(int paramInt1, int paramInt2) {
/*  319 */     RecordSet recordSet = new RecordSet();
/*  320 */     recordSet.execute("update edc_tasknode set iscurrentnode=1 where taskid=" + paramInt1 + " and id not in(select parentid from (select parentid from edc_tasknode where taskid=" + paramInt1 + " and parentid is not null) a) and status=1 and supstatus=1");
/*  321 */     createOperator(paramInt1, paramInt2, 0, 0);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String createOperator(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/*  335 */     RecordSet recordSet = new RecordSet();
/*  336 */     String str1 = "";
/*  337 */     if ("oracle".equals(recordSet.getDBType())) {
/*  338 */       str1 = "sys_guid()";
/*  339 */     } else if ("mysql".equals(recordSet.getDBType())) {
/*  340 */       str1 = "REPLACE(uuid(), '-','')";
/*      */     }
/*  342 */     else if ("postgresql".equals(recordSet.getDBType())) {
/*  343 */       str1 = "REPLACE(uuid(), '-','')";
/*      */     } else {
/*      */       
/*  346 */       str1 = "REPLACE ( newId(), '-', '' )";
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  351 */     String str2 = "";
/*  352 */     String str3 = "";
/*  353 */     if (paramInt3 == 0) {
/*  354 */       recordSet.execute("insert into edc_taskstep(uuid,taskid,nodeid)select " + str1 + "," + paramInt1 + ",id from  edc_tasknode where iscurrentnode=1 and taskid=" + paramInt1);
/*      */ 
/*      */     
/*      */     }
/*  358 */     else if (paramInt4 == 0) {
/*  359 */       recordSet.executeQuery("select * from edc_taskstep where nodeid=? order by id desc", new Object[] { Integer.valueOf(paramInt3) });
/*  360 */       if (recordSet.next()) {
/*  361 */         str2 = Util.null2String(recordSet.getString("uuid"));
/*  362 */         str3 = Util.null2String(recordSet.getString("id"));
/*  363 */         recordSet.executeUpdate("update edc_tasknode set iscurrentnode=1 where id=?", new Object[] { Integer.valueOf(paramInt3) });
/*      */       } else {
/*  365 */         str2 = UUID.randomUUID().toString().replace("-", "");
/*  366 */         recordSet.executeUpdate("insert into edc_taskstep(uuid,taskid,nodeid) values(?,?,?)", new Object[] { str2, Integer.valueOf(paramInt1), Integer.valueOf(paramInt3) });
/*  367 */         recordSet.executeUpdate("update edc_tasknode set iscurrentnode=1 where id=?", new Object[] { Integer.valueOf(paramInt3) });
/*  368 */         recordSet.executeQuery("select * from edc_taskstep where uuid=?", new Object[] { str2 });
/*  369 */         if (recordSet.next()) {
/*  370 */           str3 = Util.null2String(recordSet.getString("id"));
/*      */         }
/*      */       } 
/*  373 */     } else if (paramInt4 == 1) {
/*  374 */       str2 = UUID.randomUUID().toString().replace("-", "");
/*  375 */       recordSet.executeUpdate("insert into edc_taskstep(uuid,taskid,nodeid) values(?,?,?)", new Object[] { str2, Integer.valueOf(paramInt1), Integer.valueOf(paramInt3) });
/*  376 */       recordSet.executeUpdate("update edc_tasknode set iscurrentnode=1 where id=?", new Object[] { Integer.valueOf(paramInt3) });
/*  377 */       recordSet.executeQuery("select * from edc_taskstep where uuid=?", new Object[] { str2 });
/*  378 */       if (recordSet.next()) {
/*  379 */         str3 = Util.null2String(recordSet.getString("id"));
/*      */       }
/*  381 */     } else if (paramInt4 == 2) {
/*  382 */       recordSet.executeQuery("select * from edc_taskstep where nodeid=? order by id desc", new Object[] { Integer.valueOf(paramInt3) });
/*  383 */       if (recordSet.next()) {
/*  384 */         str2 = Util.null2String(recordSet.getString("uuid"));
/*  385 */         str3 = Util.null2String(recordSet.getString("id"));
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  390 */     String str4 = "insert into edc_taskoperator(uuid,resourceid,nodeid,taskid,arrivaldate,arrivaltime,operatorid,stepid) select " + str1 + ",o.resourceid,o.nodeid," + paramInt1 + ",'" + this.arrivaldate + "','" + this.arrivaltime + "',o.id,t.id from edc_operatorrule o,edc_taskstep t where o.taskid=t.taskid and o.nodeid=t.nodeid and t.taskid=" + paramInt1 + ((paramInt3 == 0) ? "" : (" and t.nodeid=" + paramInt3 + " and t.uuid='" + str2 + "'"));
/*      */ 
/*      */ 
/*      */     
/*  394 */     recordSet.execute(str4);
/*  395 */     String str5 = "select o.resourceid,o.nodeid,a.name from edc_operatorrule o,edc_taskstep t,edc_task a where o.taskid=t.taskid and o.nodeid=t.nodeid and t.taskid=a.id and t.taskid=" + paramInt1 + ((paramInt3 == 0) ? "" : (" and t.nodeid=" + paramInt3 + " and t.uuid='" + str2 + "'"));
/*      */ 
/*      */     
/*  398 */     recordSet.executeQuery(str5, new Object[0]);
/*      */     try {
/*  400 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  401 */       String str = resourceComInfo.getResourcename(Integer.toString(paramInt2));
/*  402 */       while (recordSet.next()) {
/*  403 */         String str6 = "~`~`7 创建人`~`8 Founder`~`9 創建人`~`~：" + str + "<br>~`~`7 创建时间`~`8 create date`~`9 創建時間`~`~：" + this.arrivaldate + " " + this.arrivaltime;
/*      */         
/*  405 */         sendMessageCenter(recordSet.getInt("resourceid"), paramInt1, recordSet.getInt("nodeid"), "~`~`7 有新的任务到达`~`8 New tasks arrive`~`9 有新的任務到達`~`~（" + recordSet.getString("name") + "）", "${506654}", str6);
/*      */       } 
/*  407 */     } catch (Exception exception) {
/*  408 */       exception.printStackTrace();
/*      */     } 
/*      */     
/*  411 */     if (paramInt3 != 0 && paramInt4 == 2) {
/*  412 */       recordSet.execute("update edc_operatorrule set issubmit=0,submitdate='',submittime='' where id  in(select operatorid from edc_taskoperator where taskid=" + paramInt1 + " and nodeid=" + paramInt3 + ")");
/*      */     }
/*      */     else {
/*      */       
/*  416 */       recordSet.execute("update edc_operatorrule set isarrival=1,arrivaldate='" + this.arrivaldate + "',arrivaltime='" + this.arrivaltime + "',issubmit=0,submitdate='',submittime='',isreceived=0,receivedate='',receivetime='' where id in(select operatorid from edc_taskoperator where taskid=" + paramInt1 + ((paramInt3 == 0) ? "" : (" and nodeid=" + paramInt3)) + ")");
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  424 */     if (paramInt3 == 0 || (paramInt3 != 0 && paramInt4 != 2)) {
/*  425 */       recordSet.execute("insert into edc_taskLog(uuid,logtype,taskid,operator,receiver,operatedate,operatetime,nodeid,stepid)select " + str1 + ",0," + paramInt1 + "," + paramInt2 + ",resourceid,'" + this.arrivaldate + "','" + this.arrivaltime + "',nodeid,stepid from edc_taskoperator where taskid=" + paramInt1 + ((paramInt3 == 0) ? "" : (" and nodeid=" + paramInt3)));
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*  430 */     return str3;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> modifyTaskSave(Map<String, Object> paramMap, User paramUser) {
/*  440 */     if (!checkCurrentOperator(paramMap, paramUser)) {
/*  441 */       paramMap.put("error", "1");
/*  442 */       paramUser.getLanguage();
/*  443 */       paramMap.put("errorMessage", SystemEnv.getHtmlLabelName(524742, paramUser.getLanguage()));
/*  444 */       return paramMap;
/*      */     } 
/*  446 */     String str1 = Util.null2String(paramMap.get("nodeid"));
/*  447 */     String str2 = Util.null2String(paramMap.get("taskid"));
/*  448 */     String str3 = Util.null2String(paramMap.get("currentStepid"));
/*  449 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  451 */     recordSet.executeUpdate("update edc_tasknode set status=3 where id=?", new Object[] { str1 });
/*  452 */     savePathlog("2", str2, str1, paramUser.getUID() + "", "", "", "0", str3);
/*  453 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> modifyTaskSubmit(Map<String, Object> paramMap, User paramUser) {
/*  463 */     if (!checkCurrentOperator(paramMap, paramUser)) {
/*  464 */       paramMap.put("error", "1");
/*  465 */       paramMap.put("errorMessage", SystemEnv.getHtmlLabelName(524742, paramUser.getLanguage()));
/*  466 */       return paramMap;
/*      */     } 
/*      */     
/*  469 */     CubePathNodeeDao cubePathNodeeDao = new CubePathNodeeDao();
/*  470 */     String str1 = Util.null2String(paramMap.get("nodeid"));
/*  471 */     Map map1 = cubePathNodeeDao.getNodeInfo(str1);
/*  472 */     String str2 = Util.null2String(map1.get("parentid"));
/*  473 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  475 */     recordSet.executeUpdate("update edc_tasknode set iscurrentnode=0,status=2 where id=?", new Object[] { str1 });
/*      */     
/*  477 */     paramMap = dealwithOthers(paramMap, paramUser, 2);
/*  478 */     paramMap = getCurrentStep(paramMap, paramUser);
/*  479 */     if ("".equals(str2) || "0".equals(str2)) {
/*  480 */       paramMap = finishTask(paramMap, paramUser);
/*  481 */       return paramMap;
/*      */     } 
/*  483 */     Map map2 = cubePathNodeeDao.getNodeInfo(str2);
/*  484 */     int i = Util.getIntValue(Util.null2String(map2.get("status")), 0);
/*      */     
/*  486 */     if (i == 0) {
/*  487 */       paramMap.put("error", "1");
/*  488 */       paramMap.put("errorMessage", SystemEnv.getHtmlLabelName(524745, paramUser.getLanguage()));
/*  489 */       return paramMap;
/*      */     } 
/*      */     
/*  492 */     String str3 = Util.null2String(paramMap.get("taskid"));
/*  493 */     String str4 = Util.null2String(paramMap.get("currentStepid"));
/*  494 */     int j = Util.getIntValue(Util.null2String(map2.get("reporttype")), 0);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  500 */     if (j == 1) {
/*      */       
/*  502 */       recordSet.executeQuery("select * from edc_tasknode where parentid=? and id<>? and status<>2 ", new Object[] { str2, str1 });
/*      */ 
/*      */       
/*  505 */       if (recordSet.next()) {
/*      */         
/*  507 */         savePathlog("3", str3, str1, paramUser.getUID() + "", "", str2, "0", str4);
/*  508 */         return paramMap;
/*      */       } 
/*      */     } else {
/*      */       
/*  512 */       int k = Util.getIntValue(Util.null2String(map2.get("iscurrentnode")), 0);
/*  513 */       if (k == 1) {
/*      */         
/*  515 */         savePathlog("3", str3, str1, paramUser.getUID() + "", "", str2, "0", str4);
/*  516 */         recordSet.executeQuery("select * from edc_taskstep where nodeid=?", new Object[] { str2 });
/*  517 */         if (recordSet.next()) {
/*  518 */           String str7 = Util.null2String(recordSet.getString("id"));
/*      */           
/*  520 */           String str8 = UUID.randomUUID().toString().replace("-", "");
/*  521 */           recordSet.executeQuery("insert into edc_taskmotion (uuid,taskid,stepid,nextstepid,flowtype) values (?,?,?,?,?)", new Object[] { str8, str3, str4, str7, 
/*  522 */                 Integer.valueOf(0) });
/*      */ 
/*      */           
/*  525 */           return paramMap;
/*      */         } 
/*  527 */         paramMap.put("error", "1");
/*  528 */         paramMap.put("errorMessage", SystemEnv.getHtmlLabelName(524746, paramUser.getLanguage()));
/*  529 */         return paramMap;
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  535 */     savePathlog("3", str3, str1, paramUser.getUID() + "", "", str2, "1", str4);
/*      */     
/*  537 */     String str5 = createOperator(Util.getIntValue(str3), paramUser.getUID(), Util.getIntValue(str2), 0);
/*      */ 
/*      */ 
/*      */     
/*  541 */     String str6 = UUID.randomUUID().toString().replace("-", "");
/*  542 */     recordSet.executeUpdate("insert into edc_taskmotion (uuid,taskid,stepid,nextstepid,flowtype) values (?,?,?,?,?)", new Object[] { str6, str3, str4, str5, 
/*  543 */           Integer.valueOf(0) });
/*  544 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> finishTask(Map<String, Object> paramMap, User paramUser) {
/*  554 */     RecordSet recordSet = new RecordSet();
/*  555 */     String str1 = Util.null2String(paramMap.get("taskid"));
/*  556 */     String str2 = Util.null2String(paramMap.get("nodeid"));
/*  557 */     String str3 = Util.null2String(paramMap.get("currentStepid"));
/*      */     
/*  559 */     recordSet.executeUpdate("update edc_task set isfinished=1 where id=?", new Object[] { str1 });
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  568 */     savePathlog("11", str1, str2, paramUser.getUID() + "", "", "", "1", str3);
/*  569 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> dealwithOthers(Map<String, Object> paramMap, User paramUser, int paramInt) {
/*  581 */     RecordSet recordSet1 = new RecordSet();
/*  582 */     RecordSet recordSet2 = new RecordSet();
/*  583 */     String str1 = Util.null2String(paramMap.get("nodeid"));
/*  584 */     String str2 = Util.null2String(paramMap.get("taskid"));
/*      */ 
/*      */ 
/*      */     
/*  588 */     recordSet1.executeUpdate("delete from edc_taskoperator where nodeid=?", new Object[] { str1 });
/*      */     
/*  590 */     if (paramInt == 2) {
/*      */ 
/*      */       
/*  593 */       recordSet1.executeUpdate("update edc_operatorrule set issubmit=?,submitdate=?,submittime=? where nodeid=?", new Object[] { Integer.valueOf(1), this.arrivaldate, this.arrivaltime, str1 });
/*      */ 
/*      */       
/*  596 */       String str3 = Util.null2String(paramMap.get("currentStepid"));
/*  597 */       recordSet1.executeUpdate("update edc_taskstep set submitdate=?,submittime=?,submiter=? where id=?", new Object[] { this.arrivaldate, this.arrivaltime, Integer.valueOf(paramUser.getUID()), str3 });
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  628 */       ArrayList<Map> arrayList = new ArrayList();
/*      */       
/*  630 */       List<Map> list = getAllChildNode(arrayList, str1);
/*      */ 
/*      */       
/*  633 */       String str4 = list.stream().map(paramMap -> paramMap.get("id").toString()).collect(Collectors.joining(","));
/*      */ 
/*      */       
/*  636 */       if (EDCUtil.isNotEmpty(str4)) {
/*  637 */         DBUtil.update(" update edc_tasknode SET iscurrentnode = 0, status = 2 WHERE id in (" + str4 + ")", new Object[0]);
/*  638 */         DBUtil.update(" update edc_operatorrule SET issubmit= 1,ISARRIVAL = 1, submitdate= ?,submittime= ? WHERE taskid = " + str2 + " and nodeid in (" + str4 + ")", new Object[] { this.arrivaldate, this.arrivaltime });
/*      */         
/*  640 */         DBUtil.update(" delete from edc_taskoperator WHERE taskid = " + str2 + " and nodeid in (" + str4 + ")", new Object[0]);
/*      */         
/*  642 */         DBUtil.update("update edc_taskstep set submitdate=?,submittime=?,submiter=? where taskid = " + str2 + " and nodeid in (" + str4 + ")", new Object[] { this.arrivaldate, this.arrivaltime, 
/*      */               
/*  644 */               Integer.valueOf(paramUser.getUID()) });
/*      */       } 
/*      */     } 
/*      */     
/*  648 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> modifyTaskReject(Map<String, Object> paramMap, User paramUser) {
/*  658 */     if (!checkCurrentOperator(paramMap, paramUser)) {
/*  659 */       paramMap.put("error", "1");
/*  660 */       paramMap.put("errorMessage", SystemEnv.getHtmlLabelName(524742, paramUser.getLanguage()));
/*  661 */       return paramMap;
/*      */     } 
/*  663 */     RecordSet recordSet = new RecordSet();
/*  664 */     CubePathNodeeDao cubePathNodeeDao = new CubePathNodeeDao();
/*  665 */     String str1 = Util.null2String(paramMap.get("nodeid"));
/*  666 */     String str2 = Util.null2String(paramMap.get("taskid"));
/*  667 */     paramMap = getCurrentStep(paramMap, paramUser);
/*  668 */     String str3 = Util.null2String(paramMap.get("targetnodeid"));
/*  669 */     String str4 = Util.null2String(paramMap.get("currentStepid"));
/*  670 */     Map map = cubePathNodeeDao.getNodeInfo(str1);
/*  671 */     int i = Util.getIntValue(Util.null2String(map.get("reporttype")), 0);
/*      */ 
/*      */ 
/*      */     
/*  675 */     boolean bool = (i == 1) ? true : false;
/*  676 */     if (i == 0) {
/*  677 */       recordSet.executeQuery("select * from edc_tasknode where parentid=? and status=2 and id<>?", new Object[] { str1, str3 });
/*  678 */       bool = !recordSet.next() ? true : false;
/*      */     } 
/*  680 */     if (bool) {
/*  681 */       paramMap = dealwithOthers(paramMap, paramUser, 3);
/*  682 */       recordSet.executeUpdate("update edc_tasknode set iscurrentnode=0 where id=?", new Object[] { str1 });
/*  683 */       recordSet.executeUpdate("update edc_operatorrule set isarrival=0 where nodeid=?", new Object[] { str1 });
/*      */       
/*  685 */       recordSet.executeUpdate("delete from edc_taskoperator where nodeid=?", new Object[] { str1 });
/*      */     } 
/*      */ 
/*      */     
/*  689 */     recordSet.executeUpdate("update edc_tasknode set iscurrentnode=1,status=3,isforcedcollect=0 where id=?", new Object[] { str3 });
/*      */ 
/*      */     
/*  692 */     savePathlog("4", str2, str1, paramUser.getUID() + "", "", str3, "1", str4);
/*      */     
/*  694 */     String str5 = createOperator(Util.getIntValue(str2), paramUser.getUID(), Util.getIntValue(str3), 1);
/*      */ 
/*      */     
/*  697 */     String str6 = UUID.randomUUID().toString().replace("-", "");
/*  698 */     recordSet.executeUpdate("insert into edc_taskmotion (uuid,taskid,stepid,laststepid,flowtype) values (?,?,?,?,?)", new Object[] { str6, str2, str5, str4, 
/*  699 */           Integer.valueOf(1) });
/*  700 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> modifyTaskRedirect(Map<String, Object> paramMap, User paramUser) throws Exception {
/*  710 */     if (!checkCurrentOperator(paramMap, paramUser)) {
/*  711 */       paramMap.put("error", "1");
/*  712 */       paramMap.put("errorMessage", SystemEnv.getHtmlLabelName(524742, paramUser.getLanguage()));
/*  713 */       return paramMap;
/*      */     } 
/*  715 */     RecordSet recordSet = new RecordSet();
/*  716 */     CubePathNodeeDao cubePathNodeeDao = new CubePathNodeeDao();
/*  717 */     String str1 = Util.null2String(paramMap.get("nodeid"));
/*  718 */     String str2 = Util.null2String(paramMap.get("taskid"));
/*  719 */     paramMap = getCurrentStep(paramMap, paramUser);
/*  720 */     String str3 = Util.null2String(paramMap.get("currentStepid"));
/*  721 */     Map map = cubePathNodeeDao.getNodeInfo(str1);
/*  722 */     int i = Util.getIntValue(Util.null2String(map.get("reporttype")), 0);
/*  723 */     int j = Util.getIntValue(Util.null2String(map.get("reporttime")), 2);
/*  724 */     int k = Util.getIntValue(Util.null2String(map.get("reporttimetype")), 1);
/*  725 */     int m = Util.getIntValue(Util.null2String(map.get("isreject")), 0);
/*  726 */     int n = Util.getIntValue(Util.null2String(map.get("iscustom")), 0);
/*  727 */     int i1 = Util.getIntValue(Util.null2String(map.get("undotype")), 0);
/*  728 */     int i2 = Util.getIntValue(Util.null2String(map.get("istemporary")), 0);
/*  729 */     int i3 = Util.getIntValue(Util.null2String(map.get("isforward")), 0);
/*  730 */     int i4 = Util.getIntValue(Util.null2String(map.get("autoSaveType")), 0);
/*  731 */     int i5 = Util.getIntValue(Util.null2String(map.get("saveTime")), 0);
/*  732 */     int i6 = Util.getIntValue(Util.null2String(map.get("saveTimeType")), 0);
/*  733 */     int i7 = Util.getIntValue(Util.null2String(map.get("versionNodeId")), 0);
/*  734 */     String str4 = Util.null2String(map.get("pathid"));
/*  735 */     String str5 = Util.null2String(map.get("reportTimeSetting"));
/*  736 */     int i8 = Util.getIntValue(Util.null2String(map.get("reportTimeOverType")), 0);
/*      */     
/*  738 */     boolean bool = (i == 1) ? true : false;
/*  739 */     if (i == 0) {
/*  740 */       recordSet.executeQuery("select * from edc_tasknode where parentid=? and status=2 ", new Object[] { str1 });
/*  741 */       bool = !recordSet.next() ? true : false;
/*      */     } 
/*      */     
/*  744 */     if (bool) {
/*  745 */       paramMap = dealwithOthers(paramMap, paramUser, 4);
/*  746 */       recordSet.executeUpdate("update edc_tasknode set iscurrentnode=0 where id=?", new Object[] { str1 });
/*  747 */       recordSet.executeUpdate("update edc_operatorrule set isarrival=0 where nodeid=?", new Object[] { str1 });
/*  748 */       recordSet.executeUpdate("delete from edc_taskoperator where nodeid=?", new Object[] { str1 });
/*      */     } 
/*  750 */     String str6 = Util.null2String(map.get("hreflink"));
/*  751 */     String str7 = Util.null2String(paramMap.get("operators"));
/*  752 */     JSONArray jSONArray = JSONArray.parseArray(str7);
/*  753 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  754 */     for (byte b = 0; b < jSONArray.size(); b++) {
/*  755 */       String str8 = "";
/*  756 */       String str9 = "";
/*  757 */       JSONObject jSONObject = jSONArray.getJSONObject(b);
/*  758 */       if (EDCUtil.isNotEmpty(jSONObject)) {
/*  759 */         str8 = jSONObject.getString("id");
/*  760 */         str9 = jSONObject.getString("sheetIds");
/*      */       } 
/*  762 */       if (EDCUtil.isNotEmpty(str8)) {
/*  763 */         String str = UUID.randomUUID().toString().replace("-", "");
/*      */         
/*  765 */         recordSet.executeUpdate("insert into edc_tasknode(uuid,pathid,taskid,parentid,versionnodeid,nodetype,operatetype,tablename,tablekey,showfield,dataid,reporttype,reporttime,reporttimetype,isreject,iscustom,name,hreflink,status,supstatus,undotype,istemporary,isforward, autoSaveType, saveTime, saveTimeType, sheetIds, reportTimeSetting, reportTimeOverType) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", new Object[] { str, str4, str2, str1, 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  770 */               Integer.valueOf(i7), Integer.valueOf(1), Integer.valueOf(0), "hrmresource", "id", "lastname", str8, 
/*  771 */               Integer.valueOf(i), Integer.valueOf(j), Integer.valueOf(k), Integer.valueOf(m), 
/*  772 */               Integer.valueOf(n), resourceComInfo.getLastname(str8), str6, Integer.valueOf(1), Integer.valueOf(1), 
/*  773 */               Integer.valueOf(i1), Integer.valueOf(i2), Integer.valueOf(i3), Integer.valueOf(i4), Integer.valueOf(i5), Integer.valueOf(i6), str9, str5, Integer.valueOf(i8) });
/*  774 */         recordSet.executeQuery("select * from edc_tasknode where uuid=?", new Object[] { str });
/*  775 */         if (recordSet.next()) {
/*  776 */           String str10 = Util.null2String(recordSet.getString("id"));
/*  777 */           str = UUID.randomUUID().toString().replace("-", "");
/*  778 */           recordSet.executeUpdate("insert into edc_operatorrule(uuid,resourceid,nodeid,taskid) values(?,?,?,?)", new Object[] { str, str8, str10, str2 });
/*      */ 
/*      */           
/*  781 */           savePathlog("5", str2, str1, paramUser.getUID() + "", str8, str10, "1", str3);
/*      */           
/*  783 */           String str11 = createOperator(Util.getIntValue(str2), paramUser.getUID(), Util.getIntValue(str10), 1);
/*      */ 
/*      */           
/*  786 */           str = UUID.randomUUID().toString().replace("-", "");
/*  787 */           recordSet.executeUpdate("insert into edc_taskmotion (uuid,taskid,stepid,laststepid,flowtype) values (?,?,?,?,?)", new Object[] { str, str2, str11, str3, 
/*  788 */                 Integer.valueOf(1) });
/*      */         } 
/*      */       } 
/*      */     } 
/*  792 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> modifyTaskUndo(Map<String, Object> paramMap, User paramUser) throws Exception {
/*  805 */     RecordSet recordSet = new RecordSet();
/*  806 */     CubePathNodeeDao cubePathNodeeDao = new CubePathNodeeDao();
/*  807 */     String str1 = Util.null2String(paramMap.get("nodeid"));
/*  808 */     String str2 = Util.null2String(paramMap.get("taskid"));
/*  809 */     paramMap = getCurrentStep(paramMap, paramUser);
/*  810 */     String str3 = Util.null2String(paramMap.get("currentStepid"));
/*  811 */     Map map1 = cubePathNodeeDao.getNodeInfo(str1);
/*  812 */     int i = Util.getIntValue(Util.null2String(map1.get("undotype")), 0);
/*  813 */     String str4 = Util.null2String(map1.get("parentid"));
/*  814 */     Map map2 = cubePathNodeeDao.getNodeInfo(str4);
/*  815 */     int j = paramUser.getLanguage();
/*      */     
/*  817 */     int k = Util.getIntValue(Util.null2String(map2.get("iscurrentnode")), 0);
/*  818 */     int m = Util.getIntValue(Util.null2String(map2.get("status")), 0);
/*  819 */     recordSet.executeQuery("select * from edc_tasknode where id=? and iscurrentnode=1 and status=3", new Object[] { str1 });
/*  820 */     if (recordSet.next()) {
/*  821 */       paramMap.put("error", "1");
/*  822 */       paramMap.put("errorMessage", Util.formatMultiLang("~`~`7 当前节点已撤回，无法重复撤回！`~`8 The current node has been withdrawn and cannot be recalled again!`~`9 當前節點已撤回，無法重復撤回！`~`~", String.valueOf(j)));
/*  823 */       return paramMap;
/*      */     } 
/*  825 */     if (i == 2) {
/*  826 */       if (k == 0 && m == 2) {
/*  827 */         paramMap.put("error", "1");
/*  828 */         paramMap.put("errorMessage", Util.formatMultiLang("~`~`7 上级节点已提交，无法撤回！`~`8 The parent node has been submitted and cannot be withdrawn!`~`9 上級節點已提交，無法撤回！`~`~", String.valueOf(j)));
/*  829 */         return paramMap;
/*      */       } 
/*  831 */     } else if (i == 1) {
/*  832 */       recordSet.executeQuery("select * from edc_taskstep where nodeid=? order by id desc", new Object[] { str4 });
/*  833 */       if (recordSet.next()) {
/*      */ 
/*      */ 
/*      */         
/*  837 */         int n = Util.getIntValue(Util.null2String(recordSet.getString("receiver")), 0);
/*  838 */         if (n > 0) {
/*  839 */           paramMap.put("error", "1");
/*  840 */           paramMap.put("errorMessage", Util.formatMultiLang("~`~`7 上级节点已查看，无法撤回！`~`8 The parent node has been viewed and cannot be recalled!`~`9 上級節點已查看，無法撤回！`~`~", String.valueOf(j)));
/*  841 */           return paramMap;
/*      */         } 
/*      */       } 
/*      */     } else {
/*  845 */       paramMap.put("error", "1");
/*  846 */       paramMap.put("errorMessage", Util.formatMultiLang("~`~`7 当前节点不允许撤回！`~`8 Current node cannot be recalled!`~`9 當前節點不允許撤回！`~`~", String.valueOf(j)));
/*  847 */       return paramMap;
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  860 */     if (k == 1) {
/*  861 */       int n = Util.getIntValue(Util.null2String(map2.get("reporttype")), 0);
/*  862 */       boolean bool = (n == 1) ? true : false;
/*  863 */       if (n == 0) {
/*  864 */         recordSet.executeQuery("select * from edc_tasknode where parentid=? and status=2 and id<>?", new Object[] { str4, str1 });
/*  865 */         bool = !recordSet.next() ? true : false;
/*      */       } 
/*  867 */       if (bool) {
/*  868 */         paramMap = dealwithOthers(paramMap, paramUser, 5);
/*  869 */         recordSet.executeUpdate("update edc_tasknode set iscurrentnode=0 where id=?", new Object[] { str4 });
/*      */         
/*  871 */         recordSet.executeUpdate("update edc_operatorrule set isarrival=0 where nodeid=?", new Object[] { str4 });
/*      */         
/*  873 */         recordSet.executeUpdate("delete from edc_taskoperator where nodeid=?", new Object[] { str4 });
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  878 */     recordSet.executeUpdate("update edc_tasknode set iscurrentnode=1,status=3 where id=?", new Object[] { str1 });
/*      */ 
/*      */     
/*  881 */     savePathlog("6", str2, str4, paramUser.getUID() + "", "", str1, "1", str3);
/*      */     
/*  883 */     createOperator(Util.getIntValue(str2), paramUser.getUID(), Util.getIntValue(str1), 2);
/*      */ 
/*      */     
/*  886 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> modifyTaskTransfer(Map<String, Object> paramMap, User paramUser) throws Exception {
/*  897 */     if (!checkCurrentOperator(paramMap, paramUser)) {
/*  898 */       paramMap.put("error", "1");
/*  899 */       paramMap.put("errorMessage", SystemEnv.getHtmlLabelName(524742, paramUser.getLanguage()));
/*  900 */       return paramMap;
/*      */     } 
/*      */     
/*  903 */     RecordSet recordSet = new RecordSet();
/*  904 */     String str1 = Util.null2String(paramMap.get("nodeid"));
/*  905 */     String str2 = Util.null2String(paramMap.get("taskid"));
/*  906 */     String str3 = Util.null2String(paramMap.get("operator"));
/*      */     
/*  908 */     String str4 = "";
/*  909 */     if ("oracle".equals(recordSet.getDBType())) {
/*  910 */       str4 = "sys_guid()";
/*  911 */     } else if ("mysql".equals(recordSet.getDBType())) {
/*  912 */       str4 = "REPLACE(uuid(), '-','')";
/*      */     }
/*  914 */     else if ("postgresql".equals(recordSet.getDBType())) {
/*  915 */       str4 = "REPLACE(uuid(), '-','')";
/*      */     } else {
/*      */       
/*  918 */       str4 = "REPLACE ( newId(), '-', '' )";
/*      */     } 
/*  920 */     recordSet.executeUpdate("insert into edc_operatorrule(uuid,resourceid,nodeid,ruleid,operatorid,isarrival, arrivaldate,arrivaltime,issubmit,submitdate,submittime,isreceived,receivedate,receivetime)  select " + str4 + "," + str3 + ",nodeid,ruleid,operatorid,isarrival, arrivaldate,arrivaltime,issubmit,submitdate,submittime,isreceived,receivedate,receivetime from edc_operatorrule where nodeid=? and taskid=? and resourceid=?", new Object[] { str1, str2, 
/*      */ 
/*      */ 
/*      */           
/*  924 */           Integer.valueOf(paramUser.getUID()) });
/*  925 */     recordSet.executeUpdate("update edc_operatorrule set istransfered=1 where nodeid=? and taskid=? and resourceid=?", new Object[] { str1, str2, Integer.valueOf(paramUser.getUID()) });
/*  926 */     recordSet.executeUpdate("insert into edc_taskoperator(uuid,resourceid,nodeid,taskid,arrivaldate,arrivaltime,operatorid,stepid)  select " + str4 + ",o.resourceid,o.nodeid," + str2 + ",'" + this.arrivaldate + "','" + this.arrivaltime + "',o.id,t.id from  edc_operatorrule o,edc_taskstep t where o.taskid=t.taskid and o.nodeid=t.nodeid and t.taskid=? and t.nodeid=? and o.resourceid=?", new Object[] { str2, str1, str3 });
/*      */ 
/*      */ 
/*      */     
/*  930 */     recordSet.executeUpdate("delete from edc_taskoperator where taskid=? and nodeid=? and resourceid=?", new Object[] { str2, str1, Integer.valueOf(paramUser.getUID()) });
/*  931 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> forcedCollectTask(Map<String, Object> paramMap, User paramUser) {
/*  941 */     RecordSet recordSet = new RecordSet();
/*  942 */     CubePathNodeeDao cubePathNodeeDao = new CubePathNodeeDao();
/*  943 */     String str1 = Util.null2String(paramMap.get("nodeid"));
/*  944 */     String str2 = Util.null2String(paramMap.get("taskid"));
/*      */     
/*  946 */     Map map = cubePathNodeeDao.getNodeInfo(str1);
/*      */ 
/*      */ 
/*      */     
/*  950 */     if ("1".equals(map.get("iscurrentnode").toString())) {
/*  951 */       paramMap.put("error", "1");
/*  952 */       paramMap.put("errorMessage", SystemEnv.getHtmlLabelName(524747, paramUser.getLanguage()));
/*  953 */       return paramMap;
/*      */     } 
/*      */ 
/*      */     
/*  957 */     if ("1".equals(map.get("isforcedcollect").toString())) {
/*  958 */       paramMap.put("error", "1");
/*  959 */       paramMap.put("errorMessage", SystemEnv.getHtmlLabelName(524748, paramUser.getLanguage()));
/*  960 */       return paramMap;
/*      */     } 
/*      */     
/*  963 */     ArrayList<Map> arrayList = new ArrayList();
/*      */     
/*  965 */     List<Map> list = getAllChildNode(arrayList, str1);
/*      */ 
/*      */     
/*  968 */     String str3 = list.stream().map(paramMap -> paramMap.get("id").toString()).collect(Collectors.joining(","));
/*      */ 
/*      */     
/*  971 */     if (EDCUtil.isNotEmpty(str3)) {
/*  972 */       DBUtil.update(" update edc_tasknode SET iscurrentnode = 0, isforcedcollect = 1, status = 2 WHERE id in (" + str3 + ")", new Object[0]);
/*  973 */       DBUtil.update(" update edc_operatorrule SET isforcedcollect = 1, issubmit= 1,ISARRIVAL = 1, submitdate= ?,submittime= ? WHERE taskid = " + str2 + " and nodeid in (" + str3 + ")", new Object[] { this.arrivaldate, this.arrivaltime });
/*      */       
/*  975 */       DBUtil.update(" delete from edc_taskoperator WHERE taskid = " + str2 + " and nodeid in (" + str3 + ")", new Object[0]);
/*      */       
/*  977 */       DBUtil.update("update edc_taskstep set submitdate=?,submittime=?,submiter=? where taskid = " + str2 + " and nodeid in (" + str3 + ")", new Object[] { this.arrivaldate, this.arrivaltime, 
/*      */             
/*  979 */             Integer.valueOf(paramUser.getUID()) });
/*      */     } 
/*      */ 
/*      */     
/*  983 */     savePathlog("12", str2, str1, paramUser.getUID() + "", "", "", "", "");
/*      */     
/*  985 */     String str4 = createOperator(Util.getIntValue(str2), paramUser.getUID(), Util.getIntValue(str1), 0);
/*      */ 
/*      */     
/*  988 */     String str5 = UUID.randomUUID().toString().replace("-", "");
/*  989 */     recordSet.executeUpdate("insert into edc_taskmotion (uuid,taskid,stepid,nextstepid,flowtype) values (?,?,?,?,?)", new Object[] { str5, str2, 
/*  990 */           Integer.valueOf(0), str4, Integer.valueOf(0) });
/*      */     
/*  992 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> resetCollectTask(Map<String, Object> paramMap, User paramUser) {
/* 1001 */     if (!checkCurrentOperator(paramMap, paramUser)) {
/* 1002 */       paramMap.put("error", "1");
/* 1003 */       paramMap.put("errorMessage", SystemEnv.getHtmlLabelName(524742, paramUser.getLanguage()));
/* 1004 */       return paramMap;
/*      */     } 
/* 1006 */     String str1 = Util.null2String(paramMap.get("nodeid"));
/* 1007 */     String str2 = Util.null2String(paramMap.get("taskid"));
/* 1008 */     String str3 = Util.null2String(paramMap.get("currentStepid"));
/*      */     
/* 1010 */     EdcVersionNode edcVersionNode = (EdcVersionNode)DBUtil.queryForObject("select id, pathid, hreflink, formData from edc_tasknode where id = ? ", EdcVersionNode.class, new Object[] { str1 });
/*      */     
/* 1012 */     edcVersionNode.initHrefLink(paramUser.getLanguage());
/*      */     
/* 1014 */     String str4 = Util.null2String(edcVersionNode.getFormData());
/*      */     
/* 1016 */     String str5 = edcVersionNode.getHreflinkObj().getString(2);
/* 1017 */     if (EDCUtil.isEmpty(str4) || "0".equals(str4) || EDCUtil.isEmpty(str5)) {
/* 1018 */       return paramMap;
/*      */     }
/*      */     
/* 1021 */     DBUtil.update(" update edc_tasknode SET status = 1, isresetcollect=1 WHERE id = ?", new Object[] { str1 });
/*      */     
/* 1023 */     savePathlog("14", str2, str1, paramUser.getUID() + "", "", str1, "1", str3);
/* 1024 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<Map> getAllChildNode(List<Map> paramList, String paramString) {
/* 1031 */     List list = DBUtil.queryForList("select * from edc_tasknode where PARENTID = ?", Map.class, new Object[] { paramString });
/*      */ 
/*      */     
/* 1034 */     list.forEach(paramMap -> {
/*      */           if ("2".equals(paramMap.get("status").toString())) {
/*      */             return;
/*      */           }
/*      */ 
/*      */           
/*      */           paramList.add(paramMap);
/*      */           
/*      */           getAllChildNode(paramList, paramMap.get("id").toString());
/*      */         });
/*      */     
/* 1045 */     return paramList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> getCurrentStep(Map<String, Object> paramMap, User paramUser) {
/* 1056 */     String str = Util.null2String(paramMap.get("nodeid"));
/*      */     
/* 1058 */     paramMap.put("currentStepid", DBUtil.queryForObject("select id from edc_taskstep where nodeid=? order by id desc", String.class, new Object[] { str }));
/* 1059 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean checkCurrentOperator(Map<String, Object> paramMap, User paramUser) {
/* 1069 */     RecordSet recordSet = new RecordSet();
/* 1070 */     String str = Util.null2String(paramMap.get("nodeid"));
/* 1071 */     recordSet.executeQuery("select * from edc_taskoperator where nodeid=? and resourceid=?", new Object[] { str, Integer.valueOf(paramUser.getUID()) });
/* 1072 */     return recordSet.next();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> modifyBatchReject(Map<String, Object> paramMap, User paramUser) {
/* 1082 */     if (!checkCurrentOperator(paramMap, paramUser)) {
/* 1083 */       paramMap.put("error", "1");
/* 1084 */       paramMap.put("errorMessage", SystemEnv.getHtmlLabelName(524742, paramUser.getLanguage()));
/* 1085 */       return paramMap;
/*      */     } 
/* 1087 */     RecordSet recordSet = new RecordSet();
/* 1088 */     CubePathNodeeDao cubePathNodeeDao = new CubePathNodeeDao();
/* 1089 */     String str1 = Util.null2String(paramMap.get("nodeid"));
/* 1090 */     String str2 = Util.null2String(paramMap.get("taskid"));
/* 1091 */     paramMap = getCurrentStep(paramMap, paramUser);
/* 1092 */     List list = (List)paramMap.get("targetnodeids");
/* 1093 */     String str3 = Util.null2String(paramMap.get("currentStepid"));
/* 1094 */     Map map = cubePathNodeeDao.getNodeInfo(str1);
/* 1095 */     paramMap = dealwithOthers(paramMap, paramUser, 3);
/* 1096 */     recordSet.executeUpdate("update edc_tasknode set iscurrentnode=0 where id=?", new Object[] { str1 });
/* 1097 */     recordSet.executeUpdate("update edc_operatorrule set isarrival=0 where nodeid=?", new Object[] { str1 });
/*      */     
/* 1099 */     recordSet.executeUpdate("delete from edc_taskoperator where nodeid=?", new Object[] { str1 });
/*      */ 
/*      */     
/* 1102 */     savePathlog("13", str2, str1, paramUser.getUID() + "", "", "", "1", str3);
/*      */     
/* 1104 */     for (String str4 : list) {
/* 1105 */       str4 = Util.null2String(str4);
/*      */       
/* 1107 */       recordSet.executeUpdate("update edc_tasknode set iscurrentnode=1,status=3 where id=?", new Object[] { str4 });
/*      */       
/* 1109 */       String str5 = createOperator(Util.getIntValue(str2), paramUser.getUID(), Util.getIntValue(str4), 1);
/*      */       
/* 1111 */       String str6 = UUID.randomUUID().toString().replace("-", "");
/* 1112 */       recordSet.executeUpdate("insert into edc_taskmotion (uuid,taskid,stepid,laststepid,flowtype) values (?,?,?,?,?)", new Object[] { str6, str2, str5, str3, 
/* 1113 */             Integer.valueOf(1) });
/*      */     } 
/*      */     
/* 1116 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void sendMessageCenter(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, String paramString3) {
/*      */     try {
/* 1130 */       String str1 = TaskListTransmethodBiz.getLoadRoute(String.valueOf(paramInt3));
/* 1131 */       if (EDCUtil.isNotEmpty(str1)) {
/* 1132 */         str1 = str1 + ((str1.indexOf("?") == -1) ? "?" : "&");
/*      */       } else {
/* 1134 */         str1 = "?";
/*      */       } 
/* 1136 */       String str2 = GCONST.getContextPath() + "/spa/edc/static/app/index.html#/edc/view" + str1 + "taskid=" + paramInt2 + "&nodeid=" + paramInt3 + "&userid=";
/*      */ 
/*      */       
/* 1139 */       String str3 = GCONST.getContextPath() + "/spa/edc/static4mobile/index.html#/app/excelTaskView" + str1 + "taskid=" + paramInt2 + "&nodeid=" + paramInt3 + "&userid=";
/*      */       
/* 1141 */       ConfigManager configManager = new ConfigManager();
/*      */ 
/*      */ 
/*      */       
/* 1145 */       Map map = configManager.defaultRuleCheckConfig(MessageType.DATA_TASK_ARRIVAL, paramInt1, null);
/*      */       
/* 1147 */       for (Map.Entry entry : map.entrySet()) {
/*      */         
/* 1149 */         MessageBean messageBean = Util_Message.createMessage(MessageType.DATA_TASK_ARRIVAL, 0, paramString1, paramString2, paramString3, str2, str3, 1);
/*      */         
/* 1151 */         WeaMessageTypeConfig weaMessageTypeConfig = (WeaMessageTypeConfig)entry.getKey();
/*      */         
/* 1153 */         messageBean.setMessageConfig(weaMessageTypeConfig);
/*      */         
/* 1155 */         messageBean.setUserList(Sets.newHashSet((Iterable)entry.getValue()));
/*      */         
/* 1157 */         Util_Message.sendAndpublishMessage(messageBean);
/*      */       } 
/* 1159 */     } catch (Exception exception) {
/* 1160 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/edc/biz/CubePathTaskBiz.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */