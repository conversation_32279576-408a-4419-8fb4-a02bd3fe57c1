/*    */ package com.engine.edc.web;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONArray;
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import com.api.doc.detail.util.DocDownloadCheckUtil;
/*    */ import com.api.doc.upload.web.util.UploadFile2Doc;
/*    */ import com.engine.edc.biz.action.ActionProcessor;
/*    */ import com.engine.edc.biz.action.result.Result;
/*    */ import com.engine.edc.util.EDCUtil;
/*    */ import com.weaver.formmodel.mobile.MobileFileUpload;
/*    */ import com.weaver.formmodel.mobile.utils.AttachUtil;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import javax.ws.rs.POST;
/*    */ import javax.ws.rs.Path;
/*    */ import javax.ws.rs.Produces;
/*    */ import javax.ws.rs.core.Context;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.file.FileUpload;
/*    */ import weaver.general.GCONST;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EdcUploadFileAction
/*    */ {
/*    */   @POST
/*    */   @Path("/uploadFile2Doc")
/*    */   @Produces({"text/plain"})
/*    */   public String uploadFile2Doc(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 44 */     Map<String, Integer> map = (Map)new HashMap<>();
/*    */     try {
/* 46 */       FileUpload fileUpload = new FileUpload(paramHttpServletRequest, "utf-8");
/* 47 */       map = UploadFile2Doc.upload(fileUpload, paramHttpServletRequest, paramHttpServletResponse);
/* 48 */       int i = Util.getIntValue(fileUpload.getParameter("secId"), 0);
/* 49 */       Map map1 = (Map)map.get("data");
/* 50 */       int j = 0;
/* 51 */       if (map1 != null) {
/* 52 */         j = Util.getIntValue((String)map1.get("fileid"), 0);
/*    */       }
/* 54 */       if (i == 0) {
/* 55 */         changeDocToOpen(j);
/*    */       }
/*    */     }
/* 58 */     catch (Exception exception) {
/* 59 */       map.put("status", Integer.valueOf(0));
/* 60 */       map.put("error", "catch exception : " + exception.getMessage());
/*    */     } 
/* 62 */     return JSONObject.toJSONString(map);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static void changeDocToOpen(int paramInt) {
/* 69 */     RecordSet recordSet = new RecordSet();
/* 70 */     recordSet.execute("select docid,imagefileid from DocImageFile where docid=" + paramInt + " order by docid desc");
/* 71 */     if (recordSet.next()) {
/* 72 */       String str = recordSet.getString("imagefileid");
/*    */       
/* 74 */       recordSet.execute("insert into DocPicUpload(imagefileid)values(" + str + ")");
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @POST
/*    */   @Path("/saveImage")
/*    */   @Produces({"text/plain"})
/*    */   public String saveImage(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 87 */     return ActionProcessor.handle(paramHttpServletRequest, paramUser -> {
/*    */           MobileFileUpload mobileFileUpload = new MobileFileUpload(paramHttpServletRequest, "UTF-8", false);
/*    */           String str1 = Util.null2String(paramHttpServletRequest.getParameter("src"));
/*    */           int i = AttachUtil.convertImageBase64ToDoc(str1, (FileUpload)mobileFileUpload, paramUser, -1, -1, EDCUtil.getUUID());
/*    */           JSONArray jSONArray = EDCUtil.convertAttach(String.valueOf(i), 2);
/*    */           String str2 = "";
/*    */           if (jSONArray.size() > 0)
/*    */             str2 = jSONArray.getJSONObject(0).getString("imagefileid"); 
/*    */           JSONObject jSONObject = new JSONObject();
/*    */           jSONObject.put("downurl", GCONST.getContextPath() + "/weaver/weaver.file.FileDownload?fileid=" + DocDownloadCheckUtil.checkPermission(str2, paramHttpServletRequest, null) + "&download=1");
/*    */           return Result.ok(jSONObject);
/*    */         });
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/edc/web/EdcUploadFileAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */