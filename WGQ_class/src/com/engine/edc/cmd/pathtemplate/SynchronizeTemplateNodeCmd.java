/*    */ package com.engine.edc.cmd.pathtemplate;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.edc.biz.custompath.CustomPathBiz;
/*    */ import com.engine.edc.util.DBUtil;
/*    */ import com.weaver.general.Util;
/*    */ import java.util.Map;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SynchronizeTemplateNodeCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   private String templateid;
/*    */   private String nodeid;
/*    */   private String synchronizeNodeId;
/*    */   
/*    */   public SynchronizeTemplateNodeCmd(String paramString1, String paramString2, String paramString3, User paramUser) {
/* 23 */     this.templateid = paramString1;
/* 24 */     this.nodeid = paramString2;
/* 25 */     this.synchronizeNodeId = paramString3;
/* 26 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 31 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 36 */     int i = Util.getIntValue((String)DBUtil.queryForObject("select type from edc_pathTemplate where id = ?", String.class, new Object[] { this.templateid }), 0);
/* 37 */     String str1 = "edc_pathTemplateNode_v";
/* 38 */     String str2 = "edc_templateNodeOperateMenu_v";
/* 39 */     if (i == 1) {
/* 40 */       str1 = "edc_pathTemplateNode";
/* 41 */       str2 = "edc_templateNodeOperateMenu";
/*    */     } 
/* 43 */     CustomPathBiz customPathBiz = new CustomPathBiz(this.user);
/* 44 */     customPathBiz.synchronizeNodeInfo(this.nodeid, "2", this.synchronizeNodeId, str1, str2);
/* 45 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/edc/cmd/pathtemplate/SynchronizeTemplateNodeCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */