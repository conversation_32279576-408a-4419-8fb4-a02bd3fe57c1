/*    */ package com.engine.edc.cmd.excel;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import com.cloudstore.dev.api.util.TextUtil;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.exception.ECException;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.edc.entity.EdcExcelDetailProps;
/*    */ import com.engine.edc.entity.EdcPage;
/*    */ import com.engine.edc.entity.EdcPageSheet;
/*    */ import com.engine.edc.entity.FormField;
/*    */ import com.engine.edc.util.DBUtil;
/*    */ import com.engine.edc.util.EDCUtil;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.stream.Collectors;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class GetExcelPageInfoCmd
/*    */   extends AbstractCommonCommand<EdcPage>
/*    */ {
/*    */   private String id;
/*    */   
/*    */   public GetExcelPageInfoCmd(String paramString, User paramUser) {
/* 29 */     this.id = paramString;
/* 30 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 36 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public EdcPage execute(CommandContext paramCommandContext) {
/* 41 */     EdcPage edcPage = (EdcPage)DBUtil.queryForObject("select * from edc_page where id = ? ", EdcPage.class, new Object[] { this.id });
/* 42 */     if (edcPage == null) {
/* 43 */       throw new ECException(SystemEnv.getHtmlLabelName(507118, this.user.getLanguage()));
/*    */     }
/*    */     
/* 46 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 47 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 48 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 49 */     List list = DBUtil.queryForList("select name, formid from edc_pagesheet where pageid = ? ", EdcPageSheet.class, new Object[] { this.id });
/* 50 */     if (list != null) {
/* 51 */       list.forEach(paramEdcPageSheet -> {
/*    */             List list1 = DBUtil.queryForList("select fieldlabel as name, fieldName,pubchoiceid from workflow_billfield where billid = ? ", FormField.class, new Object[] { paramEdcPageSheet.getFormid() });
/*    */             
/*    */             list1.forEach(());
/*    */             
/*    */             if (list1 != null) {
/*    */               paramMap1.put(paramEdcPageSheet.getFormid(), list1.stream().map(()).collect(Collectors.toList()));
/*    */               paramMap2.put(paramEdcPageSheet.getName(), list1.stream().map(()).collect(Collectors.toList()));
/*    */             } 
/*    */             String str = "";
/*    */             if ("oracle".equals((new RecordSet()).getDBType())) {
/*    */               str = "nvl";
/*    */             } else if ("mysql".equals((new RecordSet()).getDBType())) {
/*    */               str = "ifnull";
/*    */             } else if ("sqlserver".equals((new RecordSet()).getDBType())) {
/*    */               str = "isnull";
/*    */             } else if ("postgresql".equals((new RecordSet()).getDBType())) {
/*    */               str = "isnull";
/*    */             } 
/*    */             List list2 = DBUtil.queryForList("SELECT formid,bindingPath," + str + "(isAddRow,1) isAddRow," + str + "(isDeleteRow,1) isDeleteRow from edc_formdetailprops where formid = ? ", EdcExcelDetailProps.class, new Object[] { paramEdcPageSheet.getFormid() });
/*    */             if (list2 != null) {
/*    */               paramMap3.put(paramEdcPageSheet.getName(), list2);
/*    */             }
/*    */           });
/*    */     }
/* 76 */     edcPage.setLabels(hashMap1);
/* 77 */     edcPage.setFieldName(hashMap2);
/* 78 */     edcPage.setRowControl(JSONObject.toJSONString(hashMap3));
/* 79 */     String str1 = EDCUtil.handleCommonCheckJson(edcPage.getJson(), "design");
/* 80 */     String str2 = TextUtil.toBase64(str1);
/* 81 */     edcPage.setJson(str2);
/* 82 */     return edcPage;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/edc/cmd/excel/GetExcelPageInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */