/*    */ package com.engine.edc.cmd.excel;
/*    */ 
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.edc.common.Params;
/*    */ import com.engine.edc.util.DBUtil;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ public class DeletePageCmd extends Params<Void> {
/*    */   private String id;
/*    */   
/*    */   public DeletePageCmd(String paramString, User paramUser) {
/* 13 */     super(null, paramUser);
/* 14 */     this.id = paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 19 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Void execute(CommandContext paramCommandContext) {
/* 24 */     DBUtil.update("delete from edc_page where id = ? ", new Object[] { this.id });
/* 25 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/edc/cmd/excel/DeletePageCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */