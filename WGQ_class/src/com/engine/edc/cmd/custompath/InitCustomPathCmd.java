/*    */ package com.engine.edc.cmd.custompath;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.edc.biz.custompath.CustomPathBiz;
/*    */ import com.engine.edc.entity.EdcPath;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ public class InitCustomPathCmd
/*    */   extends AbstractCommonCommand<EdcPath>
/*    */ {
/*    */   private String appid;
/*    */   
/*    */   public InitCustomPathCmd(String paramString, User paramUser) {
/* 17 */     this.appid = paramString;
/* 18 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 24 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public EdcPath execute(CommandContext paramCommandContext) {
/* 29 */     return (new CustomPathBiz(this.user)).initCustomPath(this.appid);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/edc/cmd/custompath/InitCustomPathCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */