/*    */ package com.engine.edc.cmd.custompath;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.edc.util.DBUtil;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ public class SaveImgCmd
/*    */   extends AbstractCommonCommand<Void> {
/*    */   private int pathid;
/*    */   private String img;
/*    */   
/*    */   public SaveImgCmd(int paramInt, String paramString, User paramUser) {
/* 15 */     this.pathid = paramInt;
/* 16 */     this.img = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 22 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Void execute(CommandContext paramCommandContext) {
/* 27 */     DBUtil.update("update edc_path set img = ? where id = ?", new Object[] { this.img, Integer.valueOf(this.pathid) });
/* 28 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/edc/cmd/custompath/SaveImgCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */