/*    */ package com.engine.edc.cmd.custompath;
/*    */ 
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.edc.biz.custompath.CustomPathBiz;
/*    */ import com.engine.edc.common.Params;
/*    */ import com.engine.edc.entity.EdcVersionNode;
/*    */ import com.engine.edc.util.DBUtil;
/*    */ import com.engine.edc.util.EDCUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.stream.Collectors;
/*    */ import org.apache.commons.lang3.StringUtils;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.resource.ResourceComInfo2;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetDynamicNodePreviewListCmd
/*    */   extends Params<Map<String, Object>>
/*    */ {
/*    */   private String nodeId;
/*    */   private EdcVersionNode root;
/*    */   
/*    */   public GetDynamicNodePreviewListCmd(Map<String, Object> paramMap, User paramUser) {
/* 35 */     super(paramMap, paramUser);
/* 36 */     this.nodeId = getString("id");
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 41 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 46 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 47 */     String str1 = (String)DBUtil.queryForObject("select pathid from edc_node_v where id=?", String.class, new Object[] { this.nodeId });
/* 48 */     String str2 = (String)DBUtil.queryForObject("select id from edc_pathversions where pathid=? and isused=1 ", String.class, new Object[] { str1 });
/* 49 */     String str3 = String.valueOf(Util.getIntValue((String)DBUtil.queryForObject("select min(id) from edc_task", String.class, new Object[0]), 0) - 1);
/* 50 */     CustomPathBiz customPathBiz = new CustomPathBiz();
/* 51 */     customPathBiz.createTaskPath(str3, str1, str2, "", this.user);
/* 52 */     String str4 = "select * from edc_tasknode t1 left join edc_tasknode t2 on t1.parentid = t2.id where (t2.parentid is null or t2.parentid = 0) and t1.taskid = ? and t1.versionnodeid = ? order by t1.id";
/*    */     
/* 54 */     List<EdcVersionNode> list = DBUtil.queryForList(str4, EdcVersionNode.class, new Object[] { str3, this.nodeId });
/* 55 */     List<Map<String, Object>> list1 = getNodeInfoList(list);
/* 56 */     DBUtil.update("delete from edc_tasknode where taskid = ?", new Object[] { str3 });
/* 57 */     DBUtil.update("delete from edc_operatorrule where taskid = ?", new Object[] { str3 });
/* 58 */     hashMap.put("nodes", list1);
/* 59 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   private List<Map<String, Object>> getNodeInfoList(List<EdcVersionNode> paramList) {
/* 63 */     ArrayList<Map<String, Object>> arrayList = new ArrayList();
/* 64 */     paramList.forEach(paramEdcVersionNode -> {
/*    */           HashMap<Object, Object> hashMap = new HashMap<>();
/*    */           hashMap.put("key", paramEdcVersionNode.getId());
/*    */           hashMap.put("name", paramEdcVersionNode.getName());
/*    */           hashMap.put("operator", getNodeOperator(paramEdcVersionNode.getId()));
/*    */           List<EdcVersionNode> list = DBUtil.queryForList("select id,name from edc_tasknode where parentid = ?", EdcVersionNode.class, new Object[] { paramEdcVersionNode.getId() });
/*    */           if (EDCUtil.isNotEmpty(list)) {
/*    */             hashMap.put("subList", getNodeInfoList(list));
/*    */           }
/*    */           paramList.add(hashMap);
/*    */         });
/* 75 */     return arrayList;
/*    */   }
/*    */   
/*    */   private String getNodeOperator(String paramString) {
/* 79 */     ResourceComInfo2 resourceComInfo2 = new ResourceComInfo2();
/* 80 */     return StringUtils.join((Iterable)DBUtil.queryForList("select resourceid from edc_operatorrule where nodeid = ?", String.class, new Object[] { paramString }).stream().map(paramString -> paramResourceComInfo2.getLastname(paramString)).collect(Collectors.toList()), ",");
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/edc/cmd/custompath/GetDynamicNodePreviewListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */