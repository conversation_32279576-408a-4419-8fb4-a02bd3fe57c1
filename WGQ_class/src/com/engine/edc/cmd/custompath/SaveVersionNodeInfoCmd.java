/*    */ package com.engine.edc.cmd.custompath;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.edc.biz.custompath.CustomPathBiz;
/*    */ import com.engine.edc.entity.EdcPath;
/*    */ import com.engine.edc.entity.EdcVersionNode;
/*    */ import com.engine.edc.util.DBUtil;
/*    */ import com.engine.edc.util.UUID;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SaveVersionNodeInfoCmd
/*    */   extends AbstractCommonCommand<EdcPath>
/*    */ {
/*    */   private EdcVersionNode node;
/* 27 */   private int pathid = 0;
/*    */   
/*    */   public SaveVersionNodeInfoCmd(EdcVersionNode paramEdcVersionNode, Map<String, Object> paramMap, User paramUser) {
/* 30 */     this.node = paramEdcVersionNode;
/* 31 */     this.params = paramMap;
/* 32 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 38 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public EdcPath execute(CommandContext paramCommandContext) {
/* 43 */     CustomPathBiz customPathBiz = new CustomPathBiz(this.user);
/* 44 */     if (this.pathid == 0) {
/* 45 */       this.pathid = Util.getIntValue((String)DBUtil.queryForObject("select pathid from edc_node_v where id = ? ", String.class, new Object[] { this.node.getId() }));
/*    */     }
/* 47 */     int i = Util.getIntValue((String)DBUtil.queryForObject("select pathType from edc_path where id = ?", String.class, new Object[] { Integer.valueOf(this.pathid) }), 0);
/* 48 */     DBUtil.update("update edc_node_v set name = #{name},reportTime = #{reportTime},reportType = #{reportType},undoType = #{undoType},reportTimeType = #{reportTimeType},hreflink = #{hreflink},saveTime = #{saveTime}, saveTimeType = #{saveTimeType},autoSaveType = #{autoSaveType},sheetIds = #{sheetIds}, operatorSetType = #{operatorSetType},countProcess = #{countProcess},reportTimeSetting = #{reportTimeSetting},reportTimeOverType = #{reportTimeOverType} where id = #{id} ", new Object[] { this.node });
/*    */ 
/*    */ 
/*    */     
/* 52 */     if (i < 3)
/* 53 */     { DBUtil.update("delete from \tedc_nodeoperator_v where nodeid = ?", new Object[] { this.node.getId() });
/* 54 */       if (!"1".equals(this.node.getOperatorSetType())) {
/* 55 */         for (String str : this.node.getResourceid().split(",")) {
/* 56 */           DBUtil.update("insert into edc_nodeoperator_v (uuid,resourceid,nodeid) values (?,?,?)", new Object[] { UUID.randomKey(), str, this.node.getId() });
/*    */         } 
/*    */       } }
/* 59 */     else if (i == 3) { customPathBiz.updateDynamicNodeInfo(this.node.getId(), this.params); }
/* 60 */      customPathBiz.synchronizeNodeInfo(this.node.getId(), this.node.getSynchronizeType(), this.node.getSynchronizeNodeId(), "edc_node_v", "edc_nodeOperateMenu_v");
/* 61 */     return customPathBiz.initCustomPathById(this.pathid);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/edc/cmd/custompath/SaveVersionNodeInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */