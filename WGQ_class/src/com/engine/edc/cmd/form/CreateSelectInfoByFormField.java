/*    */ package com.engine.edc.cmd.form;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.alibaba.fastjson.JSONArray;
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.edc.biz.form.FormNameBiz;
/*    */ import com.engine.edc.entity.FormField;
/*    */ import com.engine.edc.util.DBUtil;
/*    */ import com.engine.edc.util.EDCUtil;
/*    */ 
/*    */ public class CreateSelectInfoByFormField
/*    */   extends AbstractCommonCommand<Void>
/*    */ {
/*    */   private int fieldId;
/*    */   private String options;
/*    */   
/*    */   public CreateSelectInfoByFormField(int paramInt, String paramString) {
/* 21 */     this.fieldId = paramInt;
/* 22 */     this.options = paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 27 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Void execute(CommandContext paramCommandContext) {
/* 32 */     JSONArray jSONArray = JSON.parseArray(this.options);
/*    */     
/* 34 */     DBUtil.update("delete from workflow_SelectItem where fieldid =?", new Object[] { Integer.valueOf(this.fieldId) });
/* 35 */     boolean bool = false;
/* 36 */     for (byte b = 0; b < jSONArray.size(); b++) {
/* 37 */       JSONObject jSONObject = jSONArray.getJSONObject(b);
/* 38 */       String str4 = jSONObject.getString("showname");
/* 39 */       int i = jSONObject.getIntValue("key");
/*    */       
/* 41 */       String str5 = "insert into workflow_SelectItem (uuid, isbill,isdefault,selectvalue, selectname, fieldid, listorder, cancel) values(?,?,?,?,?,?,?,?)";
/* 42 */       DBUtil.update(str5, new Object[] { EDCUtil.getUUID(), Integer.valueOf(1), "n", Integer.valueOf(i), str4, Integer.valueOf(this.fieldId), Integer.valueOf(b), "0" });
/*    */       
/* 44 */       if (i == -1) {
/* 45 */         bool = true;
/*    */       }
/*    */     } 
/*    */ 
/*    */     
/* 50 */     FormField formField = (FormField)DBUtil.queryForObject("select id, fieldname as fieldName, billid as formId, detailtable as detailTable from workflow_billfield where id = ? ", FormField.class, new Object[] { Integer.valueOf(this.fieldId) });
/*    */     
/* 52 */     String str1 = formField.getDetailTable();
/* 53 */     String str2 = EDCUtil.isNotEmpty(str1) ? str1 : (new FormNameBiz()).getTableNameByFormId(formField.getFormId().intValue());
/* 54 */     String str3 = "other_field" + this.fieldId;
/*    */     try {
/* 56 */       boolean bool1 = DBUtil.isExistColumn(str2, str3);
/* 57 */       if (bool && !bool1) {
/* 58 */         DBUtil.update("alter table " + str2 + " add " + str3 + " varchar(500)", new Object[0]);
/* 59 */       } else if (!bool && bool1) {
/* 60 */         DBUtil.update("alter table " + str2 + "  drop column " + str3, new Object[0]);
/*    */       } 
/* 62 */     } catch (Exception exception) {}
/*    */ 
/*    */     
/* 65 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/edc/cmd/form/CreateSelectInfoByFormField.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */