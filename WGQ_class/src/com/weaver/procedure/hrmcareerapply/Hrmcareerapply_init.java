/*    */ package com.weaver.procedure.hrmcareerapply;
/*    */ 
/*    */ import weaver.conn.DBProcedureInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Hrmcareerapply_init
/*    */   extends DBProcedureInterface
/*    */ {
/*    */   public Hrmcareerapply_init(ProcParamStore paramProcParamStore) {
/* 12 */     super(paramProcParamStore);
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 17 */     executeUpdate("DELETE FROM HrmCareerApply WHERE id NOT IN (SELECT applyid FROM HrmCareerApplyOtherInfo)", new Object[0]);
/* 18 */     executeQuery("select max(dftsubcomid) from SystemSet", new Object[0]);
/* 19 */     Integer integer = next() ? Integer.valueOf(getInt(1)) : null;
/* 20 */     executeUpdate("update HrmCareerApply set subcompanyid = ? where subcompanyid=0 or subcompanyid is null", new Object[] { integer });
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/hrmcareerapply/Hrmcareerapply_init.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */