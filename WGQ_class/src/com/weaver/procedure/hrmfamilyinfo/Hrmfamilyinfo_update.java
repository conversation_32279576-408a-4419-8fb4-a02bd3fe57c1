/*    */ package com.weaver.procedure.hrmfamilyinfo;
/*    */ 
/*    */ import weaver.conn.DBProcedureSimpleInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ 
/*    */ public class Hrmfamilyinfo_update
/*    */   extends DBProcedureSimpleInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Hrmfamilyinfo_update(ProcParamStore paramProcParamStore) {
/* 13 */     super(paramProcParamStore);
/* 14 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 19 */     executeUpdate("update HrmFamilyInfo SET resourceid= ? , member= ? , title= ? , company= ? , jobtitle= ? , address= ? WHERE id= ?", new Object[] { this.params[1], this.params[2], this.params[3], this.params[4], this.params[5], this.params[6], this.params[0] });
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/hrmfamilyinfo/Hrmfamilyinfo_update.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */