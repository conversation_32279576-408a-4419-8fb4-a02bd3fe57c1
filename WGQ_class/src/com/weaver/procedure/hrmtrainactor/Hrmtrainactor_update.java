/*    */ package com.weaver.procedure.hrmtrainactor;
/*    */ 
/*    */ import weaver.conn.DBProcedureSimpleInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ public class Hrmtrainactor_update
/*    */   extends DBProcedureSimpleInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Hrmtrainactor_update(ProcParamStore paramProcParamStore) {
/* 12 */     super(paramProcParamStore);
/* 13 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 18 */     executeUpdate("update HrmTrainActor set isattend= 1 where traindayid= ? and resourceid= ?", new Object[] { this.params[0], this.params[1] });
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/hrmtrainactor/Hrmtrainactor_update.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */