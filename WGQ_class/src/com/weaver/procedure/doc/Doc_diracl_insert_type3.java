/*    */ package com.weaver.procedure.doc;
/*    */ 
/*    */ import weaver.conn.DBProcedureMethodInterface;
/*    */ import weaver.conn.DBProcedureSimpleInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ import weaver.docs.util.DocTriggerUtils;
/*    */ 
/*    */ public class Doc_diracl_insert_type3
/*    */   extends DBProcedureSimpleInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Doc_diracl_insert_type3(ProcParamStore paramProcParamStore) {
/* 14 */     super(paramProcParamStore);
/* 15 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 20 */     DocTriggerUtils docTriggerUtils = new DocTriggerUtils();
/* 21 */     String str = docTriggerUtils.uuid();
/* 22 */     executeUpdate("insert into DirAccessControlList(dirid ,  dirtype ,  seclevel ,  operationcode ,  permissiontype , trigger_uuid )  values(? , ? , ? , ? , 3 , '" + str + "' )", new Object[] { this.params[0], this.params[1], this.params[3], this.params[2] });
/* 23 */     docTriggerUtils.Tri_I_DirAccessControlList(str, (DBProcedureMethodInterface)this);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/doc/Doc_diracl_insert_type3.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */