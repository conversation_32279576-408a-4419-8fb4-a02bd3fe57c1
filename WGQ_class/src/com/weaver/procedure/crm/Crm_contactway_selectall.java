/*    */ package com.weaver.procedure.crm;
/*    */ 
/*    */ import weaver.conn.DBProcedureSimpleInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Crm_contactway_selectall
/*    */   extends DBProcedureSimpleInterface
/*    */ {
/*    */   public Crm_contactway_selectall(ProcParamStore paramProcParamStore) {
/* 12 */     super(paramProcParamStore);
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 17 */     executeQuery("SELECT * FROM CRM_ContactWay order by id asc", new Object[0]);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/crm/Crm_contactway_selectall.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */