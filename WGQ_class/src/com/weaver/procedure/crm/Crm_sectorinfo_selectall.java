/*    */ package com.weaver.procedure.crm;
/*    */ 
/*    */ import weaver.conn.DBProcedureSimpleInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ 
/*    */ public class Crm_sectorinfo_selectall
/*    */   extends DBProcedureSimpleInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Crm_sectorinfo_selectall(ProcParamStore paramProcParamStore) {
/* 13 */     super(paramProcParamStore);
/* 14 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 19 */     executeQuery("SELECT * FROM CRM_SectorInfo WHERE parentid= ?  order by id", new Object[] { this.params[0] });
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/crm/Crm_sectorinfo_selectall.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */