/*    */ package com.weaver.procedure.crm;
/*    */ 
/*    */ import weaver.conn.DBProcedureSimpleInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ public class Crm_payinfo_update
/*    */   extends DBProcedureSimpleInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Crm_payinfo_update(ProcParamStore paramProcParamStore) {
/* 12 */     super(paramProcParamStore);
/* 13 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 18 */     executeUpdate("update CRM_PayInfo set factprice= ? , factdate= ? , creater= ? , formNum= ? WHERE id= ?", new Object[] { this.params[1], this.params[2], this.params[3], this.params[4], this.params[0] });
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/crm/Crm_payinfo_update.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */