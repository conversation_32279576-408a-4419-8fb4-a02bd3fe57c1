/*    */ package com.weaver.procedure.crm;
/*    */ 
/*    */ import weaver.conn.DBProcedureSimpleInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ public class Crm_customerdesc_insert
/*    */   extends DBProcedureSimpleInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Crm_customerdesc_insert(ProcParamStore paramProcParamStore) {
/* 12 */     super(paramProcParamStore);
/* 13 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 18 */     executeUpdate("INSERT INTO CRM_CustomerDesc(fullname ,  description )  VALUES(? , ? )", new Object[] { this.params[0], this.params[1] });
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/crm/Crm_customerdesc_insert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */