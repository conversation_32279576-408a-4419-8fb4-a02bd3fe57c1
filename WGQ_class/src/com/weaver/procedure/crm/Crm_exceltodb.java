/*    */ package com.weaver.procedure.crm;
/*    */ 
/*    */ import weaver.conn.DBProcedureSimpleInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ 
/*    */ public class Crm_exceltodb
/*    */   extends DBProcedureSimpleInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Crm_exceltodb(ProcParamStore paramProcParamStore) {
/* 13 */     super(paramProcParamStore);
/* 14 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 19 */     executeUpdate("INSERT INTO crm_customerinfo(department  ,  subcompanyid1  ,  NAME  ,  engname  ,  address1  ,  zipcode  ,  phone  ,  fax  ,  email  ,  country  ,  TYPE  ,  DESCRIPTION  ,  size_n  ,  sector  ,  creditamount  ,  credittime  ,  deleted  ,  status  ,  rating  ,  website  ,  source  ,  manager  ,  city  ,  province  ,  LANGUAGE  ,  crmcode  ,  bankname  ,  accountname  ,  accounts  ,  createdate  ,  seclevel  )  VALUES(? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , trunc(? ) , ? , '0' , ? , '1' , ? , '9' , ? , ? , ? , '7' , ? , ? , ? , ? , ? , 0 )", new Object[] { this.params[0], this.params[1], this.params[2], this.params[3], this.params[4], this.params[5], this.params[6], this.params[7], this.params[8], this.params[9], this.params[10], this.params[11], this.params[12], this.params[13], this.params[14], this.params[15], this.params[20], this.params[16], this.params[19], this.params[17], this.params[18], this.params[21], this.params[22], this.params[23], this.params[24], this.params[25] });
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/crm/Crm_exceltodb.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */