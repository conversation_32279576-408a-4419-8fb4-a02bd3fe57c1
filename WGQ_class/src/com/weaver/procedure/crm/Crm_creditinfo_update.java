/*    */ package com.weaver.procedure.crm;
/*    */ 
/*    */ import weaver.conn.DBProcedureSimpleInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ 
/*    */ public class Crm_creditinfo_update
/*    */   extends DBProcedureSimpleInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Crm_creditinfo_update(ProcParamStore paramProcParamStore) {
/* 13 */     super(paramProcParamStore);
/* 14 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 20 */     executeUpdate("UPDATE CRM_CreditInfo SET fullname= ? , creditamount= ? , highamount= ? WHERE id= ?", new Object[] { this.params[1], this.params[2], this.params[3], this.params[0] });
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/crm/Crm_creditinfo_update.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */