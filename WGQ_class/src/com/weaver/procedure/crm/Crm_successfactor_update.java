/*    */ package com.weaver.procedure.crm;
/*    */ 
/*    */ import weaver.conn.DBProcedureSimpleInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ public class Crm_successfactor_update
/*    */   extends DBProcedureSimpleInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Crm_successfactor_update(ProcParamStore paramProcParamStore) {
/* 12 */     super(paramProcParamStore);
/* 13 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 18 */     executeUpdate("UPDATE CRM_Successfactor SET fullname= ? , description= ? WHERE id= ?", new Object[] { this.params[1], this.params[2], this.params[0] });
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/crm/Crm_successfactor_update.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */