/*    */ package com.weaver.procedure.prj;
/*    */ 
/*    */ import weaver.conn.DBProcedureInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ 
/*    */ public class Prj_find_recentremark
/*    */   extends DBProcedureInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Prj_find_recentremark(ProcParamStore paramProcParamStore) {
/* 13 */     super(paramProcParamStore);
/* 14 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 19 */     String str = this.proxy.getOrgindbtype();
/* 20 */     if ("oracle".equalsIgnoreCase(getDBType())) {
/* 21 */       executeQuery("SELECT * from(select* from Prj_Log WHERE projectid= ?  ORDER BY submitdate DESC , submittime DESC ) WHERE rownum< 4", new Object[] { this.params[0] });
/*    */     }
/* 23 */     else if (str.equalsIgnoreCase("sqlserver")) {
/* 24 */       executeQuery("SELECT top 3 * from Prj_Log WHERE projectid= ?  ORDER BY submitdate DESC , submittime DESC", new Object[] { this.params[0] });
/*    */     }
/* 26 */     else if ("mysql".equalsIgnoreCase(getDBType())) {
/* 27 */       executeQuery("SELECT * FROM Prj_Log WHERE projectid= ? ORDER BY submitdate DESC , submittime DESC limit 3", new Object[] { this.params[0] });
/*    */     }
/* 29 */     else if ("postgresql".equalsIgnoreCase(getDBType())) {
/* 30 */       executeQuery("SELECT * FROM Prj_Log WHERE projectid= ? ORDER BY submitdate DESC , submittime DESC limit 3", new Object[] { this.params[0] });
/*    */     } else {
/*    */       
/* 33 */       executeQuery("SELECT * from(select* from Prj_Log WHERE projectid= ?  ORDER BY submitdate DESC , submittime DESC ) WHERE rownum< 4", new Object[] { this.params[0] });
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/prj/Prj_find_recentremark.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */