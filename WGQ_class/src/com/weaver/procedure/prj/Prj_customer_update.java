/*    */ package com.weaver.procedure.prj;
/*    */ 
/*    */ import weaver.conn.DBProcedureSimpleInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ public class Prj_customer_update
/*    */   extends DBProcedureSimpleInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Prj_customer_update(ProcParamStore paramProcParamStore) {
/* 12 */     super(paramProcParamStore);
/* 13 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 18 */     executeUpdate("Update Prj_Customer set customerid= ? , powerlevel= ? , reasondesc= ? where id= ?", new Object[] { this.params[1], this.params[2], this.params[3], this.params[0] });
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/prj/Prj_customer_update.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */