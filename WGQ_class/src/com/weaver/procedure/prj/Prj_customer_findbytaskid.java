/*    */ package com.weaver.procedure.prj;
/*    */ 
/*    */ import weaver.conn.DBProcedureSimpleInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ public class Prj_customer_findbytaskid
/*    */   extends DBProcedureSimpleInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Prj_customer_findbytaskid(ProcParamStore paramProcParamStore) {
/* 12 */     super(paramProcParamStore);
/* 13 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 18 */     executeQuery("SELECT * FROM Prj_Customer WHERE prjid= ? and taskid= ?", new Object[] { this.params[0], this.params[1] });
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/prj/Prj_customer_findbytaskid.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */