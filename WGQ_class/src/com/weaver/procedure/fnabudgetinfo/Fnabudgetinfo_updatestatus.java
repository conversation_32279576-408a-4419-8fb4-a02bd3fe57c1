/*    */ package com.weaver.procedure.fnabudgetinfo;
/*    */ 
/*    */ import weaver.conn.DBProcedureSimpleInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ public class Fnabudgetinfo_updatestatus
/*    */   extends DBProcedureSimpleInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Fnabudgetinfo_updatestatus(ProcParamStore paramProcParamStore) {
/* 12 */     super(paramProcParamStore);
/* 13 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 18 */     executeUpdate("UPDATE FnaBudgetInfo SET budgetstatus= ? , status= ? , revision= ? WHERE id= ?", new Object[] { this.params[3], this.params[1], this.params[2], this.params[0] });
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/fnabudgetinfo/Fnabudgetinfo_updatestatus.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */