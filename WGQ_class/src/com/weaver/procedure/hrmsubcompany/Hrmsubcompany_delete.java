/*    */ package com.weaver.procedure.hrmsubcompany;
/*    */ 
/*    */ import com.engine.hrm.util.HrmTriggerUtil;
/*    */ import weaver.conn.DBProcedureInterface;
/*    */ import weaver.conn.util.PendingProc;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ @PendingProc
/*    */ public class Hrmsubcompany_delete
/*    */   extends DBProcedureInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Hrmsubcompany_delete(ProcParamStore paramProcParamStore) {
/* 15 */     super(paramProcParamStore);
/* 16 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 21 */     executeQuery("select count(id) from HrmDepartment where subcompanyid1= ?", new Object[] { this.params[0] });
/* 22 */     boolean bool = next() ? getInt(1) : false;
/* 23 */     if (!bool) {
/* 24 */       executeUpdate(" delete from HrmSubCompany WHERE id= ?", new Object[] { this.params[0] });
/* 25 */       HrmTriggerUtil.T_AlbumSubcompanyInsByDelete(String.valueOf(this.params[0]));
/*    */     } else {
/* 27 */       transferValue(new Object[] { "20" });
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/hrmsubcompany/Hrmsubcompany_delete.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */