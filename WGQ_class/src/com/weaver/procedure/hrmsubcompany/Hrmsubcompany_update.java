/*    */ package com.weaver.procedure.hrmsubcompany;
/*    */ 
/*    */ import com.engine.hrm.util.HrmTriggerUtil;
/*    */ import weaver.conn.DBProcedureInterface;
/*    */ import weaver.conn.util.ProcParamStore;
/*    */ 
/*    */ 
/*    */ public class Hrmsubcompany_update
/*    */   extends DBProcedureInterface
/*    */ {
/*    */   private Object[] params;
/*    */   
/*    */   public Hrmsubcompany_update(ProcParamStore paramProcParamStore) {
/* 14 */     super(paramProcParamStore);
/* 15 */     this.params = paramProcParamStore.getParams();
/*    */   }
/*    */ 
/*    */   
/*    */   public void executeProc() throws Exception {
/* 20 */     executeQuery("select count(*) from HrmSubCompany where subcompanyname= ? and id != ? and supsubcomid= ?", new Object[] { this.params[1], this.params[0], this.params[4] });
/* 21 */     boolean bool1 = next() ? getInt(1) : false;
/* 22 */     if (bool1) {
/* 23 */       setFlag(2);
/* 24 */       setMsg("该分部简称已经存在，不能保存！");
/*    */       
/*    */       return;
/*    */     } 
/* 28 */     executeQuery("select count(* ) from HrmSubCompany where subcompanydesc= ? and id != ? and supsubcomid= ?", new Object[] { this.params[2], this.params[0], this.params[4] });
/* 29 */     boolean bool2 = next() ? getInt(1) : false;
/* 30 */     if (bool2) {
/* 31 */       setFlag(3);
/* 32 */       setMsg("该分部全称已经存在，不能保存！");
/*    */       return;
/*    */     } 
/* 35 */     executeUpdate("UPDATE HrmSubCompany SET subcompanyname= ? , subcompanydesc= ? , companyid= ? , supsubcomid= ? , url= ? , showorder= ? WHERE id= ?", new Object[] { this.params[1], this.params[2], this.params[3], this.params[4], this.params[5], this.params[6], this.params[0] });
/*    */     
/* 37 */     HrmTriggerUtil.generateSubcompanyPinyin(String.valueOf(this.params), getTransHandler());
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/procedure/hrmsubcompany/Hrmsubcompany_update.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */