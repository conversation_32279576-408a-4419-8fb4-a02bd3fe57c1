/*     */ package com.weaver.ecology.search.model;
/*     */ 
/*     */ import com.weaver.ecology.search.model.base.AbstractBaseBean;
/*     */ import com.weaver.ecology.search.util.CommonUtils;
/*     */ import org.apache.log4j.Logger;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ResultItem
/*     */   extends AbstractBaseBean
/*     */ {
/*  27 */   private static Logger logger = Logger.getLogger(ResultItem.class);
/*     */   
/*     */   private static final long serialVersionUID = -4601059922083771155L;
/*     */   
/*     */   private long docId;
/*     */   
/*     */   private int userId;
/*     */   
/*     */   private String author;
/*     */   
/*     */   private String text;
/*     */   
/*     */   private String title;
/*     */   
/*     */   private String date;
/*     */   
/*     */   private boolean isAttachFile;
/*     */   
/*     */   private boolean isReply;
/*     */   
/*     */   private String userLoginType;
/*     */   
/*     */   private String mainCategory;
/*     */   
/*     */   private String secondCategory;
/*     */   
/*     */   private String thirdCategory;
/*     */   
/*  55 */   private static ResourceComInfo rci = null;
/*     */   
/*  57 */   private static CustomerInfoComInfo cici = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ResultItem() {
/*     */     
/*  64 */     try { rci = new ResourceComInfo();
/*  65 */       cici = new CustomerInfoComInfo(); }
/*  66 */     catch (Exception exception) { logger.error("生成搜索结果对象时，创建ResourceComInfo，CustomerInfoComInfo时异常!", exception); }
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMainCategory() {
/*  74 */     return this.mainCategory;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setMainCategory(String paramString) {
/*  82 */     this.mainCategory = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isAttachFile() {
/*  90 */     return this.isAttachFile;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAttachFile(boolean paramBoolean) {
/*  98 */     this.isAttachFile = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isReply() {
/* 106 */     return this.isReply;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setReply(boolean paramBoolean) {
/* 114 */     this.isReply = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDate() {
/* 122 */     return this.date;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDate(String paramString) {
/* 130 */     this.date = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTitle() {
/* 138 */     return this.title;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTitle(String paramString) {
/* 146 */     this.title = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSecondCategory() {
/* 154 */     return this.secondCategory;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSecondCategory(String paramString) {
/* 162 */     this.secondCategory = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getThirdCategory() {
/* 170 */     return this.thirdCategory;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setThirdCategory(String paramString) {
/* 178 */     this.thirdCategory = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserLoginType() {
/* 186 */     return this.userLoginType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUserLoginType(String paramString) {
/* 194 */     this.userLoginType = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAuthor() {
/* 202 */     return this.author;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAuthor(String paramString) {
/* 210 */     this.author = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getText() {
/* 218 */     return this.text;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setText(String paramString) {
/* 226 */     this.text = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public long getDocId() {
/* 234 */     return this.docId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDocId(long paramLong) {
/* 242 */     this.docId = paramLong;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void clear() {
/* 249 */     this.author = "";
/* 250 */     this.date = "";
/* 251 */     this.text = "";
/* 252 */     this.title = "";
/* 253 */     this.docId = 0L;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String toString() {
/* 261 */     StringBuffer stringBuffer = new StringBuffer();
/* 262 */     stringBuffer.append("<p>");
/* 263 */     String str1 = "javascript:openFullWindowForXtable('/docs/docs/DocDsp.jsp?id=" + this.docId + "');";
/* 264 */     stringBuffer.append("<div class=\"sTitle\"><span><a href=\"" + str1 + "\">" + this.title + "</a>");
/* 265 */     if (isAttachFile())
/* 266 */       stringBuffer.append("(<img src='/images/attach2_wev8.gif'/>" + SystemEnv.getHtmlLabelName(10005750, ThreadVarLanguage.getLang()) + ")"); 
/* 267 */     if (isReply())
/* 268 */       stringBuffer.append("(<img src='/images/popup/movedown_wev8.gif'/>)" + SystemEnv.getHtmlLabelName(18546, ThreadVarLanguage.getLang()) + ""); 
/* 269 */     stringBuffer.append("</span></div>");
/* 270 */     stringBuffer.append("<div>");
/* 271 */     stringBuffer.append(getText());
/* 272 */     stringBuffer.append("</div>");
/* 273 */     stringBuffer.append("" + SystemEnv.getHtmlLabelName(10005751, ThreadVarLanguage.getLang()) + "<a href=\"javaScript:openhrm('" + this.userId + "');\" onclick='pointerXY(event);'>");
/* 274 */     String str2 = null;
/* 275 */     if (this.userLoginType.equalsIgnoreCase("1")) {
/* 276 */       str2 = rci.getResourcename(String.valueOf(this.userId));
/*     */     } else {
/* 278 */       str2 = cici.getCustomerInfoname(String.valueOf(this.userId));
/*     */     } 
/* 280 */     stringBuffer.append(str2);
/* 281 */     stringBuffer.append("</a>&nbsp;&nbsp;&nbsp;");
/* 282 */     stringBuffer.append("" + SystemEnv.getHtmlLabelName(33092, ThreadVarLanguage.getLang()) + ":" + getMainCategory());
/* 283 */     stringBuffer.append("/" + getSecondCategory());
/* 284 */     stringBuffer.append("/" + getThirdCategory());
/* 285 */     stringBuffer.append("&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10005752, ThreadVarLanguage.getLang()) + "" + CommonUtils.getShortDateString(this.date));
/* 286 */     stringBuffer.append("</p>");
/* 287 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getUserId() {
/* 295 */     return this.userId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUserId(int paramInt) {
/* 303 */     this.userId = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/ecology/search/model/ResultItem.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */