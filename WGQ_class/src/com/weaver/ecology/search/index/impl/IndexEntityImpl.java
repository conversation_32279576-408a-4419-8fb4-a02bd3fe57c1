/*     */ package com.weaver.ecology.search.index.impl;
/*     */ 
/*     */ import com.weaver.ecology.search.index.IDocumentEntity;
/*     */ import com.weaver.ecology.search.index.IndexEntity;
/*     */ import com.weaver.ecology.search.util.CommonUtils;
/*     */ import com.weaver.ecology.search.util.SysConfigure;
/*     */ import java.io.File;
/*     */ import java.io.IOException;
/*     */ import org.apache.log4j.Logger;
/*     */ import org.apache.lucene.document.Document;
/*     */ import org.apache.lucene.document.NumberTools;
/*     */ import org.apache.lucene.index.IndexReader;
/*     */ import org.apache.lucene.index.IndexWriter;
/*     */ import org.apache.lucene.index.MultiReader;
/*     */ import org.apache.lucene.index.Term;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class IndexEntityImpl
/*     */   implements IndexEntity
/*     */ {
/*     */   private String indexDb;
/*  40 */   private IndexWriter writer = null;
/*     */ 
/*     */ 
/*     */   
/*  44 */   private IndexReader reader = null;
/*     */ 
/*     */ 
/*     */   
/*  48 */   public static int docid = 0;
/*     */ 
/*     */ 
/*     */   
/*  52 */   private static Logger logger = Logger.getLogger(IndexEntityImpl.class);
/*     */ 
/*     */ 
/*     */   
/*  56 */   private int docs = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public IndexEntityImpl() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public IndexEntityImpl(String paramString) throws IOException {
/*  71 */     initIndex(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int newIndexDb() throws IOException {
/*  80 */     this.writer = new IndexWriter(this.indexDb, CommonUtils.getAnalyzer(), true);
/*  81 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int incrementIndex() throws IOException {
/*  90 */     this.writer = new IndexWriter(this.indexDb, CommonUtils.getAnalyzer(), false);
/*  91 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void initIndex(String paramString) throws IOException {
/* 100 */     this.docs = 0;
/* 101 */     this.indexDb = SysConfigure.getIndexDbPath(paramString);
/*     */     
/* 103 */     if (logger.isDebugEnabled()) logger.debug("初始化索引库:" + this.indexDb);
/*     */     
/* 105 */     File file = new File(this.indexDb);
/*     */     
/* 107 */     if (!file.exists()) file.mkdirs();
/*     */     
/* 109 */     if (IndexReader.indexExists(file)) {
/* 110 */       incrementIndex();
/*     */     } else {
/* 112 */       newIndexDb();
/* 113 */     }  this.writer.setMaxMergeDocs(SysConfigure.getMaxCacheDocs());
/* 114 */     this.writer.setMergeFactor(SysConfigure.getMergeFactor());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int overIndex() throws IOException {
/* 123 */     if (logger.isInfoEnabled()) logger.info("添加索引文档数:" + this.docs); 
/* 124 */     if (this.writer != null) {
/*     */       try {
/* 126 */         this.writer.optimize();
/* 127 */         this.writer.flush();
/* 128 */         if (logger.isInfoEnabled()) logger.info("IndexWriter,合并索引文件,创建结束!");
/*     */       
/* 130 */       } catch (Exception exception) {
/* 131 */         logger.error("合并索引文件出现错误...强行中止." + exception);
/*     */       } finally {
/*     */         
/* 134 */         if (this.writer != null) this.writer.close(); 
/* 135 */         this.writer = null;
/*     */       } 
/*     */     }
/*     */     
/* 139 */     if (this.reader != null) {
/* 140 */       if (logger.isInfoEnabled()) logger.info("IndexReader关闭!"); 
/* 141 */       this.reader.close();
/* 142 */       this.reader = null;
/*     */     } 
/* 144 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int addIndexDocument(IDocumentEntity paramIDocumentEntity) throws IOException {
/* 154 */     byte b = 0;
/*     */     try {
/* 156 */       Document document = paramIDocumentEntity.generateDoc();
/* 157 */       if (document != null) {
/* 158 */         this.writer.addDocument(document);
/* 159 */         this.docs++;
/*     */       } else {
/* 161 */         b = -1;
/* 162 */         logger.error("索引不成功的...." + docid);
/*     */       }
/*     */     
/* 165 */     } catch (Exception exception) {
/* 166 */       logger.error("无法添加索引...." + docid);
/* 167 */       logger.error("无法添加索引...." + exception);
/* 168 */       b = -1;
/*     */     } 
/* 170 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int deleteDocument(Term paramTerm) throws IOException {
/* 180 */     int i = 0;
/* 181 */     if (this.reader != null) {
/* 182 */       i = this.reader.deleteDocuments(paramTerm);
/* 183 */       if (logger.isInfoEnabled()) logger.info("已删除文档数:" + i); 
/*     */     } 
/* 185 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int deleteDocument(int paramInt) throws IOException {
/* 195 */     boolean bool = false;
/* 196 */     if (this.reader != null) {
/* 197 */       this.reader.deleteDocument(paramInt);
/* 198 */       if (logger.isInfoEnabled()) logger.info("已删除Document.id{" + paramInt + "}文档!"); 
/*     */     } 
/* 200 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int deleteDocById(int paramInt) throws IOException {
/* 210 */     int i = 0;
/* 211 */     if (this.reader != null) {
/* 212 */       Term term = new Term("id", NumberTools.longToString(paramInt));
/* 213 */       i = this.reader.deleteDocuments(term);
/* 214 */       if (logger.isInfoEnabled()) logger.info("已删除文档数:" + i + ",Term:" + term); 
/*     */     } 
/* 216 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void initUpdateIndex(String[] paramArrayOfString) throws IOException {
/* 225 */     if (logger.isDebugEnabled()) logger.debug("初始化删除文档用的索引库>>>" + CommonUtils.getJoinArray(paramArrayOfString, ",")); 
/* 226 */     IndexReader[] arrayOfIndexReader = new IndexReader[paramArrayOfString.length];
/* 227 */     String str = null;
/* 228 */     for (byte b = 0; b < paramArrayOfString.length; b++) {
/* 229 */       str = SysConfigure.getIndexDbPath(paramArrayOfString[b]);
/* 230 */       arrayOfIndexReader[b] = IndexReader.open(str);
/*     */     } 
/* 232 */     this.reader = (IndexReader)new MultiReader(arrayOfIndexReader);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getAddDocuments() {
/* 241 */     return this.docs;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/ecology/search/index/impl/IndexEntityImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */