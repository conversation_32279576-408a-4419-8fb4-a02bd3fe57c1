/*    */ package com.weaver.ecology.search.web.base;
/*    */ 
/*    */ import com.weaver.ecology.search.util.SysConfigure;
/*    */ import java.io.UnsupportedEncodingException;
/*    */ import java.util.Iterator;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import javax.servlet.jsp.PageContext;
/*    */ import org.apache.log4j.Logger;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class AbstractNonspringBaseBean
/*    */ {
/* 34 */   private static Logger logger = Logger.getLogger(AbstractNonspringBaseBean.class);
/*    */   
/* 36 */   protected PageContext pageContext = null;
/*    */ 
/*    */ 
/*    */   
/* 40 */   protected HttpServletRequest request = null;
/*    */ 
/*    */ 
/*    */   
/* 44 */   protected HttpServletResponse response = null;
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public PageContext getPageContext() {
/* 50 */     return this.pageContext;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setPageContext(PageContext paramPageContext) {
/* 58 */     this.pageContext = paramPageContext;
/* 59 */     this.request = (HttpServletRequest)this.pageContext.getRequest();
/* 60 */     this.response = (HttpServletResponse)this.pageContext.getResponse();
/*    */ 
/*    */     
/* 63 */     User user = HrmUserVarify.checkUser(this.request, this.response);
/* 64 */     SysConfigure.setUserLanguage(user.getLanguage());
/*    */ 
/*    */     
/* 67 */     try { this.request.setCharacterEncoding("UTF-8"); }
/* 68 */     catch (UnsupportedEncodingException unsupportedEncodingException) { logger.error("设置request编码时异常!", unsupportedEncodingException); }
/*    */   
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   protected void mappingToView(Map paramMap) {
/* 75 */     Iterator<E> iterator = paramMap.keySet().iterator();
/* 76 */     String str = null;
/* 77 */     while (iterator.hasNext()) {
/* 78 */       str = iterator.next().toString();
/* 79 */       this.pageContext.setAttribute(str, paramMap.get(str));
/*    */     } 
/*    */   }
/*    */   
/*    */   public abstract void setInit(boolean paramBoolean);
/*    */   
/*    */   protected abstract void setExecute(int paramInt);
/*    */   
/*    */   public abstract boolean isValidate();
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/ecology/search/web/base/AbstractNonspringBaseBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */