/*    */ package com.weaver.upgrade.domain;
/*    */ 
/*    */ import com.weaver.upgrade.FunctionUpgradeUtil;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.system.License;
/*    */ 
/*    */ public class Upgrade081
/*    */ {
/*  9 */   public static RecordSet rs = new RecordSet();
/* 10 */   public static String cid = "1928409";
/* 11 */   String sysCId = (new License()).getCId();
/*    */   
/*    */   public boolean upgrade() {
/* 14 */     System.out.println("CId==========" + this.sysCId);
/* 15 */     if (cid.equals(this.sysCId)) {
/* 16 */       System.out.println("Upgrade081更新menucontrollist表 start");
/*    */       try {
/* 18 */         String str = "update menucontrollist set isopen = '" + FunctionUpgradeUtil.getMenuStatus(10284, 1, Integer.parseInt(cid)) + "'" + " where menuid = '" + FunctionUpgradeUtil.getMenuId(10284, Integer.parseInt(cid)) + "'" + "and type='top'";
/*    */ 
/*    */         
/* 21 */         System.out.println("执行sql :" + str);
/* 22 */         rs.executeSql(str);
/* 23 */         System.out.println("Upgrade081更新menucontrollist表 end");
/* 24 */       } catch (Exception exception) {}
/*    */       
/* 26 */       return true;
/*    */     } 
/* 28 */     System.out.println("Upgrade081参数cid与系统获取cid不匹配,不更新");
/* 29 */     return false;
/*    */   }
/*    */   
/*    */   public boolean stop() {
/* 33 */     System.out.println("CId==========" + this.sysCId);
/* 34 */     if (cid.equals(this.sysCId)) {
/* 35 */       System.out.println("Upgrade081更新menucontrollist表 start");
/*    */       try {
/* 37 */         String str = "update menucontrollist set isopen = '" + FunctionUpgradeUtil.getMenuStatus(10284, 0, Integer.parseInt(cid)) + "'" + " where menuid = '" + FunctionUpgradeUtil.getMenuId(10284, Integer.parseInt(cid)) + "'" + "and type='top'";
/*    */ 
/*    */         
/* 40 */         System.out.println("执行sql :" + str);
/* 41 */         rs.executeSql(str);
/* 42 */         System.out.println("Upgrade081更新menucontrollist表 end");
/* 43 */       } catch (Exception exception) {}
/*    */       
/* 45 */       return true;
/*    */     } 
/* 47 */     System.out.println("Upgrade081参数cid与系统获取cid不匹配,不更新");
/* 48 */     return false;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/weaver/upgrade/domain/Upgrade081.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */