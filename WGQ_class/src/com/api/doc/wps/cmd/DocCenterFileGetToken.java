/*    */ package com.api.doc.wps.cmd;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.wps.doccenter.utils.TokenUtil;
/*    */ import weaver.wps.logging.Logger;
/*    */ import weaver.wps.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ public class DocCenterFileGetToken
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/* 18 */   private Logger log = LoggerFactory.getLogger(DocCenterFileGetToken.class);
/*    */   private User user;
/*    */   private Map<String, Object> params;
/*    */   
/*    */   public DocCenterFileGetToken(Map<String, Object> paramMap, User paramUser) {
/* 23 */     this.user = paramUser;
/* 24 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 29 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 34 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*    */ 
/*    */     
/* 37 */     int i = Util.getIntValue(Util.null2String(this.params.get("ecfileid")));
/* 38 */     int j = i;
/* 39 */     String str1 = Util.null2String(this.params.get("fromMould"));
/* 40 */     String str2 = Util.null2String(this.params.get("filename"));
/* 41 */     String str3 = Util.null2String(this.params.get("wpsfileid"));
/* 42 */     String str4 = Util.null2String(this.params.get("_w_tokentype"));
/* 43 */     String str5 = Util.null2String(this.params.get("sessionId"));
/*    */     
/* 45 */     int k = Util.getIntValue(Util.null2String(this.params.get("iscollaborativedoc")), 0);
/*    */     
/* 47 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 48 */     hashMap2.put("userid", this.user.getUID() + "");
/* 49 */     hashMap2.put("fromMould", str1);
/* 50 */     hashMap2.put("ecfileid", i + "");
/* 51 */     hashMap2.put("iscollaborativedoc", k + "");
/* 52 */     hashMap2.put("_w_tokentype", str4 + "");
/* 53 */     hashMap2.put("sessionId", str5);
/*    */     
/* 55 */     TokenUtil tokenUtil = new TokenUtil();
/* 56 */     hashMap1.put("token", tokenUtil.encryptForPageToken(hashMap2));
/* 57 */     hashMap1.put("timeout", tokenUtil.getPageTokenTimeout());
/*    */     
/* 59 */     return (Map)hashMap1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/doc/wps/cmd/DocCenterFileGetToken.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */