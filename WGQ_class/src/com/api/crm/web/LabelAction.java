/*     */ package com.api.crm.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Path("/crm/label")
/*     */ public class LabelAction
/*     */   extends BaseAction
/*     */ {
/*     */   @GET
/*     */   @Path("/list")
/*     */   @Produces({"text/plain"})
/*     */   public String getLabelListInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  36 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  37 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/*  38 */     Map map1 = this.labelService.getLabelListInfo(user, map);
/*  39 */     return JSONObject.toJSONString(map1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/edit")
/*     */   @Produces({"text/plain"})
/*     */   public String crmTagEdit(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  52 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  53 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/*  54 */     Map map1 = this.labelService.doEdit(user, map);
/*  55 */     return JSONObject.toJSONString(map1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/delete")
/*     */   @Produces({"text/plain"})
/*     */   public String crmTagDelete(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  68 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  69 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/*  70 */     Map map1 = this.labelService.doDelete(user, map);
/*  71 */     return JSONObject.toJSONString(map1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/markLabel")
/*     */   @Produces({"text/plain"})
/*     */   public String markLabel(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  84 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/*  85 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  86 */     Map map1 = this.labelService.markLabel(user, map);
/*  87 */     return JSONObject.toJSONString(map1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/markImportant")
/*     */   @Produces({"text/plain"})
/*     */   public String markImportant(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 100 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/* 101 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 102 */     Map map1 = this.labelService.markImportant(user, map);
/* 103 */     return JSONObject.toJSONString(map1);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/crm/web/LabelAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */