/*     */ package com.api.crm.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Path("/crm/address")
/*     */ public class AddressAction
/*     */   extends BaseAction
/*     */ {
/*     */   @GET
/*     */   @Path("/addresslist")
/*     */   @Produces({"text/plain"})
/*     */   public String getAddresslist(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  35 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  36 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/*  37 */     Map map1 = this.addressService.getAddresslist(user, map);
/*  38 */     return JSONObject.toJSONString(map1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/addForm")
/*     */   @Produces({"text/plain"})
/*     */   public String getAddressAddForm(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  51 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  52 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/*  53 */     Map map1 = this.addressService.getAddressAddForm(user, map);
/*  54 */     return JSONObject.toJSONString(map1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/editForm")
/*     */   @Produces({"text/plain"})
/*     */   public String getAddressEditForm(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  67 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  68 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/*  69 */     Map map1 = this.addressService.getAddressEditForm(user, map);
/*  70 */     return JSONObject.toJSONString(map1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/addressAdd")
/*     */   @Produces({"text/plain"})
/*     */   public String doAddressAdd(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  83 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  84 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/*  85 */     map.put("remoteAddr", paramHttpServletRequest.getRemoteAddr());
/*  86 */     Map map1 = this.addressService.doAddressAdd(user, map);
/*  87 */     return JSONObject.toJSONString(map1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/addressEdit")
/*     */   @Produces({"text/plain"})
/*     */   public String doAddressEdit(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 100 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 101 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/* 102 */     Map map1 = this.addressService.doAddressEdit(user, map);
/* 103 */     return JSONObject.toJSONString(map1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/addressDelete")
/*     */   @Produces({"text/plain"})
/*     */   public String doAddressDelete(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 116 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 117 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/* 118 */     Map map1 = this.addressService.doAddressDelete(user, map);
/* 119 */     return JSONObject.toJSONString(map1);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/crm/web/AddressAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */