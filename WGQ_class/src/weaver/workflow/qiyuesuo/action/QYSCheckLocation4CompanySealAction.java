/*    */ package weaver.workflow.qiyuesuo.action;
/*    */ 
/*    */ import weaver.general.Util;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSSignatoryType;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSStamperType;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class QYSCheckLocation4CompanySealAction
/*    */   extends QYSCheckLocation4CompanyAction
/*    */   implements Action
/*    */ {
/*    */   public QYSCheckLocation4CompanySealAction() {
/* 19 */     setSignatoryType(QYSSignatoryType.COMPANY);
/* 20 */     setRectType(QYSStamperType.SEAL_CORPORATE);
/* 21 */     setAllDocuments(true);
/*    */   }
/*    */   
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 25 */     QYSLogUtil qYSLogUtil = new QYSLogUtil(getClass().getName());
/* 26 */     qYSLogUtil.info("start");
/* 27 */     String str = Util.null2String(checkLocation(paramRequestInfo));
/* 28 */     qYSLogUtil.info("end--" + str);
/* 29 */     return paramRequestInfo.getRequestManager().getMessageid().isEmpty() ? "1" : "0";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/action/QYSCheckLocation4CompanySealAction.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */