/*    */ package weaver.workflow.qiyuesuo.action;
/*    */ 
/*    */ import weaver.general.Util;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSSignatoryType;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ 
/*    */ public class QYSAutoSign4PlatformLPAction
/*    */   extends QYSAutoSign
/*    */   implements Action {
/*    */   public QYSAutoSign4PlatformLPAction() {
/* 13 */     setSignerType(QYSSignatoryType.LP);
/*    */   }
/*    */   
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 17 */     QYSLogUtil qYSLogUtil = new QYSLogUtil(getClass().getName());
/* 18 */     qYSLogUtil.info("start--");
/* 19 */     String str = Util.null2String(autoSign(paramRequestInfo));
/* 20 */     qYSLogUtil.info("end--" + str);
/* 21 */     return paramRequestInfo.getRequestManager().getMessageid().isEmpty() ? "1" : "0";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/action/QYSAutoSign4PlatformLPAction.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */