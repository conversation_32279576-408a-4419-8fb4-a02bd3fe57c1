/*     */ package weaver.workflow.qiyuesuo.action;
/*     */ 
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.qiyuesuo.QYSInterface;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSSignatoryType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSStamperType;
/*     */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*     */ import weaver.workflow.request.RequestManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class QYSCheckLocation
/*     */   extends BaseBean
/*     */ {
/*     */   private QYSLogUtil log;
/*  25 */   private QYSSignatoryType signatoryType = null;
/*  26 */   private QYSStamperType rectType = null;
/*     */   private boolean isAllDocuments = false;
/*     */   
/*     */   public QYSCheckLocation() {
/*  30 */     this.log = new QYSLogUtil(getClass().getName());
/*     */   }
/*     */   public boolean isAllDocuments() {
/*  33 */     return this.isAllDocuments;
/*     */   }
/*     */   public void setAllDocuments(boolean paramBoolean) {
/*  36 */     this.isAllDocuments = paramBoolean;
/*     */   }
/*     */   public QYSStamperType getRectType() {
/*  39 */     return this.rectType;
/*     */   }
/*     */   public void setRectType(QYSStamperType paramQYSStamperType) {
/*  42 */     this.rectType = paramQYSStamperType;
/*     */   }
/*     */   public QYSSignatoryType getSignatoryType() {
/*  45 */     return this.signatoryType;
/*     */   }
/*     */   
/*     */   public void setSignatoryType(QYSSignatoryType paramQYSSignatoryType) {
/*  49 */     this.signatoryType = paramQYSSignatoryType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkLocation(RequestInfo paramRequestInfo) {
/*  59 */     int i = Util.getIntValue(paramRequestInfo.getRequestid(), 0);
/*  60 */     this.log.info("checkLocation--start--requestid:" + i + "--signatoryType:" + Util.null2String(this.signatoryType) + "--rectType:" + Util.null2String(this.rectType) + "--isAllDocuments:" + this.isAllDocuments);
/*     */ 
/*     */     
/*  63 */     RequestManager requestManager = paramRequestInfo.getRequestManager();
/*  64 */     User user = requestManager.getUser();
/*  65 */     QYSResponse qYSResponse = new QYSResponse((new QYSInterface(user, QYSLogType.ACTION)).checkSignLocation(i, this.signatoryType, this.rectType, this.isAllDocuments));
/*     */     
/*  67 */     String str = "";
/*  68 */     if (0 != qYSResponse.getCode()) {
/*  69 */       str = qYSResponse.getMessage();
/*  70 */       this.log.error("校验签署方是否已指定签署位置失败！" + str);
/*     */     } 
/*  72 */     if (!qYSResponse.getBooleanValue("hasLocations")) {
/*  73 */       String str1 = Util.null2String(qYSResponse.getString("tenantName"));
/*  74 */       String str2 = Util.null2String(qYSResponse.getString("tenantType"));
/*  75 */       this.log.info("--checkLocation--tenantName:" + str1 + "--tenantType:" + str2);
/*  76 */       if (str.isEmpty() && this.signatoryType != null) {
/*  77 */         str1 = "：" + str1 + "，";
/*  78 */         if (QYSSignatoryType.INTERNAL_COMPANY == this.signatoryType || QYSSignatoryType.CORPORATE == this.signatoryType) {
/*     */ 
/*     */           
/*  81 */           str = SystemEnv.getHtmlLabelName(386011, user.getLanguage()) + str1 + SystemEnv.getHtmlLabelName(390386, user.getLanguage());
/*  82 */           if (this.rectType == QYSStamperType.SEAL_CORPORATE) {
/*  83 */             str = SystemEnv.getHtmlLabelName(386011, user.getLanguage()) + str1 + SystemEnv.getHtmlLabelName(517096, user.getLanguage());
/*  84 */           } else if (this.rectType == QYSStamperType.ACROSS_PAGE) {
/*  85 */             str = SystemEnv.getHtmlLabelName(386011, user.getLanguage()) + str1 + SystemEnv.getHtmlLabelName(517095, user.getLanguage());
/*  86 */           } else if (this.rectType == QYSStamperType.SEAL_PERSONAL) {
/*  87 */             str = SystemEnv.getHtmlLabelName(386011, user.getLanguage()) + str1 + SystemEnv.getHtmlLabelName(541203, user.getLanguage());
/*     */           } 
/*  89 */         } else if (QYSSignatoryType.COMPANY == this.signatoryType) {
/*  90 */           str = SystemEnv.getHtmlLabelName(27417, user.getLanguage()) + str1 + SystemEnv.getHtmlLabelName(390386, user.getLanguage());
/*  91 */           if (this.rectType == QYSStamperType.SEAL_CORPORATE) {
/*  92 */             str = SystemEnv.getHtmlLabelName(27417, user.getLanguage()) + str1 + SystemEnv.getHtmlLabelName(517096, user.getLanguage());
/*  93 */           } else if (this.rectType == QYSStamperType.ACROSS_PAGE) {
/*  94 */             str = SystemEnv.getHtmlLabelName(27417, user.getLanguage()) + str1 + SystemEnv.getHtmlLabelName(517095, user.getLanguage());
/*     */           } 
/*  96 */         } else if (QYSSignatoryType.PERSONAL == this.signatoryType) {
/*  97 */           str = SystemEnv.getHtmlLabelName(386012, user.getLanguage()) + str1 + SystemEnv.getHtmlLabelName(390386, user.getLanguage());
/*  98 */           if (this.rectType == QYSStamperType.SEAL_CORPORATE) {
/*  99 */             str = SystemEnv.getHtmlLabelName(386012, user.getLanguage()) + str1 + SystemEnv.getHtmlLabelName(517096, user.getLanguage());
/* 100 */           } else if (this.rectType == QYSStamperType.ACROSS_PAGE) {
/* 101 */             str = SystemEnv.getHtmlLabelName(386012, user.getLanguage()) + str1 + SystemEnv.getHtmlLabelName(517095, user.getLanguage());
/* 102 */           } else if (this.rectType == QYSStamperType.SEAL_PERSONAL) {
/* 103 */             str = SystemEnv.getHtmlLabelName(386012, user.getLanguage()) + str1 + SystemEnv.getHtmlLabelName(541203, user.getLanguage());
/*     */           } 
/*     */         } else {
/* 106 */           str = SystemEnv.getHtmlLabelNames("390386", user.getLanguage());
/*     */         } 
/*     */       } 
/*     */       
/* 110 */       if (str.isEmpty() && this.signatoryType == null && this.rectType == null) {
/* 111 */         str = qYSResponse.getMessage().isEmpty() ? "校验文件是否所有文档已指定签署位置失败！文件未指定签署位置" : qYSResponse.getMessage();
/*     */       }
/*     */     } 
/* 114 */     if (!str.isEmpty()) {
/* 115 */       requestManager.setMessageid("126221");
/* 116 */       requestManager.setMessagecontent(str);
/*     */     } 
/*     */     
/* 119 */     this.log.info("checkLocation--end--requestid:" + i + "--signatoryType:" + Util.null2String(this.signatoryType) + "--rectType:" + Util.null2String(this.rectType) + "--isAllDocuments:" + this.isAllDocuments);
/*     */     
/* 121 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/action/QYSCheckLocation.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */