/*    */ package weaver.workflow.qiyuesuo.action;
/*    */ 
/*    */ import weaver.general.Util;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class QYSCheckLocation4AllDocumentsAction
/*    */   extends QYSCheckLocation
/*    */   implements Action
/*    */ {
/*    */   public QYSCheckLocation4AllDocumentsAction() {
/* 19 */     setAllDocuments(true);
/*    */   }
/*    */ 
/*    */   
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 24 */     QYSLogUtil qYSLogUtil = new QYSLogUtil(getClass().getName());
/* 25 */     qYSLogUtil.info("start");
/* 26 */     String str = Util.null2String(checkLocation(paramRequestInfo));
/* 27 */     qYSLogUtil.info("end--" + str);
/* 28 */     return paramRequestInfo.getRequestManager().getMessageid().isEmpty() ? "1" : "0";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/action/QYSCheckLocation4AllDocumentsAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */