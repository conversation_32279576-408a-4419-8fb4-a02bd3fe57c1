/*     */ package weaver.workflow.qiyuesuo.action;
/*     */ 
/*     */ import java.lang.reflect.Constructor;
/*     */ import java.lang.reflect.Method;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.workflow.qiyuesuo.QYSInterface;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*     */ import weaver.workflow.request.RequestManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class QYSCreateWorkflowAuditContract
/*     */   extends BaseBean
/*     */   implements Action
/*     */ {
/*     */   public String execute(RequestInfo paramRequestInfo) {
/*  30 */     String str = paramRequestInfo.getRequestid();
/*  31 */     writeLog("QYSCreateWorkflowAuditContract--start--requestid:" + str);
/*     */ 
/*     */     
/*  34 */     SyncCreateWorkflowAuditContract syncCreateWorkflowAuditContract = new SyncCreateWorkflowAuditContract(paramRequestInfo);
/*  35 */     (new Thread(syncCreateWorkflowAuditContract)).start();
/*     */     
/*  37 */     return "1";
/*     */   }
/*     */   
/*     */   private class SyncCreateWorkflowAuditContract extends BaseBean implements Runnable {
/*     */     private RequestInfo request;
/*     */     
/*     */     public SyncCreateWorkflowAuditContract(RequestInfo param1RequestInfo) {
/*  44 */       this.request = param1RequestInfo;
/*     */     }
/*     */ 
/*     */     
/*     */     public void run() {
/*  49 */       String str1 = this.request.getRequestid();
/*  50 */       String str2 = this.request.getWorkflowid();
/*  51 */       RequestManager requestManager = this.request.getRequestManager();
/*  52 */       String str3 = requestManager.getSrc();
/*  53 */       int i = requestManager.getNodeid();
/*  54 */       writeLog("SyncCreateWorkflowAuditContract--start--requestid:" + str1 + "--src:" + str3);
/*     */       
/*  56 */       if ("submit".equals(str3)) {
/*  57 */         writeLog("SyncCreateWorkflowAuditContract--requestid:" + str1 + "--nodeid:" + i);
/*  58 */         User user = requestManager.getUser();
/*  59 */         ArrayList<Integer> arrayList = new ArrayList();
/*  60 */         ArrayList<String> arrayList1 = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  74 */         String str4 = requestManager.getCurrentDate();
/*  75 */         String str5 = requestManager.getCurrentTime();
/*  76 */         String str6 = str4 + " " + str5;
/*  77 */         writeLog("SyncCreateWorkflowAuditContract--operatedate:" + str4 + "--operatetime:" + str5 + "--audittime:" + str6);
/*  78 */         arrayList.add(Integer.valueOf(user.getUID()));
/*  79 */         arrayList1.add(str6);
/*     */         
/*  81 */         RecordSet recordSet = new RecordSet();
/*  82 */         byte b = 0;
/*     */         while (true) {
/*  84 */           recordSet.executeQuery("select 1 from workflow_requestLog where operatedate = ? and operatetime=?", new Object[] { str4, str5 });
/*  85 */           if (recordSet.next() || b == 5) {
/*     */             break;
/*     */           }
/*     */           
/*     */           try {
/*  90 */             Thread.sleep(2000L);
/*  91 */           } catch (InterruptedException interruptedException) {
/*  92 */             interruptedException.printStackTrace();
/*     */           } finally {
/*  94 */             b++;
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/*  99 */         HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 100 */         recordSet.executeQuery("select id from workflow_nodehtmllayout where workflowid=? and nodeid=? and type=0", new Object[] { str2, Integer.valueOf(i) });
/* 101 */         if (recordSet.next()) {
/* 102 */           String str = recordSet.getString("id");
/* 103 */           hashMap.put("modeid", str);
/*     */         } 
/* 105 */         hashMap.put("requestid", str1);
/* 106 */         hashMap.put("onlyHtml", "0");
/*     */ 
/*     */         
/* 109 */         String str7 = "";
/*     */         try {
/* 111 */           Class<?> clazz = Class.forName("com.engine.workflow.util.WfToDocUtil");
/* 112 */           Constructor<?> constructor = clazz.getConstructor(new Class[0]);
/* 113 */           Method method = clazz.getMethod("getFileId", new Class[] { Map.class, User.class });
/* 114 */           Map map = (Map)method.invoke(constructor.newInstance(new Object[0]), new Object[] { hashMap, user });
/* 115 */           str7 = Util.null2String(map.get("pdf"));
/* 116 */           writeLog("SyncCreateWorkflowAuditContract--pdfImagefileid:" + str7);
/* 117 */         } catch (Exception exception) {
/* 118 */           String str = "创建电子用印凭证失败！E8不支持该接口功能";
/* 119 */           exception.printStackTrace();
/* 120 */           writeLog("SyncCreateWorkflowAuditContract--" + str);
/*     */           
/*     */           return;
/*     */         } 
/*     */         
/* 125 */         QYSResponse qYSResponse = new QYSResponse((new QYSInterface(user, QYSLogType.ACTION)).createWorkflowAuditContract(Util.getIntValue(str1), Util.getIntValue(str7), arrayList, arrayList1));
/*     */         
/* 127 */         if (qYSResponse.getCode() != 0) {
/* 128 */           String str = qYSResponse.getMessage();
/* 129 */           if (str.isEmpty()) {
/* 130 */             str = "创建电子用印凭证失败！请联系管理员处理";
/*     */           }
/* 132 */           writeLog("SyncCreateWorkflowAuditContract--创建电子用印凭证失败！" + str);
/*     */           
/* 134 */           requestManager.setMessageid("126221");
/* 135 */           requestManager.setMessagecontent(str);
/*     */         } 
/*     */ 
/*     */         
/* 139 */         if (qYSResponse.getCode() == 0) {
/* 140 */           String str = qYSResponse.getString("downloadDocId");
/* 141 */           if (Util.getIntValue(str, -1) == -1) {
/* 142 */             requestManager.setMessageid("126221");
/* 143 */             requestManager.setMessagecontent("创建电子用印凭证失败！请联系管理员处理");
/*     */           } 
/* 145 */           writeLog("SyncCreateWorkflowAuditContract--downloadDocId:" + str);
/* 146 */           recordSet.executeUpdate("update workflow_requestLog set annexdocids = ? where requestid = ?  and nodeid = ? and operatedate = ? and operatetime=?", new Object[] { str, str1, Integer.valueOf(i), str4, str5 });
/*     */         } 
/*     */       } 
/*     */       
/* 150 */       writeLog("SyncCreateWorkflowAuditContract--end--requestid:" + str1);
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/action/QYSCreateWorkflowAuditContract.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */