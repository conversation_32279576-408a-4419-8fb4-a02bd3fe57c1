/*    */ package weaver.workflow.qiyuesuo.action;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.workflow.qiyuesuo.QYSInterface;
/*    */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ import weaver.workflow.request.RequestManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class QYSCreateContractAction
/*    */   extends BaseBean
/*    */   implements Action
/*    */ {
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 24 */     RequestManager requestManager = paramRequestInfo.getRequestManager();
/* 25 */     int i = requestManager.getRequestid();
/* 26 */     String str = Util.null2String(requestManager.getSrc());
/*    */     
/* 28 */     QYSLogUtil qYSLogUtil = new QYSLogUtil(getClass().getName());
/* 29 */     qYSLogUtil.info("start--requestid:" + i + "--src:" + str);
/*    */     
/* 31 */     User user = requestManager.getUser();
/* 32 */     QYSResponse qYSResponse = new QYSResponse((new QYSInterface(user, QYSLogType.ACTION)).doSign(i));
/* 33 */     if (0 != qYSResponse.getCode()) {
/* 34 */       String str1 = qYSResponse.getMessage();
/* 35 */       qYSLogUtil.error("创建合同失败！" + str1);
/*    */       
/* 37 */       if (str1.isEmpty()) {
/* 38 */         str1 = SystemEnv.getHtmlLabelName(390818, user.getLanguage());
/*    */       } else {
/*    */         
/* 41 */         str1 = str1.replaceAll("合同", "文件");
/*    */       } 
/*    */       
/* 44 */       requestManager.setMessageid("126221");
/* 45 */       requestManager.setMessagecontent(str1);
/*    */     } 
/*    */     
/* 48 */     qYSLogUtil.info("end--requestid:" + i + "--src:" + str);
/* 49 */     return requestManager.getMessageid().isEmpty() ? "1" : "0";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/action/QYSCreateContractAction.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */