/*    */ package weaver.workflow.qiyuesuo.action;
/*    */ 
/*    */ import weaver.general.Util;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSSignatoryType;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ 
/*    */ public class QYSAutoSign4AllPersonalAction
/*    */   extends QYSAutoSign
/*    */   implements Action {
/*    */   public QYSAutoSign4AllPersonalAction() {
/* 13 */     setSignerType(QYSSignatoryType.PERSONAL);
/* 14 */     setIndex(0);
/*    */   }
/*    */ 
/*    */   
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 19 */     QYSLogUtil qYSLogUtil = new QYSLogUtil(getClass().getName());
/* 20 */     qYSLogUtil.info("start");
/* 21 */     String str = Util.null2String(autoSign(paramRequestInfo));
/* 22 */     qYSLogUtil.info("end--" + str);
/* 23 */     return paramRequestInfo.getRequestManager().getMessageid().isEmpty() ? "1" : "0";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/action/QYSAutoSign4AllPersonalAction.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */