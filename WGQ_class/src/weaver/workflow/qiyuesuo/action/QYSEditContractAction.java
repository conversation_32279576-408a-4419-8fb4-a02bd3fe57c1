/*    */ package weaver.workflow.qiyuesuo.action;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.workflow.qiyuesuo.QYSInterface;
/*    */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ import weaver.workflow.request.RequestManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class QYSEditContractAction
/*    */   extends BaseBean
/*    */   implements Action
/*    */ {
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 24 */     int i = Util.getIntValue(paramRequestInfo.getRequestid(), 0);
/*    */     
/* 26 */     QYSLogUtil qYSLogUtil = new QYSLogUtil(getClass().getName());
/* 27 */     qYSLogUtil.info("start--requestid:" + i);
/*    */     
/* 29 */     RequestManager requestManager = paramRequestInfo.getRequestManager();
/*    */     
/* 31 */     QYSResponse qYSResponse = new QYSResponse((new QYSInterface(requestManager.getUser(), QYSLogType.ACTION)).editContract(i));
/* 32 */     if (0 != qYSResponse.getCode()) {
/* 33 */       String str = qYSResponse.getMessage();
/* 34 */       qYSLogUtil.error("编辑合同失败！" + str);
/*    */       
/* 36 */       if (str.isEmpty()) {
/* 37 */         str = "编辑合同失败！";
/*    */       }
/*    */       
/* 40 */       requestManager.setMessageid("126221");
/* 41 */       requestManager.setMessagecontent(str);
/*    */     } 
/*    */     
/* 44 */     qYSLogUtil.info("end--requestid:" + i);
/* 45 */     return requestManager.getMessageid().isEmpty() ? "1" : "0";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/action/QYSEditContractAction.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */