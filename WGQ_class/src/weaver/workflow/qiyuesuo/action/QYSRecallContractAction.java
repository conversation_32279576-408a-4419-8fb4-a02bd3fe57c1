/*    */ package weaver.workflow.qiyuesuo.action;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.workflow.qiyuesuo.QYSInterface;
/*    */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ import weaver.workflow.request.RequestManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class QYSRecallContractAction
/*    */   extends BaseBean
/*    */   implements Action
/*    */ {
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 24 */     int i = Util.getIntValue(paramRequestInfo.getRequestid(), 0);
/*    */     
/* 26 */     QYSLogUtil qYSLogUtil = new QYSLogUtil(getClass().getName());
/* 27 */     qYSLogUtil.info("start--requestid:" + i);
/*    */     
/* 29 */     RequestManager requestManager = paramRequestInfo.getRequestManager();
/* 30 */     User user = requestManager.getUser();
/* 31 */     String str = "reject".equals(requestManager.getSrc()) ? SystemEnv.getHtmlLabelName(24449, user.getLanguage()) : "";
/*    */     
/* 33 */     QYSResponse qYSResponse = new QYSResponse((new QYSInterface(user, QYSLogType.ACTION)).recallContract(i, str, 0));
/* 34 */     if (0 != qYSResponse.getCode()) {
/* 35 */       String str1 = qYSResponse.getMessage();
/* 36 */       qYSLogUtil.error(SystemEnv.getHtmlLabelName(390430, user.getLanguage()) + str1);
/*    */       
/* 38 */       if (str1.isEmpty()) {
/* 39 */         str1 = SystemEnv.getHtmlLabelName(390430, user.getLanguage());
/*    */       }
/*    */       
/* 42 */       requestManager.setMessageid("126221");
/* 43 */       requestManager.setMessagecontent(str1);
/*    */     } 
/*    */     
/* 46 */     qYSLogUtil.info("end--requestid:" + i);
/* 47 */     return requestManager.getMessageid().isEmpty() ? "1" : "0";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/action/QYSRecallContractAction.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */