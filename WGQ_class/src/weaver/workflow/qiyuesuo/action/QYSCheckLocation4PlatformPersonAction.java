/*    */ package weaver.workflow.qiyuesuo.action;
/*    */ 
/*    */ import weaver.general.Util;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSSignatoryType;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSStamperType;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class QYSCheckLocation4PlatformPersonAction
/*    */   extends QYSCheckLocation
/*    */   implements Action
/*    */ {
/*    */   public QYSCheckLocation4PlatformPersonAction() {
/* 18 */     setSignatoryType(QYSSignatoryType.INTERNAL_COMPANY);
/* 19 */     setRectType(QYSStamperType.SEAL_PERSONAL);
/* 20 */     setAllDocuments(true);
/*    */   }
/*    */   
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 24 */     QYSLogUtil qYSLogUtil = new QYSLogUtil(getClass().getName());
/* 25 */     qYSLogUtil.info("start");
/* 26 */     String str = Util.null2String(checkLocation(paramRequestInfo));
/* 27 */     qYSLogUtil.info("end--" + str);
/* 28 */     return paramRequestInfo.getRequestManager().getMessageid().isEmpty() ? "1" : "0";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/action/QYSCheckLocation4PlatformPersonAction.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */