/*    */ package weaver.workflow.qiyuesuo.seal.action;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.workflow.qiyuesuo.QYSSealInterface;
/*    */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ import weaver.workflow.qiyuesuo.util.QYSUtil;
/*    */ import weaver.workflow.request.RequestManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class QYSCreatePhysicalSealAction
/*    */   extends BaseBean
/*    */   implements Action
/*    */ {
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 23 */     int i = Util.getIntValue(paramRequestInfo.getRequestid(), 0);
/*    */     
/* 25 */     QYSLogUtil qYSLogUtil = new QYSLogUtil(getClass().getName());
/* 26 */     qYSLogUtil.info("start--requestid:" + i);
/*    */     
/* 28 */     RequestManager requestManager = paramRequestInfo.getRequestManager();
/*    */     
/* 30 */     QYSResponse qYSResponse = new QYSResponse((new QYSSealInterface(requestManager.getUser(), QYSLogType.ACTION)).createPhysicalSeal(i));
/* 31 */     if (0 != qYSResponse.getCode()) {
/* 32 */       String str = qYSResponse.getMessage();
/* 33 */       qYSLogUtil.error("创建物理印章失败！" + str);
/*    */       
/* 35 */       if (str.isEmpty()) {
/* 36 */         str = "创建物理印章失败！";
/*    */       }
/*    */       
/* 39 */       requestManager.setMessageid("126221");
/* 40 */       requestManager.setMessagecontent(QYSUtil.filteErrorMsgChar(str));
/*    */     } 
/*    */     
/* 43 */     qYSLogUtil.info("end--requestid:" + i);
/* 44 */     return requestManager.getMessageid().isEmpty() ? "1" : "0";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/seal/action/QYSCreatePhysicalSealAction.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */