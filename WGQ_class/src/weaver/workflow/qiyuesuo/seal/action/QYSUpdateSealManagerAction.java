/*    */ package weaver.workflow.qiyuesuo.seal.action;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.workflow.qiyuesuo.QYSSealInterface;
/*    */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ import weaver.workflow.request.RequestManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class QYSUpdateSealManagerAction
/*    */   extends BaseBean
/*    */   implements Action
/*    */ {
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 22 */     int i = Util.getIntValue(paramRequestInfo.getRequestid(), 0);
/*    */     
/* 24 */     QYSLogUtil qYSLogUtil = new QYSLogUtil(getClass().getName());
/* 25 */     qYSLogUtil.info("start--requestid:" + i);
/*    */     
/* 27 */     RequestManager requestManager = paramRequestInfo.getRequestManager();
/*    */     
/* 29 */     QYSResponse qYSResponse = new QYSResponse((new QYSSealInterface(requestManager.getUser(), QYSLogType.ACTION)).updateSealManager(i));
/* 30 */     if (0 != qYSResponse.getCode()) {
/* 31 */       String str = qYSResponse.getMessage();
/* 32 */       qYSLogUtil.error("修改印章管理员失败！" + str);
/*    */       
/* 34 */       if (str.isEmpty()) {
/* 35 */         str = "修改印章管理员失败！";
/*    */       }
/*    */       
/* 38 */       requestManager.setMessageid("126221");
/* 39 */       requestManager.setMessagecontent(str);
/*    */     } 
/*    */     
/* 42 */     qYSLogUtil.info("end--requestid:" + i);
/* 43 */     return requestManager.getMessageid().isEmpty() ? "1" : "0";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/seal/action/QYSUpdateSealManagerAction.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */