package weaver.workflow.request;

import weaver.general.BaseBean;
import weaver.hrm.User;

public class RequestDocImagefile extends BaseBean {
  public void addWFImagefileToDoc(int paramInt1, int paramInt2, String paramString1, User paramUser, String paramString2) throws Exception {
    // Byte code:
    //   0: iload_1
    //   1: iflt -> 29
    //   4: iload_2
    //   5: ifle -> 29
    //   8: aload_3
    //   9: ifnull -> 29
    //   12: aload_3
    //   13: invokevirtual trim : ()Ljava/lang/String;
    //   16: ldc ''
    //   18: invokevirtual equals : (Ljava/lang/Object;)Z
    //   21: ifne -> 29
    //   24: aload #4
    //   26: ifnonnull -> 30
    //   29: return
    //   30: new weaver/conn/RecordSet
    //   33: dup
    //   34: invokespecial <init> : ()V
    //   37: astore #6
    //   39: new weaver/conn/RecordSet
    //   42: dup
    //   43: invokespecial <init> : ()V
    //   46: astore #7
    //   48: new weaver/workflow/request/RequestDoc
    //   51: dup
    //   52: invokespecial <init> : ()V
    //   55: astore #8
    //   57: aload #8
    //   59: new java/lang/StringBuilder
    //   62: dup
    //   63: invokespecial <init> : ()V
    //   66: ldc ''
    //   68: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   71: iload_2
    //   72: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   75: invokevirtual toString : ()Ljava/lang/String;
    //   78: invokevirtual getDocFiled : (Ljava/lang/String;)Ljava/util/ArrayList;
    //   81: astore #9
    //   83: aload #9
    //   85: ifnull -> 96
    //   88: aload #9
    //   90: invokevirtual size : ()I
    //   93: ifne -> 97
    //   96: return
    //   97: aload #9
    //   99: iconst_1
    //   100: invokevirtual get : (I)Ljava/lang/Object;
    //   103: checkcast java/lang/String
    //   106: iconst_m1
    //   107: invokestatic getIntValue : (Ljava/lang/String;I)I
    //   110: istore #10
    //   112: aload #9
    //   114: bipush #8
    //   116: invokevirtual get : (I)Ljava/lang/Object;
    //   119: checkcast java/lang/String
    //   122: iconst_0
    //   123: invokestatic getIntValue : (Ljava/lang/String;I)I
    //   126: istore #11
    //   128: aload #9
    //   130: bipush #9
    //   132: invokevirtual get : (I)Ljava/lang/Object;
    //   135: invokestatic null2String : (Ljava/lang/Object;)Ljava/lang/String;
    //   138: astore #12
    //   140: aload #9
    //   142: bipush #10
    //   144: invokevirtual get : (I)Ljava/lang/Object;
    //   147: invokestatic null2String : (Ljava/lang/Object;)Ljava/lang/String;
    //   150: astore #13
    //   152: iload #10
    //   154: ifle -> 163
    //   157: iload #11
    //   159: iconst_1
    //   160: if_icmpeq -> 164
    //   163: return
    //   164: aconst_null
    //   165: astore #14
    //   167: ldc ''
    //   169: astore #15
    //   171: iconst_0
    //   172: istore #16
    //   174: new weaver/workflow/workflow/WFManager
    //   177: dup
    //   178: invokespecial <init> : ()V
    //   181: astore #17
    //   183: aload #17
    //   185: invokevirtual reset : ()V
    //   188: aload #17
    //   190: iload_2
    //   191: invokevirtual setWfid : (I)V
    //   194: aload #17
    //   196: invokevirtual getWfInfo : ()V
    //   199: aload #17
    //   201: invokevirtual getIsBill : ()Ljava/lang/String;
    //   204: astore #15
    //   206: aload #17
    //   208: invokevirtual getFormid : ()I
    //   211: istore #16
    //   213: aload #15
    //   215: ifnull -> 231
    //   218: ldc ''
    //   220: aload #15
    //   222: invokevirtual trim : ()Ljava/lang/String;
    //   225: invokevirtual equals : (Ljava/lang/Object;)Z
    //   228: ifeq -> 232
    //   231: return
    //   232: aconst_null
    //   233: astore #18
    //   235: aload #15
    //   237: ldc '1'
    //   239: invokevirtual equals : (Ljava/lang/Object;)Z
    //   242: ifeq -> 270
    //   245: new java/lang/StringBuilder
    //   248: dup
    //   249: invokespecial <init> : ()V
    //   252: ldc 'select a.fieldname from workflow_billfield a where a.id='
    //   254: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   257: iload #10
    //   259: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   262: invokevirtual toString : ()Ljava/lang/String;
    //   265: astore #18
    //   267: goto -> 292
    //   270: new java/lang/StringBuilder
    //   273: dup
    //   274: invokespecial <init> : ()V
    //   277: ldc 'select a.fieldname from workflow_formdict a where a.id='
    //   279: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   282: iload #10
    //   284: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   287: invokevirtual toString : ()Ljava/lang/String;
    //   290: astore #18
    //   292: aload #6
    //   294: aload #18
    //   296: invokevirtual executeSql : (Ljava/lang/String;)Z
    //   299: pop
    //   300: aload #6
    //   302: invokevirtual next : ()Z
    //   305: ifeq -> 319
    //   308: aload #6
    //   310: iconst_1
    //   311: invokevirtual getString : (I)Ljava/lang/String;
    //   314: invokestatic null2String : (Ljava/lang/String;)Ljava/lang/String;
    //   317: astore #14
    //   319: aload #14
    //   321: ifnull -> 337
    //   324: aload #14
    //   326: invokevirtual trim : ()Ljava/lang/String;
    //   329: ldc ''
    //   331: invokevirtual equals : (Ljava/lang/Object;)Z
    //   334: ifeq -> 338
    //   337: return
    //   338: ldc 'workflow_form'
    //   340: astore #19
    //   342: aload #15
    //   344: ldc '1'
    //   346: invokevirtual equals : (Ljava/lang/Object;)Z
    //   349: ifeq -> 402
    //   352: aload #6
    //   354: new java/lang/StringBuilder
    //   357: dup
    //   358: invokespecial <init> : ()V
    //   361: ldc 'select tablename from workflow_bill where id = '
    //   363: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   366: iload #16
    //   368: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   371: invokevirtual toString : ()Ljava/lang/String;
    //   374: invokevirtual executeSql : (Ljava/lang/String;)Z
    //   377: pop
    //   378: aload #6
    //   380: invokevirtual next : ()Z
    //   383: ifeq -> 401
    //   386: aload #6
    //   388: ldc 'tablename'
    //   390: invokevirtual getString : (Ljava/lang/String;)Ljava/lang/String;
    //   393: invokestatic null2String : (Ljava/lang/String;)Ljava/lang/String;
    //   396: astore #19
    //   398: goto -> 402
    //   401: return
    //   402: ldc ''
    //   404: aload #19
    //   406: invokevirtual trim : ()Ljava/lang/String;
    //   409: invokevirtual equals : (Ljava/lang/Object;)Z
    //   412: ifeq -> 426
    //   415: ldc 'null'
    //   417: aload #19
    //   419: invokevirtual equalsIgnoreCase : (Ljava/lang/String;)Z
    //   422: ifeq -> 426
    //   425: return
    //   426: iconst_0
    //   427: istore #20
    //   429: new java/lang/StringBuilder
    //   432: dup
    //   433: invokespecial <init> : ()V
    //   436: ldc 'select '
    //   438: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   441: aload #14
    //   443: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   446: ldc ' from '
    //   448: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   451: aload #19
    //   453: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   456: ldc ' where requestId='
    //   458: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   461: iload_1
    //   462: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   465: invokevirtual toString : ()Ljava/lang/String;
    //   468: astore #18
    //   470: aload #6
    //   472: aload #18
    //   474: invokevirtual executeSql : (Ljava/lang/String;)Z
    //   477: pop
    //   478: aload #6
    //   480: invokevirtual next : ()Z
    //   483: ifeq -> 498
    //   486: aload #6
    //   488: iconst_1
    //   489: invokevirtual getString : (I)Ljava/lang/String;
    //   492: iconst_m1
    //   493: invokestatic getIntValue : (Ljava/lang/String;I)I
    //   496: istore #20
    //   498: iload #20
    //   500: ifle -> 558
    //   503: new java/lang/StringBuilder
    //   506: dup
    //   507: invokespecial <init> : ()V
    //   510: ldc ','
    //   512: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   515: aload_3
    //   516: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   519: ldc ','
    //   521: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   524: invokevirtual toString : ()Ljava/lang/String;
    //   527: new java/lang/StringBuilder
    //   530: dup
    //   531: invokespecial <init> : ()V
    //   534: ldc ','
    //   536: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   539: iload #20
    //   541: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   544: ldc ','
    //   546: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   549: invokevirtual toString : ()Ljava/lang/String;
    //   552: invokevirtual indexOf : (Ljava/lang/String;)I
    //   555: ifge -> 559
    //   558: return
    //   559: ldc ''
    //   561: astore #21
    //   563: aload #15
    //   565: ldc '1'
    //   567: invokevirtual equals : (Ljava/lang/Object;)Z
    //   570: ifeq -> 645
    //   573: new java/lang/StringBuilder
    //   576: dup
    //   577: invokespecial <init> : ()V
    //   580: ldc 'select * from workflow_billfield b where (b.viewtype is null or b.viewtype <> 1) and b.fieldhtmltype=6 and b.billid='
    //   582: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   585: iload #16
    //   587: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   590: invokevirtual toString : ()Ljava/lang/String;
    //   593: astore #21
    //   595: ldc ''
    //   597: aload #12
    //   599: invokevirtual equals : (Ljava/lang/Object;)Z
    //   602: ifne -> 714
    //   605: aload #12
    //   607: ifnull -> 714
    //   610: new java/lang/StringBuilder
    //   613: dup
    //   614: invokespecial <init> : ()V
    //   617: aload #21
    //   619: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   622: ldc '  and id in('
    //   624: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   627: aload #12
    //   629: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   632: ldc ')'
    //   634: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   637: invokevirtual toString : ()Ljava/lang/String;
    //   640: astore #21
    //   642: goto -> 714
    //   645: new java/lang/StringBuilder
    //   648: dup
    //   649: invokespecial <init> : ()V
    //   652: ldc 'select * from workflow_formfield f, workflow_formdict d where f.fieldid=d.id and (f.isdetail <> '1' or f.isdetail is null) and d.fieldhtmltype=6 and f.formid='
    //   654: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   657: iload #16
    //   659: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   662: invokevirtual toString : ()Ljava/lang/String;
    //   665: astore #21
    //   667: ldc ''
    //   669: aload #12
    //   671: invokevirtual equals : (Ljava/lang/Object;)Z
    //   674: ifne -> 714
    //   677: aload #12
    //   679: ifnull -> 714
    //   682: new java/lang/StringBuilder
    //   685: dup
    //   686: invokespecial <init> : ()V
    //   689: aload #21
    //   691: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   694: ldc '  and fieldid in('
    //   696: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   699: aload #12
    //   701: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   704: ldc ')'
    //   706: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   709: invokevirtual toString : ()Ljava/lang/String;
    //   712: astore #21
    //   714: aload #6
    //   716: aload #21
    //   718: invokevirtual execute : (Ljava/lang/String;)Z
    //   721: pop
    //   722: new java/util/ArrayList
    //   725: dup
    //   726: invokespecial <init> : ()V
    //   729: astore #22
    //   731: aload #6
    //   733: invokevirtual next : ()Z
    //   736: ifeq -> 785
    //   739: aload #6
    //   741: ldc 'fieldname'
    //   743: invokevirtual getString : (Ljava/lang/String;)Ljava/lang/String;
    //   746: invokestatic null2String : (Ljava/lang/String;)Ljava/lang/String;
    //   749: astore #23
    //   751: aload #23
    //   753: invokevirtual trim : ()Ljava/lang/String;
    //   756: ldc ''
    //   758: invokevirtual equals : (Ljava/lang/Object;)Z
    //   761: ifne -> 782
    //   764: ldc 'null'
    //   766: aload #23
    //   768: invokevirtual equalsIgnoreCase : (Ljava/lang/String;)Z
    //   771: ifne -> 782
    //   774: aload #22
    //   776: aload #23
    //   778: invokevirtual add : (Ljava/lang/Object;)Z
    //   781: pop
    //   782: goto -> 731
    //   785: aload #22
    //   787: invokevirtual size : ()I
    //   790: istore #23
    //   792: iload #23
    //   794: iconst_1
    //   795: if_icmpge -> 799
    //   798: return
    //   799: new java/lang/StringBuilder
    //   802: dup
    //   803: invokespecial <init> : ()V
    //   806: astore #24
    //   808: new weaver/conn/RecordSet
    //   811: dup
    //   812: invokespecial <init> : ()V
    //   815: astore #25
    //   817: aload #6
    //   819: new java/lang/StringBuilder
    //   822: dup
    //   823: invokespecial <init> : ()V
    //   826: ldc 'select * from '
    //   828: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   831: aload #19
    //   833: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   836: ldc ' where requestid='
    //   838: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   841: iload_1
    //   842: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   845: invokevirtual toString : ()Ljava/lang/String;
    //   848: invokevirtual execute : (Ljava/lang/String;)Z
    //   851: pop
    //   852: aload #6
    //   854: invokevirtual next : ()Z
    //   857: ifeq -> 1620
    //   860: iconst_0
    //   861: istore #26
    //   863: iload #26
    //   865: iload #23
    //   867: if_icmpge -> 1584
    //   870: aload #22
    //   872: iload #26
    //   874: invokevirtual get : (I)Ljava/lang/Object;
    //   877: checkcast java/lang/String
    //   880: astore #27
    //   882: aload #6
    //   884: aload #27
    //   886: invokevirtual getString : (Ljava/lang/String;)Ljava/lang/String;
    //   889: invokestatic null2String : (Ljava/lang/String;)Ljava/lang/String;
    //   892: astore #28
    //   894: ldc ''
    //   896: aload #28
    //   898: invokevirtual trim : ()Ljava/lang/String;
    //   901: invokevirtual equals : (Ljava/lang/Object;)Z
    //   904: ifne -> 1578
    //   907: ldc 'null'
    //   909: aload #28
    //   911: invokevirtual equalsIgnoreCase : (Ljava/lang/String;)Z
    //   914: ifeq -> 920
    //   917: goto -> 1578
    //   920: aload #28
    //   922: ldc ','
    //   924: invokestatic TokenizerString : (Ljava/lang/String;Ljava/lang/String;)Ljava/util/ArrayList;
    //   927: astore #29
    //   929: iconst_0
    //   930: istore #30
    //   932: iload #30
    //   934: aload #29
    //   936: invokeinterface size : ()I
    //   941: if_icmpge -> 1523
    //   944: iconst_0
    //   945: istore #31
    //   947: ldc ''
    //   949: astore #32
    //   951: aload #29
    //   953: iload #30
    //   955: invokeinterface get : (I)Ljava/lang/Object;
    //   960: checkcast java/lang/String
    //   963: astore #32
    //   965: iconst_0
    //   966: istore #33
    //   968: aload #7
    //   970: new java/lang/StringBuilder
    //   973: dup
    //   974: invokespecial <init> : ()V
    //   977: ldc 'select max(a.id) as maxid from DocDetail a where a.doceditionid>0 and  exists(select 1 from DocDetail  where doceditionid=a.doceditionid and id='
    //   979: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   982: aload #32
    //   984: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   987: ldc ') '
    //   989: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   992: invokevirtual toString : ()Ljava/lang/String;
    //   995: invokevirtual executeSql : (Ljava/lang/String;)Z
    //   998: pop
    //   999: aload #7
    //   1001: invokevirtual next : ()Z
    //   1004: ifeq -> 1020
    //   1007: aload #7
    //   1009: ldc 'maxid'
    //   1011: invokevirtual getString : (Ljava/lang/String;)Ljava/lang/String;
    //   1014: iconst_0
    //   1015: invokestatic getIntValue : (Ljava/lang/String;I)I
    //   1018: istore #33
    //   1020: iload #33
    //   1022: aload #32
    //   1024: invokestatic parseInt : (Ljava/lang/String;)I
    //   1027: if_icmple -> 1052
    //   1030: new java/lang/StringBuilder
    //   1033: dup
    //   1034: invokespecial <init> : ()V
    //   1037: iload #33
    //   1039: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   1042: ldc ''
    //   1044: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1047: invokevirtual toString : ()Ljava/lang/String;
    //   1050: astore #32
    //   1052: iload #20
    //   1054: aload #32
    //   1056: iconst_m1
    //   1057: invokestatic getIntValue : (Ljava/lang/String;I)I
    //   1060: if_icmpne -> 1066
    //   1063: goto -> 1517
    //   1066: aload #24
    //   1068: invokevirtual length : ()I
    //   1071: ifle -> 1103
    //   1074: aload #24
    //   1076: new java/lang/StringBuilder
    //   1079: dup
    //   1080: invokespecial <init> : ()V
    //   1083: ldc ','
    //   1085: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1088: aload #32
    //   1090: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1093: invokevirtual toString : ()Ljava/lang/String;
    //   1096: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1099: pop
    //   1100: goto -> 1111
    //   1103: aload #24
    //   1105: aload #32
    //   1107: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1110: pop
    //   1111: aload #25
    //   1113: ldc 'select 1 from wfexttoodocext where docid=? and requestid=? and wf_extraid=?'
    //   1115: iconst_3
    //   1116: anewarray java/lang/Object
    //   1119: dup
    //   1120: iconst_0
    //   1121: iload #20
    //   1123: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   1126: aastore
    //   1127: dup
    //   1128: iconst_1
    //   1129: iload_1
    //   1130: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   1133: aastore
    //   1134: dup
    //   1135: iconst_2
    //   1136: aload #32
    //   1138: aastore
    //   1139: invokevirtual executeQuery : (Ljava/lang/String;[Ljava/lang/Object;)Z
    //   1142: pop
    //   1143: aload #25
    //   1145: invokevirtual next : ()Z
    //   1148: ifeq -> 1154
    //   1151: goto -> 1517
    //   1154: new weaver/docs/docs/DocManager
    //   1157: dup
    //   1158: invokespecial <init> : ()V
    //   1161: astore #34
    //   1163: aload #34
    //   1165: invokevirtual resetParameter : ()V
    //   1168: aload #34
    //   1170: aload #32
    //   1172: invokestatic parseInt : (Ljava/lang/String;)I
    //   1175: invokevirtual setId : (I)V
    //   1178: aload #34
    //   1180: invokevirtual getDocInfoById : ()V
    //   1183: aload #34
    //   1185: invokevirtual copyDocNew : ()V
    //   1188: aload #34
    //   1190: invokevirtual getId : ()I
    //   1193: istore #35
    //   1195: iconst_m1
    //   1196: istore #36
    //   1198: aload #25
    //   1200: ldc 'select id from docimagefile where docid=?'
    //   1202: iconst_1
    //   1203: anewarray java/lang/Object
    //   1206: dup
    //   1207: iconst_0
    //   1208: iload #35
    //   1210: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   1213: aastore
    //   1214: invokevirtual executeQuery : (Ljava/lang/String;[Ljava/lang/Object;)Z
    //   1217: pop
    //   1218: aload #25
    //   1220: invokevirtual next : ()Z
    //   1223: ifeq -> 1234
    //   1226: aload #25
    //   1228: iconst_1
    //   1229: invokevirtual getInt : (I)I
    //   1232: istore #36
    //   1234: iload #36
    //   1236: ifge -> 1249
    //   1239: aload #25
    //   1241: ldc 'RequestDocImagefile--docimagefileid<0'
    //   1243: invokevirtual writeLog : (Ljava/lang/Object;)V
    //   1246: goto -> 1517
    //   1249: aload #25
    //   1251: new java/lang/StringBuilder
    //   1254: dup
    //   1255: invokespecial <init> : ()V
    //   1258: ldc 'update docimagefile set docid='
    //   1260: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1263: iload #20
    //   1265: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   1268: ldc ', isextfile='1' where docid='
    //   1270: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1273: iload #35
    //   1275: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   1278: invokevirtual toString : ()Ljava/lang/String;
    //   1281: iconst_0
    //   1282: anewarray java/lang/Object
    //   1285: invokevirtual executeUpdate : (Ljava/lang/String;[Ljava/lang/Object;)Z
    //   1288: istore #37
    //   1290: iload #37
    //   1292: ifeq -> 1315
    //   1295: aload #25
    //   1297: ldc 'delete from docdetail where id = ? '
    //   1299: iconst_1
    //   1300: anewarray java/lang/Object
    //   1303: dup
    //   1304: iconst_0
    //   1305: iload #35
    //   1307: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   1310: aastore
    //   1311: invokevirtual executeUpdate : (Ljava/lang/String;[Ljava/lang/Object;)Z
    //   1314: pop
    //   1315: aload #25
    //   1317: ldc 'insert into wfexttoodocext(requestid,docid,wf_extraid,docimagefileid) values(?,?,?,?)'
    //   1319: iconst_4
    //   1320: anewarray java/lang/Object
    //   1323: dup
    //   1324: iconst_0
    //   1325: iload_1
    //   1326: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   1329: aastore
    //   1330: dup
    //   1331: iconst_1
    //   1332: iload #20
    //   1334: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   1337: aastore
    //   1338: dup
    //   1339: iconst_2
    //   1340: aload #32
    //   1342: aastore
    //   1343: dup
    //   1344: iconst_3
    //   1345: iload #36
    //   1347: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   1350: aastore
    //   1351: invokevirtual executeUpdate : (Ljava/lang/String;[Ljava/lang/Object;)Z
    //   1354: pop
    //   1355: iconst_1
    //   1356: istore #31
    //   1358: goto -> 1366
    //   1361: astore #33
    //   1363: iconst_0
    //   1364: istore #31
    //   1366: iload #31
    //   1368: iconst_1
    //   1369: if_icmpne -> 1517
    //   1372: aload #25
    //   1374: new java/lang/StringBuilder
    //   1377: dup
    //   1378: invokespecial <init> : ()V
    //   1381: ldc 'select * from docdetail where id='
    //   1383: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1386: aload #32
    //   1388: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1391: invokevirtual toString : ()Ljava/lang/String;
    //   1394: invokevirtual execute : (Ljava/lang/String;)Z
    //   1397: pop
    //   1398: aload #25
    //   1400: invokevirtual next : ()Z
    //   1403: pop
    //   1404: aload #25
    //   1406: ldc 'docsubject'
    //   1408: invokevirtual getString : (Ljava/lang/String;)Ljava/lang/String;
    //   1411: astore #33
    //   1413: aload #25
    //   1415: ldc 'doccreaterid'
    //   1417: invokevirtual getString : (Ljava/lang/String;)Ljava/lang/String;
    //   1420: astore #34
    //   1422: aload #25
    //   1424: ldc 'docCreaterType'
    //   1426: invokevirtual getString : (Ljava/lang/String;)Ljava/lang/String;
    //   1429: astore #35
    //   1431: new weaver/docs/docs/DocManager
    //   1434: dup
    //   1435: invokespecial <init> : ()V
    //   1438: astore #36
    //   1440: aload #36
    //   1442: aload #32
    //   1444: invokestatic getIntValue : (Ljava/lang/String;)I
    //   1447: invokevirtual setId : (I)V
    //   1450: aload #36
    //   1452: aload #33
    //   1454: invokevirtual setDocsubject : (Ljava/lang/String;)V
    //   1457: aload #36
    //   1459: aload #4
    //   1461: invokevirtual getUID : ()I
    //   1464: invokevirtual setUserid : (I)V
    //   1467: aload #36
    //   1469: aload #4
    //   1471: invokevirtual getLogintype : ()Ljava/lang/String;
    //   1474: invokevirtual setUsertype : (Ljava/lang/String;)V
    //   1477: aload #36
    //   1479: aload #5
    //   1481: invokevirtual setClientAddress : (Ljava/lang/String;)V
    //   1484: aload #36
    //   1486: aload #34
    //   1488: iconst_0
    //   1489: invokestatic getIntValue : (Ljava/lang/String;I)I
    //   1492: invokevirtual setDoccreaterid : (I)V
    //   1495: aload #36
    //   1497: aload #35
    //   1499: invokevirtual setDocCreaterType : (Ljava/lang/String;)V
    //   1502: aload #13
    //   1504: ldc '1'
    //   1506: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1509: ifne -> 1517
    //   1512: aload #36
    //   1514: invokevirtual DeleteDocInfo : ()V
    //   1517: iinc #30, 1
    //   1520: goto -> 932
    //   1523: aload #13
    //   1525: ldc '1'
    //   1527: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1530: ifne -> 1578
    //   1533: aload #25
    //   1535: new java/lang/StringBuilder
    //   1538: dup
    //   1539: invokespecial <init> : ()V
    //   1542: ldc 'update '
    //   1544: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1547: aload #19
    //   1549: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1552: ldc ' set '
    //   1554: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1557: aload #27
    //   1559: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1562: ldc ' = '-2' where requestid='
    //   1564: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1567: iload_1
    //   1568: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   1571: invokevirtual toString : ()Ljava/lang/String;
    //   1574: invokevirtual execute : (Ljava/lang/String;)Z
    //   1577: pop
    //   1578: iinc #26, 1
    //   1581: goto -> 863
    //   1584: aload #25
    //   1586: new java/lang/StringBuilder
    //   1589: dup
    //   1590: invokespecial <init> : ()V
    //   1593: ldc ' update docdetail set accessorycount = (select count(distinct id) from DocImageFile where isextfile = '1' and docid = '
    //   1595: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1598: iload #20
    //   1600: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   1603: ldc ' and docfiletype <> '1' ) where id = '
    //   1605: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1608: iload #20
    //   1610: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   1613: invokevirtual toString : ()Ljava/lang/String;
    //   1616: invokevirtual executeSql : (Ljava/lang/String;)Z
    //   1619: pop
    //   1620: aload #24
    //   1622: invokevirtual length : ()I
    //   1625: ifle -> 1811
    //   1628: aload #13
    //   1630: ldc '1'
    //   1632: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1635: ifeq -> 1811
    //   1638: aload #24
    //   1640: ldc '-2'
    //   1642: invokevirtual indexOf : (Ljava/lang/String;)I
    //   1645: iconst_m1
    //   1646: if_icmpne -> 1811
    //   1649: aload #24
    //   1651: invokevirtual toString : ()Ljava/lang/String;
    //   1654: invokestatic StringToSqlPara : (Ljava/lang/String;)Ljava/util/Map;
    //   1657: astore #26
    //   1659: aload #25
    //   1661: new java/lang/StringBuilder
    //   1664: dup
    //   1665: invokespecial <init> : ()V
    //   1668: ldc 'select docimagefileid from wfexttoodocext where requestid=? and docid=? and wf_extraid not in ('
    //   1670: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1673: aload #26
    //   1675: ldc 'paras'
    //   1677: invokeinterface get : (Ljava/lang/Object;)Ljava/lang/Object;
    //   1682: invokevirtual append : (Ljava/lang/Object;)Ljava/lang/StringBuilder;
    //   1685: ldc ') '
    //   1687: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   1690: invokevirtual toString : ()Ljava/lang/String;
    //   1693: iconst_3
    //   1694: anewarray java/lang/Object
    //   1697: dup
    //   1698: iconst_0
    //   1699: iload_1
    //   1700: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   1703: aastore
    //   1704: dup
    //   1705: iconst_1
    //   1706: iload #20
    //   1708: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   1711: aastore
    //   1712: dup
    //   1713: iconst_2
    //   1714: aload #26
    //   1716: ldc 'values'
    //   1718: invokeinterface get : (Ljava/lang/Object;)Ljava/lang/Object;
    //   1723: aastore
    //   1724: invokevirtual executeQuery : (Ljava/lang/String;[Ljava/lang/Object;)Z
    //   1727: pop
    //   1728: new weaver/conn/RecordSet
    //   1731: dup
    //   1732: invokespecial <init> : ()V
    //   1735: astore #27
    //   1737: aload #25
    //   1739: invokevirtual next : ()Z
    //   1742: ifeq -> 1811
    //   1745: aload #27
    //   1747: ldc 'delete from docimagefile where id =?'
    //   1749: iconst_1
    //   1750: anewarray java/lang/Object
    //   1753: dup
    //   1754: iconst_0
    //   1755: aload #25
    //   1757: iconst_1
    //   1758: invokevirtual getInt : (I)I
    //   1761: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   1764: aastore
    //   1765: invokevirtual executeUpdate : (Ljava/lang/String;[Ljava/lang/Object;)Z
    //   1768: pop
    //   1769: aload #27
    //   1771: ldc 'delete from wfexttoodocext where requestid=? and docid=? and docimagefileid =?'
    //   1773: iconst_3
    //   1774: anewarray java/lang/Object
    //   1777: dup
    //   1778: iconst_0
    //   1779: iload_1
    //   1780: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   1783: aastore
    //   1784: dup
    //   1785: iconst_1
    //   1786: iload #20
    //   1788: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   1791: aastore
    //   1792: dup
    //   1793: iconst_2
    //   1794: aload #25
    //   1796: iconst_1
    //   1797: invokevirtual getInt : (I)I
    //   1800: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   1803: aastore
    //   1804: invokevirtual executeUpdate : (Ljava/lang/String;[Ljava/lang/Object;)Z
    //   1807: pop
    //   1808: goto -> 1737
    //   1811: return
    // Line number table:
    //   Java source line number -> byte code offset
    //   #24	-> 0
    //   #25	-> 29
    //   #27	-> 30
    //   #28	-> 39
    //   #31	-> 48
    //   #32	-> 57
    //   #33	-> 83
    //   #34	-> 96
    //   #36	-> 97
    //   #37	-> 112
    //   #38	-> 128
    //   #39	-> 140
    //   #41	-> 152
    //   #42	-> 163
    //   #48	-> 164
    //   #50	-> 167
    //   #51	-> 171
    //   #52	-> 174
    //   #53	-> 183
    //   #54	-> 188
    //   #55	-> 194
    //   #56	-> 199
    //   #57	-> 206
    //   #59	-> 213
    //   #60	-> 231
    //   #63	-> 232
    //   #64	-> 235
    //   #65	-> 245
    //   #67	-> 270
    //   #69	-> 292
    //   #70	-> 300
    //   #71	-> 308
    //   #73	-> 319
    //   #74	-> 337
    //   #77	-> 338
    //   #78	-> 342
    //   #79	-> 352
    //   #80	-> 378
    //   #81	-> 386
    //   #83	-> 401
    //   #86	-> 402
    //   #87	-> 425
    //   #91	-> 426
    //   #94	-> 429
    //   #95	-> 470
    //   #96	-> 478
    //   #97	-> 486
    //   #100	-> 498
    //   #101	-> 558
    //   #106	-> 559
    //   #107	-> 563
    //   #108	-> 573
    //   #109	-> 595
    //   #110	-> 610
    //   #113	-> 645
    //   #114	-> 667
    //   #115	-> 682
    //   #118	-> 714
    //   #119	-> 722
    //   #120	-> 731
    //   #121	-> 739
    //   #122	-> 751
    //   #123	-> 774
    //   #125	-> 782
    //   #126	-> 785
    //   #127	-> 792
    //   #128	-> 798
    //   #131	-> 799
    //   #132	-> 808
    //   #133	-> 817
    //   #134	-> 852
    //   #135	-> 860
    //   #136	-> 870
    //   #137	-> 882
    //   #138	-> 894
    //   #139	-> 917
    //   #141	-> 920
    //   #142	-> 929
    //   #143	-> 944
    //   #144	-> 947
    //   #146	-> 951
    //   #147	-> 965
    //   #148	-> 968
    //   #149	-> 999
    //   #150	-> 1007
    //   #152	-> 1020
    //   #153	-> 1030
    //   #155	-> 1052
    //   #156	-> 1063
    //   #160	-> 1066
    //   #161	-> 1074
    //   #163	-> 1103
    //   #166	-> 1111
    //   #167	-> 1143
    //   #168	-> 1151
    //   #171	-> 1154
    //   #172	-> 1163
    //   #173	-> 1168
    //   #174	-> 1178
    //   #175	-> 1183
    //   #176	-> 1188
    //   #178	-> 1195
    //   #179	-> 1198
    //   #180	-> 1218
    //   #181	-> 1226
    //   #183	-> 1234
    //   #184	-> 1239
    //   #185	-> 1246
    //   #188	-> 1249
    //   #189	-> 1290
    //   #192	-> 1295
    //   #194	-> 1315
    //   #195	-> 1355
    //   #198	-> 1358
    //   #196	-> 1361
    //   #197	-> 1363
    //   #199	-> 1366
    //   #200	-> 1372
    //   #201	-> 1398
    //   #202	-> 1404
    //   #203	-> 1413
    //   #204	-> 1422
    //   #206	-> 1431
    //   #207	-> 1440
    //   #208	-> 1450
    //   #209	-> 1457
    //   #210	-> 1467
    //   #211	-> 1477
    //   #212	-> 1484
    //   #213	-> 1495
    //   #214	-> 1502
    //   #215	-> 1512
    //   #142	-> 1517
    //   #220	-> 1523
    //   #221	-> 1533
    //   #135	-> 1578
    //   #227	-> 1584
    //   #230	-> 1620
    //   #231	-> 1649
    //   #232	-> 1659
    //   #233	-> 1728
    //   #234	-> 1737
    //   #235	-> 1745
    //   #236	-> 1769
    //   #240	-> 1811
    // Exception table:
    //   from	to	target	type
    //   951	1063	1361	java/lang/Exception
    //   1066	1151	1361	java/lang/Exception
    //   1154	1246	1361	java/lang/Exception
    //   1249	1358	1361	java/lang/Exception
  }
  
  public static void main(String[] paramArrayOfString) {}
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestDocImagefile.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */