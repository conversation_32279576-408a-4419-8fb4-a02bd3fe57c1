/*     */ package weaver.admincenter.file;
/*     */ 
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.FileWriter;
/*     */ import java.io.InputStreamReader;
/*     */ import java.util.Iterator;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.DocumentHelper;
/*     */ import org.dom4j.Element;
/*     */ import org.dom4j.io.OutputFormat;
/*     */ import org.dom4j.io.SAXReader;
/*     */ import org.dom4j.io.XMLWriter;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.security.util.SecurityMethodUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FileUtil
/*     */ {
/*  37 */   private static String codeModel = null;
/*     */   
/*  39 */   public static String filePath = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean dispatch(String paramString1, String paramString2) {
/*  46 */     return dispatch(paramString1, paramString2, null, "80");
/*     */   }
/*     */   
/*     */   public static boolean dispatch(String paramString1, String paramString2, String paramString3) {
/*  50 */     return dispatch(paramString1, paramString2, paramString3, "80");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean dispatch(String paramString1, String paramString2, String paramString3, String paramString4) {
/*  56 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean deploy(String paramString1, String paramString2, String paramString3) {
/*     */     try {
/*  64 */       return copyFile(new File(paramString2 + "/" + paramString1 + ".class"), new File(paramString3 + "/" + paramString1 + ".class"));
/*  65 */     } catch (Exception exception) {
/*  66 */       exception.printStackTrace();
/*  67 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static boolean copyFile(File paramFile1, File paramFile2) {
/*  73 */     BufferedInputStream bufferedInputStream = null;
/*  74 */     BufferedOutputStream bufferedOutputStream = null;
/*     */     
/*     */     try {
/*  77 */       bufferedInputStream = new BufferedInputStream(new FileInputStream(paramFile1));
/*     */ 
/*     */       
/*  80 */       bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(paramFile2));
/*     */ 
/*     */       
/*  83 */       byte[] arrayOfByte = new byte[5120];
/*     */       int i;
/*  85 */       while ((i = bufferedInputStream.read(arrayOfByte)) != -1) {
/*  86 */         bufferedOutputStream.write(arrayOfByte, 0, i);
/*     */       }
/*     */       
/*  89 */       bufferedOutputStream.flush();
/*  90 */       return true;
/*  91 */     } catch (Exception exception) {
/*  92 */       return false;
/*     */     } finally {
/*     */       
/*  95 */       if (bufferedInputStream != null) {
/*     */         try {
/*  97 */           bufferedInputStream.close();
/*  98 */         } catch (Exception exception) {}
/*     */       }
/*     */ 
/*     */       
/* 102 */       if (bufferedOutputStream != null) {
/*     */         try {
/* 104 */           bufferedOutputStream.close();
/* 105 */         } catch (Exception exception) {}
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean createCode(String paramString) {
/* 118 */     if (codeModel == null) {
/* 119 */       boolean bool = loadBaseAction();
/* 120 */       if (!bool) return false;
/*     */     
/*     */     } 
/* 123 */     String str = codeModel.replaceAll("BaseAction", paramString);
/*     */     try {
/* 125 */       BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(new File(filePath + "/" + paramString + ".java")));
/* 126 */       bufferedWriter.write(str);
/* 127 */       bufferedWriter.close();
/* 128 */       return true;
/* 129 */     } catch (Exception exception) {
/* 130 */       exception.printStackTrace();
/* 131 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean addOrUpdateXml(String paramString1, String paramString2) {
/*     */     try {
/* 140 */       XMLWriter xMLWriter = null;
/* 141 */       SAXReader sAXReader = new SAXReader();
/* 142 */       new SecurityMethodUtil(); SecurityMethodUtil.setReaderFeature(sAXReader);
/* 143 */       File file = new File(paramString2 + "WEB-INF/service/action.xml");
/* 144 */       OutputFormat outputFormat = OutputFormat.createPrettyPrint();
/* 145 */       outputFormat.setEncoding(GCONST.XML_UTF8);
/* 146 */       if (file.exists()) {
/* 147 */         Document document = sAXReader.read(file);
/* 148 */         Element element1 = document.getRootElement();
/* 149 */         for (Iterator<Element> iterator = element1.elementIterator("service-point"); iterator.hasNext(); ) {
/* 150 */           Element element = iterator.next();
/* 151 */           if (element.attributeValue("id").equals(paramString1)) {
/* 152 */             return true;
/*     */           }
/*     */         } 
/*     */         
/* 156 */         Element element2 = element1.addElement("service-point");
/* 157 */         element2.addAttribute("id", paramString1);
/* 158 */         element2.addAttribute("interface", "weaver.interfaces.workflow.action.Action");
/* 159 */         Element element3 = element2.addElement("invoke-factory");
/* 160 */         Element element4 = element3.addElement("construct");
/* 161 */         element4.addAttribute("class", "weaver.interfaces.workflow.action." + paramString1);
/*     */         
/* 163 */         xMLWriter = new XMLWriter(new FileWriter(file), outputFormat);
/* 164 */         xMLWriter.write(document);
/* 165 */         xMLWriter.close();
/*     */       } else {
/*     */         
/* 168 */         Document document = DocumentHelper.createDocument();
/* 169 */         Element element1 = document.addElement("module");
/* 170 */         element1.addAttribute("id", "action");
/* 171 */         element1.addAttribute("version", "1.0.0");
/* 172 */         Element element2 = element1.addElement("service-point");
/* 173 */         element2.addAttribute("id", paramString1);
/* 174 */         element2.addAttribute("interface", "weaver.interfaces.workflow.action.Action");
/* 175 */         Element element3 = element2.addElement("invoke-factory");
/* 176 */         Element element4 = element3.addElement("construct");
/* 177 */         element4.addAttribute("class", "weaver.interfaces.workflow.action." + paramString1);
/*     */         
/* 179 */         xMLWriter = new XMLWriter(new FileWriter(file), outputFormat);
/* 180 */         xMLWriter.write(document);
/* 181 */         xMLWriter.close();
/*     */       } 
/* 183 */     } catch (Exception exception) {
/* 184 */       exception.printStackTrace();
/* 185 */       return false;
/*     */     } 
/* 187 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static boolean loadBaseAction() {
/*     */     try {
/* 196 */       String str = "UTF-8";
/* 197 */       codeModel = "";
/* 198 */       File file = new File(filePath + "/BaseAction.java");
/* 199 */       if (file.isFile() && file.exists()) {
/* 200 */         InputStreamReader inputStreamReader = new InputStreamReader(new FileInputStream(file), str);
/*     */         
/* 202 */         BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
/* 203 */         String str1 = null;
/* 204 */         while ((str1 = bufferedReader.readLine()) != null) {
/* 205 */           codeModel += "\n" + str1;
/*     */         }
/* 207 */         inputStreamReader.close();
/* 208 */         return true;
/*     */       } 
/*     */       
/* 211 */       return false;
/*     */     }
/* 213 */     catch (Exception exception) {
/*     */       
/* 215 */       exception.printStackTrace();
/* 216 */       return false;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/admincenter/file/FileUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */