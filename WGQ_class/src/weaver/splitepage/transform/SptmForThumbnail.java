/*     */ package weaver.splitepage.transform;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.IOException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.List;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.page.PageManager;
/*     */ import weaver.page.maint.layout.FileType;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SptmForThumbnail
/*     */   extends HttpServlet
/*     */ {
/*     */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  36 */     paramHttpServletRequest.setCharacterEncoding("UTF-8");
/*  37 */     PageManager pageManager = new PageManager();
/*  38 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("filerealpath"));
/*  39 */     str1 = PageManager.getRealPath(str1);
/*  40 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("isrealimg"));
/*  41 */     String str3 = str1.substring(str1.indexOf(".") + 1);
/*  42 */     String str4 = str1.substring(0, str1.indexOf("page"));
/*     */     
/*  44 */     FileInputStream fileInputStream = null;
/*  45 */     String str5 = "";
/*     */     try {
/*  47 */       str5 = "/" + Util.null2String(paramHttpServletRequest.getParameter("filerealpath")).replaceAll("\\\\", "/");
/*  48 */       if ("flv".equals(str3) || "swf".equals(str3) || "mp3".equals(str3) || "mp4".equals(str3)) {
/*  49 */         str1 = "/page/resource/Thumbnail/" + str3 + "_wev8.png";
/*  50 */         str5 = str1;
/*     */       } 
/*     */       
/*  53 */       paramHttpServletResponse.sendRedirect(GCONST.getContextPath() + str5 + "?time=" + (new Date()).getTime());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/*  65 */     catch (Exception exception) {
/*  66 */       exception.printStackTrace();
/*     */     } finally {
/*     */       try {
/*  69 */         if (fileInputStream != null) fileInputStream.close(); 
/*  70 */       } catch (Exception exception) {
/*  71 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static String GetFileContentType(String paramString) {
/*  78 */     String str1 = "image/jpeg;charset=UTF-8";
/*  79 */     String str2 = FileType.getImageFileType(new File(paramString));
/*  80 */     if ("jpg".equals(str2)) { str1 = "image/jpeg;charset=UTF-8"; }
/*  81 */     else if ("gif".equals(str2)) { str1 = "image/gif;charset=UTF-8"; }
/*  82 */     else if ("png".equals(str2)) { str1 = "image/png;charset=UTF-8"; }
/*  83 */      return str1;
/*     */   }
/*     */   
/*     */   public String getHref(String paramString1, String paramString2) {
/*  87 */     return "<div style=\"margin-top:2px;text-align:center;width:108px;\">" + paramString2 + "</div>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> getOperate(String paramString) {
/*  98 */     ArrayList<String> arrayList = new ArrayList();
/*  99 */     arrayList.add("true");
/* 100 */     arrayList.add("true");
/* 101 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> getPortalOperate(String paramString1, String paramString2) {
/* 111 */     ArrayList<String> arrayList = new ArrayList();
/* 112 */     arrayList.add("true");
/* 113 */     if ("sys".equals(paramString2)) {
/* 114 */       arrayList.add("false");
/*     */     } else {
/* 116 */       arrayList.add("true");
/*     */     } 
/* 118 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/splitepage/transform/SptmForThumbnail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */